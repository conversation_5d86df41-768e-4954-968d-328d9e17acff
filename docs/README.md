# SM - 智能代理服务

基于 Rust 和 Pingora 框架构建的企业级高性能反向代理服务，提供完整的 Web 管理界面和递归代理功能。

## 🎯 **最新更新 (v2024.12)**

### 🔧 **代码质量大幅提升**
- ✅ **Clippy深度优化**: 修复250+代码质量警告
- ✅ **安全问题修复**: 解决跨await持有锁等关键安全问题
- ✅ **性能优化**: 函数参数结构化，减少内存占用
- ✅ **代码风格统一**: 使用现代Rust语法和最佳实践

### 🏗️ **架构优化**
- ✅ **模块化重构**: 清晰的代码架构，易于维护
- ✅ **类型安全**: 强化类型系统，减少运行时错误
- ✅ **错误处理**: 统一的错误处理机制
- ✅ **异步优化**: 优化异步代码性能和安全性

## 🚀 快速开始

### 启动服务
```bash
# 编译项目
cargo build --release

# 使用部署脚本（Linux推荐）
sudo ./setup.sh
```

### 访问管理界面
- **管理界面和API**: http://127.0.0.1:1319
- **代理服务**: http://127.0.0.1:1911

### 系统要求
- Rust 1.70+
- Linux 服务器（推荐 Debian/Ubuntu）
- **MongoDB 7.0+**（必需，强制安装）
- 内存: 1GB+ (推荐2GB+)
- CPU: 1核+ (推荐2核+)

## 🏗️ 项目结构

```
SM/
├── src/                    # 主应用代码
│   ├── api/               # API路由层 (Web管理接口)
│   │   ├── auth.rs        # 认证授权API
│   │   ├── domain.rs      # 域名管理API
│   │   ├── recursive_management.rs # 递归代理管理API
│   │   └── status.rs      # 系统状态API
│   ├── db/                # 数据库层
│   │   ├── models/        # 数据模型
│   │   ├── repository/    # 数据仓库
│   │   └── factory.rs     # 数据库工厂
│   ├── recursive_proxy/   # 递归代理核心
│   │   ├── service.rs     # 递归代理服务
│   │   ├── context.rs     # 递归上下文
│   │   ├── cache.rs       # 智能缓存
│   │   └── proxy_client.rs # 代理客户端
│   ├── security/          # 安全模块
│   │   ├── manager.rs     # 安全管理器
│   │   ├── input_validation.rs # 输入验证
│   │   └── jwt_manager.rs # JWT管理
│   ├── services/          # 业务服务层
│   │   ├── database.rs    # 数据库服务
│   │   └── events.rs      # 事件服务
│   ├── domain_pool/       # 域名池管理
│   └── main.rs           # 应用入口 (Pingora+Web双服务)
├── crates/                # 核心库
│   ├── proxy-core/        # Pingora代理核心
│   ├── proxy-cache/       # 缓存服务
│   ├── proxy-config/      # 配置管理
│   └── proxy-types/       # 类型定义
├── frontend/              # Web管理界面
│   ├── css/              # 样式文件
│   ├── js/               # JavaScript文件
│   └── index.html        # 主页面
├── config/                # 配置文件
├── docs/                  # 完整文档
└── deploy/                # 部署脚本
```

## 🔧 主要功能

### 🏗️ **核心架构**
- **Pingora代理核心**: 基于字节跳动开源的高性能代理框架
- **双端口服务**: Web管理界面(1319) + Pingora代理(1911)
- **MongoDB集成**: 强制使用MongoDB，数据持久化存储
- **模块化设计**: 清晰的代码架构，易于维护和扩展

### 🎯 **递归代理功能**
- **智能递归**: 自动发现和处理上游域名
- **URL提取**: 从HTML/JSON响应中自动提取URL
- **域名配对**: 自动上游域名发现和配对
- **实时监控**: 递归会话状态跟踪和统计
- **内容处理**: 支持内容替换和重写功能

### 🛡️ **安全与性能**
- **安全加固**: 修复跨await持有锁等关键安全问题
- **输入验证**: 完整的安全输入验证和防护
- **JWT认证**: 安全的用户认证和授权机制
- **IP黑名单**: IP级别的访问控制
- **智能缓存**: 基于命中率的磁盘缓存系统

### 📊 **管理与监控**
- **Web管理界面**: 直观的前端管理界面
- **实时监控**: 系统状态和性能指标监控
- **事件日志**: 完整的操作和错误日志记录
- **健康检查**: 自动服务健康状态检测
- **热配置**: 无需重启的配置更新

## 📚 文档目录

- **[部署指南](deployment.md)** - 完整的部署说明和Linux环境配置
- **[API文档](api.md)** - 接口说明和使用示例
- **[安全配置](security.md)** - 安全设置指南
- **[故障排除](troubleshooting.md)** - 常见问题解决方案

## 🛠️ 开发

### 构建项目
```bash
# 开发构建
cargo build

# 发布构建
cargo build --release

# 运行测试
cargo test
```

### 环境变量配置
```bash
# JWT密钥（必需）
export JWT_SECRET="$(openssl rand -base64 64)"

# 管理员账户（可选）
export ADMIN_USERNAME="admin"
export ADMIN_PASSWORD_HASH="your-hash-here"

# 数据库连接（可选）
export MONGODB_URI="mongodb://localhost:27017/sm_db"
export REDIS_URI="redis://localhost:6379"
```

## 🚀 快速高可用部署

### ⚡ Linux服务器一键部署
```bash
# 1. 编译项目
cargo build --release

# 2. 一键高可用部署
sudo ./setup.sh

# 3. 验证部署
./test-api.sh

# 4. 验证重启后持续运行
sudo reboot
# 重启后运行：
./test-reboot.sh
```

### 🎯 高可用特性
- ✅ **后台服务运行** - systemd专业管理
- ✅ **故障自动重启** - 10秒内自动恢复
- ✅ **开机自动运行** - 系统重启后自动启动
- ✅ **服务健康监控** - 每2分钟自动检查
- ✅ **资源保护** - 内存2GB/CPU200%限制
- ✅ **安全沙箱** - systemd安全特性

### 🌐 访问地址
- **前端管理**: http://your-server:1319
- **代理服务**: http://your-server:1911
- **默认登录**: admin / admin888 (请立即修改密码)

详细部署说明请查看 [deployment.md](deployment.md)。

## 🔒 安全

- JWT 认证机制
- 输入验证和防护
- TLS/HTTPS 支持
- 审计日志记录
- 环境变量密钥管理

详细安全配置请查看 [security.md](security.md)。

## 📄 许可证

本项目采用 MIT 许可证。

---

**快速链接**: [部署指南](deployment.md) | [API文档](api.md) | [安全配置](security.md) | [故障排除](troubleshooting.md)