# API系统清理计划

## 🎯 目标
安全删除简化API系统（`/api/domains/*`），统一使用域名池API系统（`/api/domain-pool/*`）

## 📊 两套API系统对比

### 简化API系统 (`/api/domains/*`)
- **位置**: `src/api/mod.rs` 中的 `simple_*` 函数
- **实现**: 模拟实现，无真实数据库操作
- **功能**: 
  - ❌ `simple_list_domains()` - 返回模拟数据
  - ❌ `simple_create_domain()` - 无实际创建
  - ❌ `simple_batch_domains()` - 无实际批量操作
  - ❌ `simple_get_domain()` - 返回模拟数据
  - ❌ `simple_update_domain()` - 无实际更新
  - ❌ `simple_delete_domain()` - 无实际删除

### 域名池API系统 (`/api/domain-pool/*`)
- **位置**: `src/domain_pool/api.rs`
- **实现**: 完整实现，真实数据库操作
- **功能**:
  - ✅ `batch_add_domains()` - 真实批量添加
  - ✅ `get_pool_stats()` - 真实统计数据
  - ✅ `get_active_mappings()` - 真实映射数据
  - ✅ `trigger_auto_pairing()` - 自动配对功能
  - ✅ `get_all_domains()` - 获取所有域名
  - ✅ `get_upstream_domains()` - 获取上游域名
  - ✅ `get_downstream_domains()` - 获取下游域名

## 🔄 迁移状态

### ✅ 已完成
1. **域名池API挂载**: 已将域名池路由挂载到 `/api/domain-pool/*`
2. **前端API调用更新**: 已更新前端使用域名池API
3. **缺失功能补充**: 已添加获取域名列表的API
4. **下游域名功能**: 已修复下游域名批量添加功能

### 🔄 进行中
1. **后端编译**: 需要修复重复定义错误
2. **功能测试**: 需要验证域名添加功能正常工作

### ⏳ 待完成
1. **简化API删除**: 安全删除简化API函数
2. **路由清理**: 移除简化API路由
3. **依赖检查**: 确保没有其他模块依赖简化API
4. **测试验证**: 全面测试确保功能正常

## 🛡️ 安全删除步骤

### 第一阶段：依赖分析
1. **检查前端调用**: 确认所有前端都已切换到域名池API
2. **检查后端引用**: 搜索是否有其他模块调用简化API
3. **检查测试代码**: 确认测试代码不依赖简化API

### 第二阶段：逐步删除
1. **删除路由定义**: 移除 `/api/domains/*` 路由
2. **删除函数实现**: 移除 `simple_*` 函数
3. **清理导入**: 移除不再需要的导入

### 第三阶段：验证测试
1. **编译测试**: 确保代码能正常编译
2. **功能测试**: 验证域名管理功能正常
3. **回归测试**: 确保其他功能不受影响

## 📋 删除清单

### 需要删除的路由
```rust
// src/api/mod.rs 中的路由
.route("/api/domains", get(simple_list_domains).post(simple_create_domain))
.route("/api/domains/:id", get(simple_get_domain).put(simple_update_domain).delete(simple_delete_domain))
.route("/api/domains/batch", post(simple_batch_domains))
```

### 需要删除的函数
```rust
// src/api/mod.rs 中的函数
- simple_list_domains()
- simple_create_domain()
- simple_get_domain()
- simple_update_domain()
- simple_delete_domain()
- simple_batch_domains()
```

## ⚠️ 风险评估

### 低风险
- 简化API只是模拟实现，删除不会影响数据
- 域名池API功能更完整，是更好的替代方案

### 中等风险
- 需要确保前端完全切换到新API
- 需要确保没有遗漏的API调用

### 高风险
- 如果有其他模块依赖简化API，删除可能导致编译错误
- 需要仔细检查所有依赖关系

## 🧪 测试计划

### 功能测试
1. **域名添加**: 测试上游和下游域名批量添加
2. **域名列表**: 测试域名列表获取和显示
3. **域名删除**: 测试域名删除功能
4. **统计信息**: 测试统计数据获取

### 集成测试
1. **前端集成**: 测试前端与新API的集成
2. **认证测试**: 测试API认证功能
3. **错误处理**: 测试错误情况的处理

## 📝 执行记录

### 2025-07-01
- ✅ 挂载域名池API到主路由
- ✅ 添加获取域名列表的API
- ✅ 修复下游域名批量添加功能
- ✅ 更新前端API调用
- 🔄 修复后端编译错误（进行中）

### 下一步
1. 修复重复定义错误
2. 测试域名添加功能
3. 执行安全删除计划
