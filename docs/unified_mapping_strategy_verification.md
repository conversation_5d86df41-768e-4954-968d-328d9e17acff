# 统一映射策略验证指南

本文档描述如何验证统一映射策略的实现，确保用户需求得到完全满足。

## 核心需求验证

### 场景1：首次发现创建映射
**需求**：A.com/page1 首次发现 common.com 时创建映射 `common.com -> sub1.downstream.com`

**验证步骤**：
1. 启动系统，确保数据库为空状态
2. 访问 A.com/page1，该页面包含 `<script src="https://common.com/resource.js"></script>`
3. 验证系统行为：
   - 在数据库中创建了 `common.com -> sub1.downstream.com` 的映射
   - A.com/page1 的响应内容中，所有 `common.com` 都被替换为 `sub1.downstream.com`
   - 映射记录中包含发现上下文：parent_url = "A.com/page1"

**预期结果**：
```json
{
  "upstream_domain": "common.com",
  "downstream_domain": "sub1.downstream.com",
  "first_discovered_from": "https://A.com/page1",
  "discovery_count": 1,
  "discovery_contexts": [
    {
      "parent_url": "https://A.com/page1",
      "discovery_method": "HTML",
      "session_id": "session_xxx",
      "depth_level": 1
    }
  ]
}
```

### 场景2：后续发现复用映射
**需求**：B.com/page2 后续发现 common.com 时复用现有映射

**验证步骤**：
1. 在场景1的基础上，访问 B.com/page2
2. B.com/page2 也包含 `<script src="https://common.com/resource.js"></script>`
3. 验证系统行为：
   - 不创建新的映射，复用现有的 `common.com -> sub1.downstream.com`
   - B.com/page2 的响应内容中，`common.com` 被替换为 `sub1.downstream.com`
   - 映射记录中添加新的发现上下文

**预期结果**：
```json
{
  "upstream_domain": "common.com",
  "downstream_domain": "sub1.downstream.com",
  "first_discovered_from": "https://A.com/page1",
  "discovery_count": 2,
  "discovery_contexts": [
    {
      "parent_url": "https://A.com/page1",
      "discovery_method": "HTML",
      "session_id": "session_xxx",
      "depth_level": 1
    },
    {
      "parent_url": "https://B.com/page2",
      "discovery_method": "HTML",
      "session_id": "session_yyy",
      "depth_level": 1
    }
  ]
}
```

### 场景3：全局内容替换一致性
**需求**：所有页面中的同一域名都指向同一下游域名

**验证步骤**：
1. 创建包含多种引用方式的测试页面：
```html
<html>
  <head>
    <link rel="stylesheet" href="https://common.com/style.css">
    <script src="https://common.com/script.js"></script>
  </head>
  <body>
    <img src="https://common.com/image.png">
    <a href="https://common.com/page">Link</a>
    <iframe src="https://common.com/embed"></iframe>
  </body>
</html>
```

2. 验证所有引用都被正确替换：
```html
<html>
  <head>
    <link rel="stylesheet" href="https://sub1.downstream.com/style.css">
    <script src="https://sub1.downstream.com/script.js"></script>
  </head>
  <body>
    <img src="https://sub1.downstream.com/image.png">
    <a href="https://sub1.downstream.com/page">Link</a>
    <iframe src="https://sub1.downstream.com/embed"></iframe>
  </body>
</html>
```

## API验证

### 1. 域名发现API
```bash
# 模拟域名发现
curl -X POST http://localhost:1319/api/domains/discover \
  -H "Content-Type: application/json" \
  -d '{
    "upstream_domain": "example.com",
    "parent_url": "https://test.com/page1",
    "discovery_method": "HTML",
    "session_id": "test_session",
    "depth_level": 1
  }'
```

**预期响应**：
```json
{
  "success": true,
  "downstream_domain": "sub1.downstream.com",
  "is_new_mapping": true,
  "discovery_count": 1
}
```

### 2. 映射查询API
```bash
# 查询现有映射
curl http://localhost:1319/api/domains/mappings/example.com
```

**预期响应**：
```json
{
  "upstream_domain": "example.com",
  "downstream_domain": "sub1.downstream.com",
  "discovery_stats": {
    "total_discoveries": 1,
    "unique_parent_urls": 1,
    "first_discovered_from": "https://test.com/page1"
  }
}
```

### 3. 复用统计API
```bash
# 获取映射复用统计
curl http://localhost:1319/api/domains/reuse-stats
```

**预期响应**：
```json
{
  "total_mappings": 10,
  "total_discoveries": 25,
  "reused_mappings": 8,
  "single_use_mappings": 2,
  "reuse_rate": 0.8,
  "average_discoveries_per_mapping": 2.5
}
```

## 性能验证

### 并发发现测试
验证多个并发请求发现同一域名时的一致性：

```bash
# 并发发送10个请求
for i in {1..10}; do
  curl -X POST http://localhost:1319/api/domains/discover \
    -H "Content-Type: application/json" \
    -d "{
      \"upstream_domain\": \"concurrent.com\",
      \"parent_url\": \"https://site${i}.com/page\",
      \"discovery_method\": \"HTML\",
      \"session_id\": \"session_${i}\",
      \"depth_level\": 1
    }" &
done
wait
```

**验证要点**：
- 所有请求都应该返回相同的下游域名
- 数据库中只应该有一个映射记录
- 发现次数应该为10

### 大规模映射测试
验证系统处理大量映射的性能：

```bash
# 创建1000个不同的映射
for i in {1..1000}; do
  curl -X POST http://localhost:1319/api/domains/discover \
    -H "Content-Type: application/json" \
    -d "{
      \"upstream_domain\": \"domain${i}.com\",
      \"parent_url\": \"https://site${i}.com/page\",
      \"discovery_method\": \"HTML\",
      \"session_id\": \"session_${i}\",
      \"depth_level\": 1
    }"
done
```

**性能指标**：
- 平均响应时间 < 100ms
- 内存使用稳定
- 数据库查询效率高

## 边界情况验证

### 1. 无效域名处理
```bash
curl -X POST http://localhost:1319/api/domains/discover \
  -H "Content-Type: application/json" \
  -d '{
    "upstream_domain": "invalid..domain",
    "parent_url": "https://test.com",
    "discovery_method": "HTML",
    "session_id": "test",
    "depth_level": 1
  }'
```

**预期**：返回错误响应，不创建映射

### 2. 空域名处理
```bash
curl -X POST http://localhost:1319/api/domains/discover \
  -H "Content-Type: application/json" \
  -d '{
    "upstream_domain": "",
    "parent_url": "https://test.com",
    "discovery_method": "HTML",
    "session_id": "test",
    "depth_level": 1
  }'
```

**预期**：返回错误响应

### 3. 超长域名处理
测试域名长度超过限制的情况。

## 数据一致性验证

### 1. 映射唯一性检查
```sql
-- 检查上游域名的唯一性
SELECT upstream_domain, COUNT(*) as count 
FROM proxy_mappings 
GROUP BY upstream_domain 
HAVING count > 1;
```

**预期**：结果为空，确保每个上游域名只有一个映射

### 2. 发现上下文完整性检查
```sql
-- 检查发现上下文的完整性
SELECT * FROM proxy_mappings 
WHERE discovery_contexts IS NULL 
   OR discovery_count = 0 
   OR first_discovered_from IS NULL;
```

**预期**：结果为空，确保所有映射都有完整的发现信息

## 监控和日志验证

### 关键日志检查
1. **首次发现日志**：
```
INFO 创建新的全局映射: example.com -> sub1.downstream.com (首次发现来源: https://test.com/page1)
```

2. **复用映射日志**：
```
INFO 复用现有映射: example.com -> sub1.downstream.com (发现次数: 2)
```

3. **内容替换日志**：
```
DEBUG 全局映射内容替换完成 - 原始大小: 1024, 替换后大小: 1048, 替换次数: 3, 使用映射: 1
```

### 监控指标
- 映射创建速率
- 映射复用率
- 内容替换成功率
- 平均响应时间
- 错误率

## 回归测试清单

- [ ] 首次发现创建映射功能正常
- [ ] 后续发现复用映射功能正常
- [ ] 全局内容替换一致性正确
- [ ] 发现上下文追踪完整
- [ ] 并发发现处理正确
- [ ] 性能指标满足要求
- [ ] 边界情况处理正确
- [ ] 数据一致性保持
- [ ] 错误处理机制有效
- [ ] 监控和日志完整

通过以上验证步骤，可以确保统一映射策略的实现完全满足用户需求。
