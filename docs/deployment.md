# SM智能代理系统 - 高可用部署指南

## 🎯 概述

本文档提供SM智能代理系统的**企业级高可用部署**指南，确保系统在生产环境中稳定运行，具备故障自动恢复和系统重启后持续运行能力。

## 🎯 **最新更新 (v2024.12)**

### 🏗️ **架构升级**
- ✅ **Pingora代理核心**: 基于字节跳动开源的高性能代理框架
- ✅ **双端口服务**: Web管理界面(1319) + Pingora代理(1911)
- ✅ **MongoDB强制要求**: 移除内存数据库，强制使用MongoDB 7.0+
- ✅ **代码质量优化**: 经过Clippy深度优化，修复250+警告

### 🛡️ **安全与性能提升**
- ✅ **安全问题修复**: 解决跨await持有锁等关键安全问题
- ✅ **性能优化**: 异步代码优化，内存使用优化
- ✅ **类型安全**: 强化类型系统，减少运行时错误
- ✅ **错误处理**: 统一错误处理机制

## 🚀 高可用特性

### ✅ 企业级可靠性保障

| 功能 | 状态 | 说明 |
|------|------|------|
| **后台服务运行** | ✅ | systemd 专业级服务管理 |
| **故障自动重启** | ✅ | 10秒内自动恢复，无需人工干预 |
| **卡死自动重启** | ✅ | 端口检查+进程检查，智能重启 |
| **开机自动启动** | ✅ | 系统重启后自动启动所有服务 |
| **服务健康监控** | ✅ | 每2分钟自动检查，异常自动修复 |
| **重启后自动恢复** | ✅ | 所有功能持续运行 |
| **资源限制保护** | ✅ | 内存2GB/CPU200%限制，防止系统过载 |
| **安全沙箱运行** | ✅ | systemd安全特性，隔离运行环境 |
| **完整监控日志** | ✅ | systemd定时器替代cron，更可靠 |

### 🛡️ **守护进程核心特性**

#### **1. Systemd 服务配置**
```bash
# 故障自动重启配置
Restart=always          # 任何情况下都自动重启
RestartSec=10           # 重启间隔10秒
StartLimitInterval=60   # 60秒内限制重启次数
StartLimitBurst=3       # 最多连续重启3次

# 服务器重启自动启动
systemctl enable sm     # 开机自动启动
WantedBy=multi-user.target  # 多用户模式下启动

# 进程管理
KillMode=mixed          # 混合终止模式
TimeoutStartSec=30      # 启动超时30秒
TimeoutStopSec=15       # 停止超时15秒
```

#### **2. 服务监控系统**
- ✅ **服务状态检查**: 每2分钟检查服务是否运行
- ✅ **端口连通性检查**: 检查1319和1911端口
- ✅ **自动重启**: 检测到故障自动重启服务
- ✅ **重启次数限制**: 最多重启5次，防止无限重启
- ✅ **系统重启检测**: 检测系统重启后自动恢复

#### **3. 故障处理机制**
- ✅ **端口检查**: 检测端口是否响应
- ✅ **进程检查**: 检测进程是否存在但无响应
- ✅ **自动重启**: 检测到卡死自动重启服务
- ✅ **智能重启策略**: 重启次数限制，防止无限循环

#### **4. Systemd 定时器**
```bash
OnBootSec=2min          # 系统启动2分钟后开始监控
OnUnitActiveSec=2min    # 每2分钟执行一次检查
Persistent=true         # 持久化定时器
```

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心以上 (推荐4核心)
- **内存**: 4GB以上 (推荐8GB，应用限制使用2GB)
- **磁盘**: 20GB以上可用空间
- **网络**: 稳定网络连接，开放端口1319和1911

### 软件要求
- **操作系统**: Linux服务器 (Ubuntu 20.04+, Debian 11+, CentOS 8+, RHEL 8+)
- **Rust**: 1.70+（仅编译时需要）
- **systemd**: 用于服务管理（Linux标配）
- **curl**: 用于健康检查（部署脚本自动检查）

### 必需依赖
- **MongoDB 7.0+**: 数据持久化（强制要求，不再支持内存数据库）
- **防火墙**: ufw或firewalld（部署脚本自动配置）

### 可选依赖
- **监控工具**: htop, iotop等系统监控工具
- **负载均衡**: Nginx/HAProxy（多实例部署时）

## 🛠️ 环境准备

### 1. 安装Rust编译环境
```bash
# 安装Rust（仅编译时需要）
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source $HOME/.cargo/env

# 验证安装
rustc --version
cargo --version
```

### 2. 安装系统依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y curl git build-essential pkg-config libssl-dev

# CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y curl git openssl-devel pkg-config

# 验证systemd（Linux标配）
systemctl --version
```

### 3. 安装MongoDB 7.0（必需）
```bash
# Ubuntu/Debian
wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list
sudo apt update
sudo apt install -y mongodb-org

# CentOS/RHEL
sudo tee /etc/yum.repos.d/mongodb-org-7.0.repo << 'EOF'
[mongodb-org-7.0]
name=MongoDB Repository
baseurl=https://repo.mongodb.org/yum/redhat/$releasever/mongodb-org/7.0/x86_64/
gpgcheck=1
enabled=1
gpgkey=https://www.mongodb.org/static/pgp/server-7.0.asc
EOF
sudo yum install -y mongodb-org

# 启动MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# 验证安装
mongosh --eval "db.adminCommand('ismaster')"
```

## 🚀 一键高可用部署

### ⚡ 快速部署（推荐）
```bash
# 1. 克隆项目
git clone <your-repo-url> /opt/sm
cd /opt/sm

# 2. 编译项目
cargo build --release

# 3. 一键高可用部署
sudo ./setup.sh
```

### 🎯 部署脚本自动完成的任务
- ✅ **Linux系统检查** - 确保在Linux服务器上运行
- ✅ **依赖检查** - 自动检查并提示安装缺失依赖
- ✅ **智能路径检测** - 自动选择最佳部署路径
- ✅ **服务用户创建** - 创建专用proxy用户
- ✅ **文件权限设置** - 设置安全的文件权限
- ✅ **systemd服务配置** - 配置高可用systemd服务
- ✅ **开机自启设置** - 启用开机自动启动
- ✅ **监控定时器创建** - 创建systemd定时器监控
- ✅ **防火墙配置** - 自动配置防火墙规则
- ✅ **管理工具生成** - 创建状态检查、监控等工具

### 🧪 部署后验证
```bash
# 1. 检查服务状态
sudo systemctl status sm

# 2. 验证开机自启
sudo systemctl is-enabled sm

# 3. 检查监控定时器
sudo systemctl status sm-monitor.timer

# 4. 运行完整测试
./test-api.sh

# 5. 查看服务日志
sudo journalctl -u sm -f
```

## 🔧 高可用配置详解

### systemd服务配置
部署脚本自动创建的高可用systemd服务包含以下特性：

```ini
[Unit]
Description=SM - 智能代理服务 (高可用后台服务)
After=network-online.target          # 等待网络完全就绪
Wants=network-online.target
RequiresMountsFor=/opt/sm           # 确保挂载点可用

[Service]
Type=simple
User=proxy
Group=proxy
WorkingDirectory=/opt/sm
ExecStart=/opt/sm/sm

# 故障自动重启配置
Restart=always                      # 任何情况下都自动重启
RestartSec=10                      # 重启间隔10秒
StartLimitInterval=60              # 60秒内最多重启3次
StartLimitBurst=3
WatchdogSec=30                     # 30秒看门狗检测

# 资源保护
MemoryMax=2G                       # 最大内存2GB
CPUQuota=200%                      # 最大CPU 200%
LimitNOFILE=65536                  # 文件句柄限制

# 安全沙箱
NoNewPrivileges=yes                # 禁止提权
PrivateTmp=yes                     # 私有临时目录
ProtectSystem=strict               # 严格系统保护

[Install]
WantedBy=multi-user.target         # 开机自启
```

### 监控定时器配置
自动创建的systemd定时器确保服务持续监控：

```ini
[Unit]
Description=SM服务监控定时器
Requires=sm-monitor.service

[Timer]
OnBootSec=2min                     # 开机2分钟后启动
OnUnitActiveSec=2min               # 每2分钟运行一次
Persistent=true                    # 重启后保持状态

[Install]
WantedBy=timers.target             # 开机自启
```

## 🛠️ 管理工具

部署完成后，系统会自动生成以下管理工具：

### 状态检查工具
```bash
# 运行完整状态检查
/opt/sm/status.sh
```
显示内容：
- ✅ 服务运行状态和运行时间
- ✅ 开机自启状态
- ✅ 监控定时器状态
- ✅ 端口监听状态
- ✅ 进程和资源使用情况
- ✅ 重启次数统计
- ✅ 最近日志记录
- ✅ API健康检查

### 服务监控工具
```bash
# 手动运行监控检查
/opt/sm/monitor.sh
```
监控功能：
- 🔍 检测服务状态
- 🔍 检测端口监听
- 🛡️ 自动重启故障服务
- 📝 记录监控日志
- 🔄 系统重启检测

### API测试工具
```bash
# 运行API功能测试
./test-api.sh
```
测试内容：
- 🧪 服务状态检查
- 🧪 端口监听测试
- 🧪 API健康检查
- 🧪 性能测试
- 🧪 重启次数统计

### 系统重启测试工具
```bash
# 验证重启后持续运行
./test-reboot.sh
```
验证内容：
- 🔄 系统运行时间检查
- 🔄 主服务自动启动验证
- 🔄 监控定时器自动启动验证
- 🔄 端口和API功能验证

## 🔄 系统重启后持续运行

### ✅ 重启后自动恢复流程
1. **系统启动** → systemd启动
2. **网络就绪** → SM服务自动启动
3. **2分钟后** → 监控定时器开始工作
4. **每2分钟** → 自动检查服务状态
5. **发现异常** → 自动重启服务

### 🧪 验证重启后持续运行
```bash
# 方法1: 重启系统后验证
sudo reboot
# 等待系统重启完成，然后运行：
./test-reboot.sh

# 方法2: 模拟服务故障
sudo systemctl stop sm
# 等待2分钟，监控系统会自动重启服务

# 方法3: 查看服务自启状态
sudo systemctl is-enabled sm          # 应该显示 "enabled"
sudo systemctl is-enabled sm-monitor.timer  # 应该显示 "enabled"
```

### 📊 持续运行保障机制
- **🔄 systemd服务管理** - 开机自动启动
- **⏰ systemd定时器** - 替代cron，更可靠
- **🛡️ 故障自动重启** - Restart=always配置
- **👁️ 健康监控** - 每2分钟检查一次
- **📝 完整日志** - systemd日志记录所有活动

## 🌐 网络配置

### 防火墙设置（自动配置）
部署脚本会自动配置防火墙，也可以手动设置：

```bash
# 使用 ufw
sudo ufw allow 1319/tcp comment "SM Frontend"
sudo ufw allow 1911/tcp comment "SM Backend"
sudo ufw enable

# 使用 firewalld
sudo firewall-cmd --permanent --add-port=1319/tcp
sudo firewall-cmd --permanent --add-port=1911/tcp
sudo firewall-cmd --reload
```

## 🎯 服务管理

### 基本服务管理命令
```bash
# 启动服务
sudo systemctl start sm

# 停止服务
sudo systemctl stop sm

# 重启服务
sudo systemctl restart sm

# 查看服务状态
sudo systemctl status sm

# 查看详细状态
/opt/sm/status.sh

# 查看实时日志
sudo journalctl -u sm -f

# 查看错误日志
sudo journalctl -u sm -p err
```

### 监控管理命令
```bash
# 查看监控定时器状态
sudo systemctl status sm-monitor.timer

# 手动运行监控检查
/opt/sm/monitor.sh

# 查看监控日志
sudo journalctl -u sm-monitor.service

# 查看定时器列表
sudo systemctl list-timers sm-monitor.timer
```

## 🔄 更新部署

### 标准更新流程
```bash
# 1. 拉取新代码
cd /opt/sm
git pull

# 2. 重新编译
cargo build --release

# 3. 一键重新部署
sudo ./setup.sh

# 4. 重启服务
sudo systemctl restart sm

# 5. 验证更新
./test-api.sh
```

### 零停机更新（推荐）
```bash
# 1. 备份当前版本
sudo cp /opt/sm/sm /opt/sm/sm.backup.$(date +%Y%m%d_%H%M%S)

# 2. 编译新版本
cargo build --release

# 3. 热更新二进制文件
sudo systemctl stop sm
sudo cp target/release/sm /opt/sm/
sudo systemctl start sm

# 4. 验证更新
sleep 10
./test-api.sh
```

## 🆘 故障排除

### 快速诊断
```bash
# 运行完整状态检查
/opt/sm/status.sh

# 检查系统重启后恢复
./test-reboot.sh

# 运行API测试
./test-api.sh
```

### 常见问题解决
1. **服务启动失败**
   ```bash
   sudo journalctl -u sm -n 50
   sudo systemctl status sm -l
   ```

2. **端口被占用**
   ```bash
   sudo ss -tlnp | grep -E ":(1319|1911)"
   sudo lsof -i :1319
   ```

3. **监控定时器未运行**
   ```bash
   sudo systemctl status sm-monitor.timer
   sudo systemctl restart sm-monitor.timer
   ```

4. **重启后服务未自启**
   ```bash
   sudo systemctl is-enabled sm
   sudo systemctl enable sm
   ```

### 紧急恢复
```bash
# 完全重置服务
sudo systemctl stop sm
sudo systemctl stop sm-monitor.timer
sudo ./setup.sh
sudo systemctl start sm
```

## 📊 生产环境监控

### 系统资源监控
```bash
# 查看服务资源使用
systemctl show sm --property=MemoryCurrent,CPUUsageNSec

# 查看系统负载
htop
iotop
```

### 日志管理
```bash
# 设置日志轮转
sudo journalctl --vacuum-time=30d
sudo journalctl --vacuum-size=1G

# 查看日志大小
sudo journalctl --disk-usage
```

---

## 🎉 部署完成

恭喜！您已成功部署SM智能代理系统的高可用版本。

### ✅ 已实现的功能
- 🔄 **后台服务运行** - systemd专业管理
- 🛡️ **故障自动重启** - 10秒内自动恢复
- 🚀 **开机自动运行** - 系统重启后自动启动
- 👁️ **服务健康监控** - 每2分钟自动检查
- 🔒 **资源保护** - 内存和CPU限制
- 🛡️ **安全沙箱** - systemd安全特性

### 🌐 访问地址
- **前端管理**: http://your-server:1319
- **代理服务**: http://your-server:1911
- **首次登录**: admin / admin888 (请立即修改密码)

**下一步**: 查看 [安全配置](security.md) 来加强生产环境安全性。