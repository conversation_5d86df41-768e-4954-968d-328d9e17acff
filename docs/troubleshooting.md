# 故障排除指南

## 🎯 **最新更新 (v2024.12)**

### 🔧 **代码优化后的新问题**
- ✅ **编译警告处理**: 如何处理Clippy优化后的390个警告
- ✅ **Pingora集成问题**: 双端口服务的故障排除
- ✅ **递归代理问题**: 递归深度和域名发现问题
- ✅ **MongoDB强制要求**: 数据库连接和配置问题

## 🔍 常见问题诊断

### 快速诊断脚本
```bash
# 运行快速诊断
/opt/sm/status.sh

# 或手动检查
sudo systemctl status sm
sudo journalctl -u sm -n 20
```

## 🚀 服务启动问题

### 服务无法启动
**症状**: `systemctl start sm` 失败

**诊断步骤**:
```bash
# 1. 查看详细错误
sudo journalctl -u sm -f

# 2. 检查配置文件
sudo -u proxy /opt/sm/sm --check-config

# 3. 检查文件权限
ls -la /opt/sm/sm
ls -la /opt/sm/.env

# 4. 检查端口占用
sudo ss -tlnp | grep -E ":(1319|1911)"
```

**常见解决方案**:
```bash
# 权限问题
sudo chmod 755 /opt/sm/sm
sudo chown proxy:proxy /opt/sm/sm

# 端口占用
sudo systemctl stop sm
sudo pkill -f sm

# 配置文件问题
sudo nano /opt/sm/.env
sudo systemctl restart sm
```

### 编译错误
**症状**: `cargo build --release` 失败

**解决方案**:
```bash
# 更新 Rust
rustup update

# 清理缓存
cargo clean

# 重新编译
cargo build --release

# 检查依赖
cargo check
```

### 编译警告处理
**症状**: 编译成功但有390个警告

**说明**: 这些警告是正常的，主要包括：
- 未使用代码 (Dead Code) - 为功能完整性保留
- 未使用变量 - 函数参数暂时未使用
- Async Trait 警告 - 编译器建议，不影响功能

**如果需要清理警告**:
```bash
# 查看具体警告
cargo clippy

# 自动修复部分警告
cargo clippy --fix

# 允许特定警告
#[allow(dead_code)]
#[allow(unused_variables)]
```

### Pingora双端口问题
**症状**: Web界面(1319)正常但代理服务(1911)无法访问

**诊断步骤**:
```bash
# 1. 检查两个端口都在监听
sudo ss -tlnp | grep -E ":(1319|1911)"

# 2. 检查Pingora服务状态
sudo journalctl -u sm -f | grep -i pingora

# 3. 测试代理端口
curl -v http://127.0.0.1:1911/health
```

**解决方案**:
```bash
# Pingora代理仅本地访问是正常的
# 如需外部访问，需要通过Web管理界面配置

# 重启服务
sudo systemctl restart sm

# 检查配置
grep -i listen /opt/sm/config/config.yaml
```

## 🌐 网络连接问题

### 无法访问管理界面
**症状**: 浏览器无法打开 `http://localhost:1319`

**诊断步骤**:
```bash
# 1. 检查服务是否运行
sudo systemctl status sm

# 2. 检查端口监听
sudo ss -tlnp | grep :1319

# 3. 检查防火墙
sudo ufw status
sudo iptables -L -n

# 4. 测试本地连接
curl -v http://localhost:1319/api/health
```

**解决方案**:
```bash
# 防火墙问题
sudo ufw allow 1319/tcp

# 重启服务
sudo systemctl restart sm

# 检查监听地址
grep "listen" /opt/sm/config/config.yaml
```

### API 接口 404 错误
**症状**: API 请求返回 404

**诊断步骤**:
```bash
# 检查 API 路由
curl -v http://localhost:1319/api/health

# 查看应用日志
tail -f /opt/sm/logs/app.log

# 检查配置
cat /opt/sm/config/config.yaml
```

**解决方案**:
```bash
# 重启服务
sudo systemctl restart sm

# 检查配置格式
yaml-lint /opt/sm/config/config.yaml

# 重新加载配置
curl -X POST http://localhost:1319/api/config/reload
```

## 🔐 认证问题

### 登录失败
**症状**: 用户名密码正确但无法登录

**诊断步骤**:
```bash
# 1. 查看认证日志
tail -f /opt/sm/logs/security.log

# 2. 检查 JWT 配置
grep JWT /opt/sm/.env

# 3. 检查用户配置
grep ADMIN /opt/sm/.env
```

**解决方案**:
```bash
# 重置管理员密码
echo 'ADMIN_PASSWORD_HASH="$6$salt$hashedpassword"' >> /opt/sm/.env

# 重新生成 JWT 密钥
echo "JWT_SECRET=$(openssl rand -base64 64)" >> /opt/sm/.env

# 重启服务
sudo systemctl restart sm
```

### Token 无效
**症状**: API 请求返回 401 未授权

**解决方案**:
```bash
# 重新登录获取新 token
curl -X POST http://localhost:1319/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"your_password"}'

# 检查 token 格式
echo "your_token" | base64 -d
```

## 💾 性能问题

### 内存使用过高
**症状**: 系统内存不足

**诊断步骤**:
```bash
# 1. 检查内存使用
free -h
ps aux | grep sm

# 2. 检查系统资源限制
systemctl show sm | grep Memory

# 3. 查看内存泄漏
valgrind --tool=memcheck /opt/sm/sm
```

**解决方案**:
```bash
# 调整内存限制
sudo systemctl edit sm
```
```ini
[Service]
MemoryMax=1G
MemorySwapMax=512M
```

```bash
# 重启服务
sudo systemctl daemon-reload
sudo systemctl restart sm

# 清理缓存
curl -X DELETE http://localhost:1319/api/cache/clear
```

### 响应缓慢
**症状**: API 响应时间过长

**诊断步骤**:
```bash
# 1. 检查系统负载
htop
iostat -x 1

# 2. 检查网络延迟
ping upstream-server.com

# 3. 分析日志
grep "slow" /opt/sm/logs/app.log
```

**解决方案**:
```bash
# 优化配置
sudo nano /opt/sm/config/config.yaml
```
```yaml
server:
  workers: 8  # 增加工作进程
  keep_alive: 30
  
cache:
  enabled: true
  max_size: "256MB"
```

## 🔄 递归代理问题

### 递归代理无法启动
**症状**: 递归代理功能不工作

**诊断步骤**:
```bash
# 1. 检查递归代理状态
curl http://localhost:1319/api/recursive-proxy/status

# 2. 查看递归代理日志
tail -f /opt/sm/logs/app.log | grep -i recursive

# 3. 检查配置
curl http://localhost:1319/api/recursive-proxy/config
```

**解决方案**:
```bash
# 启动递归代理
curl -X POST http://localhost:1319/api/recursive-proxy/start

# 检查配置限制
# max_depth: 递归深度限制
# max_discovered_domains: 域名发现限制
# request_timeout: 请求超时设置
```

### 递归深度达到限制
**症状**: 递归代理停止在某个深度

**解决方案**:
```bash
# 调整递归配置
curl -X PUT http://localhost:1319/api/recursive-proxy/config \
  -H "Content-Type: application/json" \
  -d '{
    "max_depth": 10,
    "max_discovered_domains": 200,
    "request_timeout": 60
  }'
```

### 域名发现失败
**症状**: 无法从响应中提取新域名

**诊断步骤**:
```bash
# 检查内容类型支持
# 确保响应是 text/html 或 application/json

# 查看URL提取日志
tail -f /opt/sm/logs/app.log | grep -i "url extract"

# 检查域名黑名单
curl http://localhost:1319/api/domain-pool/blacklist
```

### 缓存问题
**症状**: 递归代理缓存不工作或缓存过期

**解决方案**:
```bash
# 清理递归代理缓存
curl -X DELETE http://localhost:1319/api/cache/clear

# 检查缓存配置
curl http://localhost:1319/api/cache/status

# 调整缓存TTL
curl -X POST http://localhost:1319/api/cache/config \
  -H "Content-Type: application/json" \
  -d '{"ttl": 3600, "enabled": true}'
```

## 🗄️ 数据库问题

### MongoDB 强制要求问题
**症状**: 系统提示必须安装MongoDB

**说明**: 系统已移除内存数据库支持，强制要求MongoDB

**解决方案**:
```bash
# Ubuntu/Debian 安装MongoDB 7.0
wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list
sudo apt update
sudo apt install -y mongodb-org

# 启动MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# 验证安装
mongosh --eval "db.adminCommand('ismaster')"
```

### MongoDB 连接失败
**症状**: 数据库连接错误

**诊断步骤**:
```bash
# 1. 检查 MongoDB 状态
sudo systemctl status mongodb

# 2. 测试连接
mongosh $MONGODB_URI

# 3. 检查网络
telnet mongodb-host 27017
```

**解决方案**:
```bash
# 重启 MongoDB
sudo systemctl restart mongodb

# 检查认证
mongosh admin
> db.auth("username", "password")

# 更新连接字符串
echo 'MONGODB_URI="******************************************"' >> /opt/sm/.env
```

### Redis 连接问题
**症状**: 缓存服务不可用

**解决方案**:
```bash
# 检查 Redis 状态
sudo systemctl status redis

# 测试连接
redis-cli ping

# 重启 Redis
sudo systemctl restart redis

# 清理连接
redis-cli flushall
```

## 📁 文件系统问题

### 权限拒绝
**症状**: 文件访问权限错误

**解决方案**:
```bash
# 重新设置权限
sudo chown -R proxy:proxy /opt/sm
sudo chmod 755 /opt/sm/sm
sudo chmod 600 /opt/sm/.env
sudo chmod -R 750 /opt/sm/{config,logs,data}

# 检查 SELinux (如果启用)
sudo setsebool -P httpd_can_network_connect 1
```

### 磁盘空间不足
**症状**: 写入失败或日志无法记录

**解决方案**:
```bash
# 检查磁盘使用
df -h /opt/sm

# 清理日志
find /opt/sm/logs -name "*.log.*" -mtime +7 -delete

# 压缩旧日志
gzip /opt/sm/logs/*.log

# 清理系统临时文件
sudo apt autoclean
sudo apt autoremove
```

## 🔒 SSL/TLS 问题

### 证书问题
**症状**: HTTPS 访问失败

**诊断步骤**:
```bash
# 1. 检查证书有效性
openssl x509 -in /opt/sm/certs/cert.pem -text -noout

# 2. 验证证书链
openssl verify -CAfile ca.pem /opt/sm/certs/cert.pem

# 3. 测试 TLS 连接
openssl s_client -connect localhost:1319 -servername your-domain.com
```

**解决方案**:
```bash
# 重新生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout /opt/sm/certs/key.pem \
  -out /opt/sm/certs/cert.pem -days 365 -nodes

# 更新 Let's Encrypt 证书
sudo certbot renew

# 设置正确权限
sudo chmod 600 /opt/sm/certs/key.pem
sudo chmod 644 /opt/sm/certs/cert.pem
```

## 🔄 配置问题

### 配置文件格式错误
**症状**: 服务启动时配置解析失败

**解决方案**:
```bash
# 验证 YAML 格式
python3 -c "import yaml; yaml.safe_load(open('/opt/sm/config/config.yaml'))"

# 检查配置语法
/opt/sm/sm --check-config

# 使用默认配置
cp /opt/sm/config/config.yaml.example /opt/sm/config/config.yaml
```

### 热重载失败
**症状**: 配置更改后未生效

**解决方案**:
```bash
# 手动重载配置
curl -X POST http://localhost:1319/api/config/reload

# 重启服务
sudo systemctl restart sm

# 检查配置文件权限
ls -la /opt/sm/config/config.yaml
```

## 🔧 系统级问题

### 系统资源限制
**症状**: 文件描述符不足、进程数限制

**解决方案**:
```bash
# 检查当前限制
ulimit -a

# 增加系统限制
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 更新 systemd 服务限制
sudo systemctl edit sm
```
```ini
[Service]
LimitNOFILE=65536
LimitNPROC=4096
```

### 防火墙阻断
**症状**: 外部无法访问服务

**解决方案**:
```bash
# 检查防火墙规则
sudo ufw status verbose

# 添加规则
sudo ufw allow from any to any port 1319
sudo ufw allow from any to any port 1911

# 检查 iptables
sudo iptables -L -n
```

## 📊 日志分析

### 查看详细日志
```bash
# 应用日志
tail -f /opt/sm/logs/app.log

# 系统日志
sudo journalctl -u sm -f

# 错误日志
tail -f /opt/sm/logs/error.log

# 安全日志
tail -f /opt/sm/logs/security.log
```

### 日志级别调整
```bash
# 临时开启调试模式
export RUST_LOG=debug
sudo systemctl restart sm

# 永久设置
echo "RUST_LOG=debug" | sudo tee -a /opt/sm/.env
```

## 🆘 紧急恢复

### 服务完全失效
```bash
# 1. 停止服务
sudo systemctl stop sm

# 2. 备份当前状态
sudo tar -czf /tmp/sm-backup-$(date +%Y%m%d).tar.gz /opt/sm

# 3. 重新部署
cd /opt/sm
sudo ./setup.sh

# 4. 恢复配置
sudo cp /tmp/backup/.env /opt/sm/
sudo systemctl start sm
```

### 数据库损坏
```bash
# MongoDB 修复
sudo systemctl stop mongodb
sudo mongod --repair --dbpath /var/lib/mongodb
sudo systemctl start mongodb

# 从备份恢复
mongorestore --drop /path/to/backup
```

## 📋 诊断工具

### 创建诊断脚本
```bash
sudo tee /opt/sm/diagnose.sh << 'EOF'
#!/bin/bash
echo "=== SM 服务诊断报告 ==="
echo "时间: $(date)"
echo ""

echo "1. 服务状态:"
systemctl is-active sm && echo "✅ 服务运行中" || echo "❌ 服务已停止"

echo ""
echo "2. 端口监听:"
ss -tlnp | grep -E ":(1319|1911)" || echo "❌ 端口未监听"

echo ""
echo "3. 进程信息:"
ps aux | grep sm | grep -v grep

echo ""
echo "4. 内存使用:"
free -h

echo ""
echo "5. 磁盘空间:"
df -h /opt/sm

echo ""
echo "6. 最近错误:"
tail -n 5 /opt/sm/logs/error.log 2>/dev/null || echo "无错误日志"

echo ""
echo "7. 网络测试:"
curl -s http://localhost:1319/api/health && echo "✅ API 可访问" || echo "❌ API 不可访问"
EOF

chmod +x /opt/sm/diagnose.sh
```

## 📞 获取帮助

### 收集诊断信息
运行诊断脚本并收集以下信息：
```bash
# 运行完整诊断
/opt/sm/diagnose.sh > /tmp/sm-diagnosis.txt

# 收集系统信息
uname -a >> /tmp/sm-diagnosis.txt
cat /etc/os-release >> /tmp/sm-diagnosis.txt

# 收集配置信息
grep -v "SECRET\|PASSWORD" /opt/sm/.env >> /tmp/sm-diagnosis.txt
```

### 日志导出
```bash
# 导出最近的日志
journalctl -u sm --since "1 hour ago" > /tmp/sm-system.log
tail -n 100 /opt/sm/logs/app.log > /tmp/sm-app.log
tail -n 100 /opt/sm/logs/error.log > /tmp/sm-error.log
```

---

**提示**: 如果问题仍未解决，请检查 [API 文档](api.md) 确认接口使用方式，或查看 [安全配置](security.md) 确认安全设置。