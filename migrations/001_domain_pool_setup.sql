-- 域名池管理数据库迁移脚本
-- 版本: v1.0
-- 创建时间: 2025-06-11

-- 创建下游域名池表
CREATE TABLE IF NOT EXISTS downstream_pool (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain TEXT NOT NULL UNIQUE,
    added_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status TEXT NOT NULL CHECK (status IN ('available', 'used', 'reserved', 'disabled')) DEFAULT 'available',
    priority INTEGER NOT NULL DEFAULT 0,
    created_by TEXT,
    notes TEXT,
    UNIQUE(domain)
);

-- 创建上游域名池表
CREATE TABLE IF NOT EXISTS upstream_pool (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain TEXT NOT NULL UNIQUE,
    added_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status TEXT NOT NULL CHECK (status IN ('available', 'used', 'reserved', 'disabled')) DEFAULT 'available',
    priority INTEGER NOT NULL DEFAULT 0,
    health_status TEXT NOT NULL CHECK (health_status IN ('healthy', 'unhealthy', 'checking', 'captcha', 'timeout', 'unknown')) DEFAULT 'unknown',
    last_check DATETIME,
    response_time INTEGER, -- 毫秒
    tags TEXT, -- JSON格式的标签
    created_by TEXT,
    notes TEXT,
    UNIQUE(domain)
);

-- 创建代理映射表
CREATE TABLE IF NOT EXISTS proxy_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    downstream_domain TEXT NOT NULL,
    upstream_domain TEXT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_used DATETIME,
    status TEXT NOT NULL CHECK (status IN ('active', 'inactive', 'failed', 'paused')) DEFAULT 'active',
    request_count INTEGER NOT NULL DEFAULT 0,
    success_count INTEGER NOT NULL DEFAULT 0,
    error_count INTEGER NOT NULL DEFAULT 0,
    average_response_time INTEGER, -- 毫秒
    last_error TEXT,
    is_manual BOOLEAN NOT NULL DEFAULT FALSE, -- 区分手动创建和自动配对
    created_by TEXT,
    UNIQUE(downstream_domain)
);

-- 创建递归链路记录表
CREATE TABLE IF NOT EXISTS recursive_chains (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    mapping_id INTEGER NOT NULL,
    original_upstream TEXT NOT NULL,
    extracted_upstream TEXT NOT NULL,
    extraction_rule TEXT NOT NULL,
    extracted_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    chain_depth INTEGER NOT NULL DEFAULT 1,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    response_data TEXT, -- 存储响应内容的摘要
    FOREIGN KEY (mapping_id) REFERENCES proxy_mappings(id) ON DELETE CASCADE
);

-- 创建配对配置表
CREATE TABLE IF NOT EXISTS pairing_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    max_batch_size INTEGER NOT NULL DEFAULT 100,
    auto_pairing_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    health_check_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    health_check_interval INTEGER NOT NULL DEFAULT 300, -- 秒
    max_retry_attempts INTEGER NOT NULL DEFAULT 3,
    timeout_seconds INTEGER NOT NULL DEFAULT 30,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT
);

-- 创建健康检查历史表
CREATE TABLE IF NOT EXISTS health_check_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain TEXT NOT NULL,
    check_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status TEXT NOT NULL,
    response_time INTEGER, -- 毫秒
    status_code INTEGER,
    error_message TEXT,
    requires_captcha BOOLEAN NOT NULL DEFAULT FALSE,
    redirect_url TEXT
);

-- 创建域名池操作日志表
CREATE TABLE IF NOT EXISTS domain_pool_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operation_type TEXT NOT NULL, -- 'add', 'remove', 'pair', 'health_check', etc.
    domain_type TEXT, -- 'downstream', 'upstream'
    domain TEXT,
    details TEXT, -- JSON格式的详细信息
    performed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    performed_by TEXT,
    result TEXT NOT NULL -- 'success', 'failed'
);

-- 创建索引以提高查询性能

-- 下游域名池索引
CREATE INDEX IF NOT EXISTS idx_downstream_pool_status ON downstream_pool(status);
CREATE INDEX IF NOT EXISTS idx_downstream_pool_priority ON downstream_pool(priority);
CREATE INDEX IF NOT EXISTS idx_downstream_pool_added_at ON downstream_pool(added_at);

-- 上游域名池索引
CREATE INDEX IF NOT EXISTS idx_upstream_pool_status ON upstream_pool(status);
CREATE INDEX IF NOT EXISTS idx_upstream_pool_health_status ON upstream_pool(health_status);
CREATE INDEX IF NOT EXISTS idx_upstream_pool_priority ON upstream_pool(priority);
CREATE INDEX IF NOT EXISTS idx_upstream_pool_last_check ON upstream_pool(last_check);

-- 代理映射索引
CREATE INDEX IF NOT EXISTS idx_proxy_mappings_downstream ON proxy_mappings(downstream_domain);
CREATE INDEX IF NOT EXISTS idx_proxy_mappings_upstream ON proxy_mappings(upstream_domain);
CREATE INDEX IF NOT EXISTS idx_proxy_mappings_status ON proxy_mappings(status);
CREATE INDEX IF NOT EXISTS idx_proxy_mappings_last_used ON proxy_mappings(last_used);

-- 递归链路索引
CREATE INDEX IF NOT EXISTS idx_recursive_chains_mapping_id ON recursive_chains(mapping_id);
CREATE INDEX IF NOT EXISTS idx_recursive_chains_extracted_at ON recursive_chains(extracted_at);
CREATE INDEX IF NOT EXISTS idx_recursive_chains_is_active ON recursive_chains(is_active);

-- 健康检查历史索引
CREATE INDEX IF NOT EXISTS idx_health_check_domain ON health_check_history(domain);
CREATE INDEX IF NOT EXISTS idx_health_check_time ON health_check_history(check_time);
CREATE INDEX IF NOT EXISTS idx_health_check_status ON health_check_history(status);

-- 操作日志索引
CREATE INDEX IF NOT EXISTS idx_domain_pool_logs_operation ON domain_pool_logs(operation_type);
CREATE INDEX IF NOT EXISTS idx_domain_pool_logs_domain ON domain_pool_logs(domain);
CREATE INDEX IF NOT EXISTS idx_domain_pool_logs_performed_at ON domain_pool_logs(performed_at);

-- 插入默认配对配置
INSERT OR IGNORE INTO pairing_configs (id, max_batch_size, auto_pairing_enabled, health_check_enabled) 
VALUES (1, 100, TRUE, TRUE);

-- 创建视图以简化常用查询

-- 域名池统计视图
CREATE VIEW IF NOT EXISTS domain_pool_stats AS
SELECT 
    (SELECT COUNT(*) FROM downstream_pool WHERE status = 'available') as downstream_available,
    (SELECT COUNT(*) FROM downstream_pool WHERE status = 'used') as downstream_used,
    (SELECT COUNT(*) FROM upstream_pool WHERE status = 'available') as upstream_available,
    (SELECT COUNT(*) FROM upstream_pool WHERE status = 'used') as upstream_used,
    (SELECT COUNT(*) FROM upstream_pool WHERE health_status = 'healthy') as upstream_healthy,
    (SELECT COUNT(*) FROM upstream_pool WHERE health_status = 'unhealthy') as upstream_unhealthy,
    (SELECT COUNT(*) FROM proxy_mappings WHERE status = 'active') as active_mappings,
    (SELECT COUNT(*) FROM proxy_mappings WHERE status = 'failed') as failed_mappings,
    (SELECT COALESCE(SUM(request_count), 0) FROM proxy_mappings) as total_requests,
    (SELECT COALESCE(SUM(success_count), 0) FROM proxy_mappings) as total_success;

-- 活跃代理映射详情视图
CREATE VIEW IF NOT EXISTS active_proxy_mappings AS
SELECT 
    pm.*,
    up.health_status as upstream_health,
    up.last_check as upstream_last_check,
    up.response_time as upstream_response_time
FROM proxy_mappings pm
LEFT JOIN upstream_pool up ON pm.upstream_domain = up.domain
WHERE pm.status = 'active';

-- 健康检查摘要视图
CREATE VIEW IF NOT EXISTS health_check_summary AS
SELECT 
    domain,
    status,
    response_time,
    check_time,
    ROW_NUMBER() OVER (PARTITION BY domain ORDER BY check_time DESC) as rn
FROM health_check_history;

-- 创建触发器以维护数据一致性

-- 当代理映射创建时，更新域名池状态
CREATE TRIGGER IF NOT EXISTS update_domain_status_on_mapping_create
AFTER INSERT ON proxy_mappings
FOR EACH ROW
WHEN NEW.is_manual = FALSE  -- 只对自动配对生效
BEGIN
    -- 标记下游域名为已使用
    UPDATE downstream_pool 
    SET status = 'used' 
    WHERE domain = NEW.downstream_domain AND status = 'available';
    
    -- 标记上游域名为已使用
    UPDATE upstream_pool 
    SET status = 'used' 
    WHERE domain = NEW.upstream_domain AND status = 'available';
    
    -- 记录操作日志
    INSERT INTO domain_pool_logs (operation_type, details, result)
    VALUES (
        'auto_pair',
        json_object(
            'downstream_domain', NEW.downstream_domain,
            'upstream_domain', NEW.upstream_domain,
            'mapping_id', NEW.id
        ),
        'success'
    );
END;

-- 当代理映射删除时，回收域名到池中
CREATE TRIGGER IF NOT EXISTS recycle_domains_on_mapping_delete
AFTER DELETE ON proxy_mappings
FOR EACH ROW
WHEN OLD.is_manual = FALSE  -- 只对自动配对生效
BEGIN
    -- 回收下游域名
    UPDATE downstream_pool 
    SET status = 'available' 
    WHERE domain = OLD.downstream_domain AND status = 'used';
    
    -- 回收上游域名（如果健康的话）
    UPDATE upstream_pool 
    SET status = 'available' 
    WHERE domain = OLD.upstream_domain 
      AND status = 'used' 
      AND health_status IN ('healthy', 'unknown');
    
    -- 记录操作日志
    INSERT INTO domain_pool_logs (operation_type, details, result)
    VALUES (
        'recycle_domains',
        json_object(
            'downstream_domain', OLD.downstream_domain,
            'upstream_domain', OLD.upstream_domain,
            'mapping_id', OLD.id
        ),
        'success'
    );
END;

-- 健康检查结果更新触发器
CREATE TRIGGER IF NOT EXISTS update_upstream_health_on_check
AFTER INSERT ON health_check_history
FOR EACH ROW
BEGIN
    UPDATE upstream_pool 
    SET 
        health_status = NEW.status,
        last_check = NEW.check_time,
        response_time = NEW.response_time
    WHERE domain = NEW.domain;
END;

-- 插入一些示例数据（仅用于测试）
-- 注意：生产环境中应该删除这些示例数据

-- 示例下游域名
INSERT OR IGNORE INTO downstream_pool (domain, priority) VALUES 
('api1.example.com', 1),
('api2.example.com', 2),
('app1.example.com', 3),
('app2.example.com', 4),
('service1.example.com', 5);

-- 示例上游域名
INSERT OR IGNORE INTO upstream_pool (domain, priority, health_status, tags) VALUES 
('backend1.internal.com', 1, 'unknown', '["production", "api"]'),
('backend2.internal.com', 2, 'unknown', '["production", "api"]'),
('service-a.k8s.local', 3, 'unknown', '["microservice", "users"]'),
('service-b.k8s.local', 4, 'unknown', '["microservice", "orders"]'),
('legacy.oldserver.com', 5, 'unknown', '["legacy", "backup"]');

-- 版本控制信息
CREATE TABLE IF NOT EXISTS schema_migrations (
    version TEXT PRIMARY KEY,
    applied_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

INSERT OR IGNORE INTO schema_migrations (version, description) 
VALUES ('001_domain_pool_initial', '初始域名池管理表结构');