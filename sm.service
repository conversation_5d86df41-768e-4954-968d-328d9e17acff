﻿# 这是一个模板文件，实际的服务文件将由 deploy-linux.sh 脚本自动生成
# 模板说明：
# - WorkingDirectory 和 ExecStart 路径将自动检测并替换
# - User 和 Group 将根据系统环境自动设置
# - ReadWritePaths 将根据实际部署路径动态配置

# 使用方法：
# 1. 运行 ./deploy-linux.sh 进行智能部署
# 2. 脚本会自动生成正确的服务文件到 /etc/systemd/system/sm.service
# 3. 无需手动修改路径配置

# 如果需要手动安装，请先运行部署脚本生成正确的服务文件

[Unit]
Description=Secure Proxy Manager - 统一反向代理服务 (模板)
Documentation=https://github.com/your-repo/sm
After=network.target
Wants=network.target

[Service]
# 注意：以下路径将由部署脚本自动替换为实际路径
Type=simple
User=TEMPLATE_USER
Group=TEMPLATE_GROUP
WorkingDirectory=TEMPLATE_WORKING_DIR
ExecStart=TEMPLATE_EXEC_START
ExecReload=/bin/kill -HUP $MAINPID
Restart=on-failure
RestartSec=5
KillMode=mixed
TimeoutStopSec=30

# 强化安全设置
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=TEMPLATE_READ_WRITE_PATHS
ProtectKernelTunables=yes
ProtectKernelModules=yes
ProtectControlGroups=yes
ProtectKernelLogs=yes
ProtectHostname=yes
ProtectClock=yes
RestrictRealtime=yes
RestrictSUIDSGID=yes
RemoveIPC=yes
RestrictNamespaces=yes

# 系统调用过滤
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# 网络安全
PrivateNetwork=no
IPAddressDeny=any
IPAddressAllow=localhost
IPAddressAllow=10.0.0.0/8
IPAddressAllow=**********/12
IPAddressAllow=***********/16

# 环境变量
Environment=RUST_LOG=info
Environment=RUST_BACKTRACE=1
EnvironmentFile=-TEMPLATE_ENV_FILE

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
LimitCORE=0

# 内存和CPU限制
MemoryMax=2G
CPUQuota=200%

[Install]
WantedBy=multi-user.target
