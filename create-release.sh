#!/bin/bash
# ═══════════════════════════════════════════════════════════════
# 📦 SM智能代理系统 - 发布包创建脚本
# 🎯 自动打包所有必需文件，创建可分发的发布包
# ═══════════════════════════════════════════════════════════════

set -euo pipefail

# 颜色输出
RED='\033[1;31m'
GREEN='\033[1;32m'
YELLOW='\033[1;33m'
BLUE='\033[1;34m'
CYAN='\033[1;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 配置
VERSION=$(date +%Y%m%d_%H%M%S)
RELEASE_NAME="sm-release-${VERSION}"
RELEASE_DIR="releases/${RELEASE_NAME}"

echo -e "${GREEN}📦 SM智能代理系统 - 发布包创建${NC}"
echo -e "${BLUE}═══════════════════════════════════════${NC}"
echo -e "${WHITE}🎯 创建可分发的完整发布包${NC}"
echo -e "${WHITE}📅 版本: ${CYAN}${VERSION}${NC}"
echo ""

# 检查编译产物
if [ ! -f "target/release/sm" ]; then
    echo -e "${RED}❌ 未找到编译后的二进制文件${NC}"
    echo -e "${YELLOW}💡 请先运行: cargo build --release${NC}"
    exit 1
fi

# 创建发布目录
echo -e "${BLUE}📁 创建发布目录...${NC}"
mkdir -p "$RELEASE_DIR"

# 复制核心文件
echo -e "${BLUE}📋 复制核心文件...${NC}"

# 1. 二进制文件
echo -e "${CYAN}  • 复制二进制文件...${NC}"
cp target/release/sm "$RELEASE_DIR/"
chmod +x "$RELEASE_DIR/sm"

# 2. 配置文件
echo -e "${CYAN}  • 复制配置文件...${NC}"
mkdir -p "$RELEASE_DIR/config"
cp config/config.yaml "$RELEASE_DIR/config/"

# 3. 前端文件
echo -e "${CYAN}  • 复制前端文件...${NC}"
cp -r frontend "$RELEASE_DIR/"

# 4. 部署脚本
echo -e "${CYAN}  • 复制部署脚本...${NC}"
cp install.sh "$RELEASE_DIR/"
cp setup.sh "$RELEASE_DIR/"
cp optimize-server.sh "$RELEASE_DIR/"
cp fix-env.sh "$RELEASE_DIR/"
chmod +x "$RELEASE_DIR"/*.sh

# 5. 文档文件
echo -e "${CYAN}  • 复制文档文件...${NC}"
cp README.md "$RELEASE_DIR/" 2>/dev/null || echo "README.md not found, skipping"
cp LICENSE "$RELEASE_DIR/" 2>/dev/null || echo "LICENSE not found, skipping"

# 创建发布说明
echo -e "${BLUE}📝 创建发布说明...${NC}"
cat > "$RELEASE_DIR/RELEASE_NOTES.md" << EOF
# SM智能代理系统 - 发布包 v${VERSION}

## 📦 发布包内容

### 🎯 核心文件
- \`sm\` - 主程序二进制文件 ($(du -h target/release/sm | cut -f1))
- \`config/config.yaml\` - 主配置文件
- \`frontend/\` - Web管理界面文件

### 🚀 部署脚本
- \`install.sh\` - 万能安装启动器
- \`setup.sh\` - 主部署脚本
- \`optimize-server.sh\` - 服务器性能优化脚本
- \`fix-env.sh\` - 环境修复脚本

### 📚 文档
- \`README.md\` - 详细使用说明
- \`RELEASE_NOTES.md\` - 本发布说明

## 🚀 快速部署

### 方法1: 万能命令 (推荐)
\`\`\`bash
# 解压发布包
tar -xzf ${RELEASE_NAME}.tar.gz
cd ${RELEASE_NAME}

# 运行万能命令
sed -i '1s/^\xEF\xBB\xBF//' install.sh && ./install.sh
\`\`\`

### 方法2: 手动部署
\`\`\`bash
# 1. 复制文件到部署目录
sudo mkdir -p /opt/sm
sudo cp sm /opt/sm/
sudo cp -r config /opt/sm/
sudo cp -r frontend /opt/sm/

# 2. 设置权限
sudo chmod +x /opt/sm/sm
sudo chown -R proxy:proxy /opt/sm/

# 3. 创建systemd服务
sudo ./setup.sh
\`\`\`

## 📋 系统要求

### 最低要求
- **操作系统**: Linux (Debian/Ubuntu/CentOS)
- **内存**: 1GB RAM
- **磁盘**: 1GB 可用空间
- **网络**: 需要访问MongoDB

### 推荐配置
- **操作系统**: Debian 12 / Ubuntu 22.04
- **内存**: 4GB RAM
- **磁盘**: 10GB 可用空间
- **CPU**: 2核心以上

### 依赖服务
- **MongoDB**: 4.4+
- **Redis**: 6.0+ (可选)

## 🌐 访问地址

部署完成后访问：
- **Web管理界面**: http://your-server:1319
- **默认账户**: admin / admin888

## 📞 技术支持

如有问题，请查看 README.md 中的故障排除部分。

---
📅 发布时间: $(date)
🏗️ 编译环境: $(uname -a)
EOF

# 创建快速部署脚本
echo -e "${BLUE}🚀 创建快速部署脚本...${NC}"
cat > "$RELEASE_DIR/quick-deploy.sh" << 'EOF'
#!/bin/bash
# SM智能代理系统 - 快速部署脚本

echo "🚀 SM智能代理系统 - 快速部署"
echo "═══════════════════════════════════════"

# 检查权限
if [ "$(whoami)" != "root" ]; then
    echo "❌ 需要root权限"
    echo "💡 请使用: sudo $0"
    exit 1
fi

# 运行万能命令
echo "🔧 开始自动部署..."
sed -i '1s/^\xEF\xBB\xBF//' install.sh
chmod +x install.sh setup.sh optimize-server.sh fix-env.sh
./install.sh

echo "✅ 快速部署完成！"
echo "🌐 访问地址: http://$(hostname -I | awk '{print $1}'):1319"
echo "👤 默认账户: admin / admin888"
EOF
chmod +x "$RELEASE_DIR/quick-deploy.sh"

# 计算文件大小
echo -e "${BLUE}📊 计算发布包大小...${NC}"
TOTAL_SIZE=$(du -sh "$RELEASE_DIR" | cut -f1)

# 创建压缩包
echo -e "${BLUE}📦 创建压缩包...${NC}"
cd releases
tar -czf "${RELEASE_NAME}.tar.gz" "${RELEASE_NAME}/"
ARCHIVE_SIZE=$(du -sh "${RELEASE_NAME}.tar.gz" | cut -f1)
cd ..

# 创建校验和
echo -e "${BLUE}🔐 创建校验和...${NC}"
cd releases
sha256sum "${RELEASE_NAME}.tar.gz" > "${RELEASE_NAME}.tar.gz.sha256"
cd ..

echo ""
echo -e "${GREEN}🎉 发布包创建完成！${NC}"
echo -e "${BLUE}═══════════════════════════════════════${NC}"
echo ""
echo -e "${WHITE}📦 发布包信息:${NC}"
echo -e "${CYAN}  名称: ${RELEASE_NAME}${NC}"
echo -e "${CYAN}  目录大小: ${TOTAL_SIZE}${NC}"
echo -e "${CYAN}  压缩包大小: ${ARCHIVE_SIZE}${NC}"
echo -e "${CYAN}  位置: releases/${RELEASE_NAME}.tar.gz${NC}"
echo ""
echo -e "${WHITE}📋 包含文件:${NC}"
ls -la "$RELEASE_DIR" | while read line; do
    echo -e "${WHITE}  $line${NC}"
done
echo ""
echo -e "${WHITE}🚀 分发方式:${NC}"
echo -e "${CYAN}  1. 发送压缩包: releases/${RELEASE_NAME}.tar.gz${NC}"
echo -e "${CYAN}  2. 接收方解压: tar -xzf ${RELEASE_NAME}.tar.gz${NC}"
echo -e "${CYAN}  3. 运行部署: cd ${RELEASE_NAME} && sudo ./quick-deploy.sh${NC}"
echo ""
echo -e "${WHITE}🔐 校验和文件: releases/${RELEASE_NAME}.tar.gz.sha256${NC}"
echo -e "${YELLOW}💡 建议同时发送校验和文件以验证完整性${NC}"
