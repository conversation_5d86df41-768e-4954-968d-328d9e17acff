#!/bin/bash

# 前端问题检查脚本
# 自动检查常见的前端问题

echo "🔍 开始检查前端问题..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 错误计数
ERROR_COUNT=0
WARNING_COUNT=0

# 检查函数
check_error() {
    echo -e "${RED}❌ $1${NC}"
    ((ERROR_COUNT++))
}

check_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((WARNING_COUNT++))
}

check_ok() {
    echo -e "${GREEN}✅ $1${NC}"
}

check_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 进入前端目录
cd "$(dirname "$0")"

echo ""
echo "📁 当前目录: $(pwd)"

# 1. 检查JavaScript语法错误
echo ""
echo "🔧 检查JavaScript语法..."
for js_file in js/*.js; do
    if [[ -f "$js_file" ]]; then
        if node -c "$js_file" 2>/dev/null; then
            check_ok "语法检查通过: $js_file"
        else
            check_error "语法错误: $js_file"
            node -c "$js_file" 2>&1 | sed 's/^/    /'
        fi
    fi
done

# 2. 检查HTML中的getElementById引用
echo ""
echo "🔍 检查DOM元素引用..."
for html_file in *.html; do
    if [[ -f "$html_file" ]]; then
        echo "检查文件: $html_file"
        
        # 提取HTML中的所有id
        html_ids=$(grep -o 'id="[^"]*"' "$html_file" | sed 's/id="//g' | sed 's/"//g' | sort -u)
        
        # 检查对应的JS文件中是否有getElementById调用
        js_file="js/${html_file%.html}.js"
        if [[ -f "$js_file" ]]; then
            # 提取JS中的getElementById调用
            js_getelements=$(grep -o "getElementById(['\"][^'\"]*['\"])" "$js_file" | sed "s/getElementById(['\"]//g" | sed "s/['\"])//g" | sort -u)
            
            # 检查每个getElementById调用是否有对应的HTML元素
            while IFS= read -r element_id; do
                if [[ -n "$element_id" ]]; then
                    if echo "$html_ids" | grep -q "^$element_id$"; then
                        check_ok "元素引用正确: $element_id"
                    else
                        check_error "找不到元素: $element_id (在 $js_file 中引用)"
                    fi
                fi
            done <<< "$js_getelements"
        fi
    fi
done

# 3. 检查函数定义和调用
echo ""
echo "🔧 检查函数定义和调用..."
for js_file in js/*.js; do
    if [[ -f "$js_file" ]]; then
        echo "检查文件: $js_file"
        
        # 提取函数定义
        functions_defined=$(grep -o "function [a-zA-Z_][a-zA-Z0-9_]*" "$js_file" | sed 's/function //g' | sort -u)
        functions_defined+=$'\n'$(grep -o "[a-zA-Z_][a-zA-Z0-9_]* *=" "$js_file" | sed 's/ *=//g' | sort -u)
        functions_defined+=$'\n'$(grep -o "async [a-zA-Z_][a-zA-Z0-9_]*" "$js_file" | sed 's/async //g' | sort -u)
        
        # 检查HTML中的onclick调用
        html_file="${js_file#js/}"
        html_file="${html_file%.js}.html"
        
        if [[ -f "$html_file" ]]; then
            onclick_calls=$(grep -o 'onclick="[^"]*"' "$html_file" | sed 's/onclick="//g' | sed 's/".*//g' | sed 's/(.*//' | sort -u)
            
            while IFS= read -r func_name; do
                if [[ -n "$func_name" ]]; then
                    if echo "$functions_defined" | grep -q "^$func_name$"; then
                        check_ok "函数定义正确: $func_name"
                    else
                        check_error "找不到函数定义: $func_name (在 $html_file 中调用)"
                    fi
                fi
            done <<< "$onclick_calls"
        fi
    fi
done

# 4. 检查API调用是否使用认证
echo ""
echo "🔐 检查API认证..."
for js_file in js/*.js; do
    if [[ -f "$js_file" ]]; then
        # 检查是否有未认证的fetch调用
        unauth_fetches=$(grep -n "await fetch(" "$js_file" | grep -v "AuthGuard.authenticatedFetch")
        
        if [[ -n "$unauth_fetches" ]]; then
            check_warning "发现未认证的API调用在 $js_file:"
            echo "$unauth_fetches" | sed 's/^/    /'
        else
            check_ok "API认证检查通过: $js_file"
        fi
    fi
done

# 5. 检查CSS文件引用
echo ""
echo "🎨 检查CSS文件引用..."
for html_file in *.html; do
    if [[ -f "$html_file" ]]; then
        css_refs=$(grep -o 'href="css/[^"]*"' "$html_file" | sed 's/href="//g' | sed 's/"//g')
        
        while IFS= read -r css_file; do
            if [[ -n "$css_file" && -f "$css_file" ]]; then
                check_ok "CSS文件存在: $css_file"
            elif [[ -n "$css_file" ]]; then
                check_error "CSS文件不存在: $css_file (在 $html_file 中引用)"
            fi
        done <<< "$css_refs"
    fi
done

# 6. 检查JavaScript文件引用
echo ""
echo "⚡ 检查JavaScript文件引用..."
for html_file in *.html; do
    if [[ -f "$html_file" ]]; then
        js_refs=$(grep -o 'src="js/[^"]*"' "$html_file" | sed 's/src="//g' | sed 's/"//g')
        
        while IFS= read -r js_file; do
            if [[ -n "$js_file" && -f "$js_file" ]]; then
                check_ok "JS文件存在: $js_file"
            elif [[ -n "$js_file" ]]; then
                check_error "JS文件不存在: $js_file (在 $html_file 中引用)"
            fi
        done <<< "$js_refs"
    fi
done

# 总结
echo ""
echo "📊 检查结果总结:"
echo "================="
if [[ $ERROR_COUNT -eq 0 && $WARNING_COUNT -eq 0 ]]; then
    check_ok "所有检查都通过了！前端代码看起来很健康。"
else
    if [[ $ERROR_COUNT -gt 0 ]]; then
        check_error "发现 $ERROR_COUNT 个错误需要修复"
    fi
    if [[ $WARNING_COUNT -gt 0 ]]; then
        check_warning "发现 $WARNING_COUNT 个警告需要注意"
    fi
fi

echo ""
echo "🔧 建议的修复步骤:"
echo "1. 修复所有标记为❌的错误"
echo "2. 检查所有标记为⚠️的警告"
echo "3. 确保所有API调用都使用认证"
echo "4. 验证DOM元素ID的一致性"
echo "5. 重新运行此脚本确认修复"

exit $ERROR_COUNT
