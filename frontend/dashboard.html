<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控仪表板 - SM智能代理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        .dashboard-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .widget {
            background: #2a2a2a;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #404040;
            transition: all 0.3s ease;
        }

        .widget:hover {
            border-color: #4CAF50;
            transform: translateY(-2px);
        }

        .widget-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .widget-title {
            font-size: 1.2rem;
            color: #4CAF50;
            font-weight: 600;
        }

        .widget-icon {
            font-size: 1.5rem;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #e0e0e0;
            margin-bottom: 8px;
        }

        .metric-label {
            color: #b0b0b0;
            font-size: 0.9rem;
        }

        .metric-change {
            font-size: 0.8rem;
            margin-top: 4px;
        }

        .metric-up {
            color: #4CAF50;
        }

        .metric-down {
            color: #f44336;
        }

        .chart-container {
            height: 200px;
            margin-top: 16px;
            background: #333;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        .status-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #444;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #4CAF50;
        }

        .status-warning {
            background: #ff9800;
        }

        .status-offline {
            background: #f44336;
        }

        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto;
            position: relative;
        }

        .progress-ring svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .progress-ring circle {
            fill: none;
            stroke-width: 8;
        }

        .progress-ring .background {
            stroke: #444;
        }

        .progress-ring .progress {
            stroke: #4CAF50;
            stroke-linecap: round;
            transition: stroke-dasharray 0.3s ease;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            font-weight: bold;
            color: #4CAF50;
        }

        .activity-feed {
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: flex-start;
            padding: 12px 0;
            border-bottom: 1px solid #444;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
        }

        .activity-success {
            background: #4CAF50;
            color: white;
        }

        .activity-warning {
            background: #ff9800;
            color: white;
        }

        .activity-error {
            background: #f44336;
            color: white;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            color: #e0e0e0;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .activity-time {
            color: #888;
            font-size: 0.8rem;
        }

        .quick-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 10px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .action-btn:hover {
            background: #45a049;
            transform: translateY(-1px);
        }

        .action-btn.secondary {
            background: #666;
        }

        .action-btn.secondary:hover {
            background: #777;
        }

        .action-btn.danger {
            background: #f44336;
        }

        .action-btn.danger:hover {
            background: #da190b;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 页面标题和统一导航栏 -->
        <div class="widget">
            <h1 style="color: #4CAF50; margin-bottom: 8px;">📊 系统监控仪表板</h1>
            <p style="color: #b0b0b0; margin: 0;">实时监控SM智能代理系统运行状态和性能指标</p>
        </div>

        <!-- 核心指标 -->
        <div class="dashboard-grid">
            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">总请求数</div>
                    <div class="widget-icon">📈</div>
                </div>
                <div class="metric-value" id="totalRequests">0</div>
                <div class="metric-label">今日请求</div>
                <div class="metric-change metric-up" id="requestsChange">+0%</div>
            </div>

            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">活跃域名</div>
                    <div class="widget-icon">🌐</div>
                </div>
                <div class="metric-value" id="activeDomains">0</div>
                <div class="metric-label">映射域名</div>
                <div class="metric-change metric-up" id="domainsChange">+0</div>
            </div>

            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">SSL证书</div>
                    <div class="widget-icon">🔒</div>
                </div>
                <div class="metric-value" id="sslCertificates">0</div>
                <div class="metric-label">有效证书</div>
                <div class="metric-change metric-warning" id="sslWarning">0 即将过期</div>
            </div>

            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">缓存命中率</div>
                    <div class="widget-icon">⚡</div>
                </div>
                <div class="progress-ring">
                    <svg>
                        <circle class="background" cx="60" cy="60" r="52"></circle>
                        <circle class="progress" cx="60" cy="60" r="52" id="cacheProgressCircle"></circle>
                    </svg>
                    <div class="progress-text" id="cacheHitRate">0%</div>
                </div>
            </div>
        </div>

        <!-- 系统状态和活动 -->
        <div class="dashboard-grid">
            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">系统状态</div>
                    <div class="widget-icon">🖥️</div>
                </div>
                <ul class="status-list">
                    <li class="status-item">
                        <div style="display: flex; align-items: center;">
                            <div class="status-indicator status-online"></div>
                            <span>代理服务</span>
                        </div>
                        <span style="color: #4CAF50;">运行中</span>
                    </li>
                    <li class="status-item">
                        <div style="display: flex; align-items: center;">
                            <div class="status-indicator status-online"></div>
                            <span>SSL管理</span>
                        </div>
                        <span style="color: #4CAF50;">正常</span>
                    </li>
                    <li class="status-item">
                        <div style="display: flex; align-items: center;">
                            <div class="status-indicator status-warning"></div>
                            <span>递归发现</span>
                        </div>
                        <span style="color: #ff9800;">部分异常</span>
                    </li>
                    <li class="status-item">
                        <div style="display: flex; align-items: center;">
                            <div class="status-indicator status-online"></div>
                            <span>数据库</span>
                        </div>
                        <span style="color: #4CAF50;">连接正常</span>
                    </li>
                </ul>
            </div>

            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">最近活动</div>
                    <div class="widget-icon">📋</div>
                </div>
                <div class="activity-feed" id="activityFeed">
                    <!-- 动态加载活动记录 -->
                </div>
            </div>

            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">快速操作</div>
                    <div class="widget-icon">⚡</div>
                </div>
                <div class="quick-actions">
                    <button class="action-btn" onclick="triggerDiscovery()">触发发现</button>
                    <button class="action-btn secondary" onclick="clearCache()">清空缓存</button>
                    <button class="action-btn secondary" onclick="refreshStats()">刷新数据</button>
                    <a href="auto-proxy.html" class="action-btn">管理配置</a>
                    <a href="ssl-monitor.html" class="action-btn">SSL监控</a>
                    <button class="action-btn danger" onclick="restartService()">重启服务</button>
                </div>
            </div>

            <div class="widget">
                <div class="widget-header">
                    <div class="widget-title">性能图表</div>
                    <div class="widget-icon">📊</div>
                </div>
                <div class="chart-container">
                    <div>性能图表功能开发中</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class Dashboard {
            constructor() {
                this.init();
            }

            async init() {
                await this.loadDashboardData();
                this.setupRealTimeUpdates();
            }

            async loadDashboardData() {
                try {
                    // 加载统计数据
                    const statsResponse = await fetch('/api/auto-proxy/statistics');
                    const statsResult = await statsResponse.json();
                    
                    if (statsResult.success) {
                        this.updateMetrics(statsResult.data);
                    }

                    // 加载活动记录
                    this.loadActivityFeed();
                } catch (error) {
                    console.error('加载仪表板数据失败:', error);
                }
            }

            updateMetrics(stats) {
                // 更新核心指标
                document.getElementById('totalRequests').textContent = this.formatNumber(stats.total_requests || 0);
                document.getElementById('activeDomains').textContent = stats.active_mappings || 0;
                document.getElementById('sslCertificates').textContent = stats.valid_certificates || 0;
                
                // 更新缓存命中率
                const hitRate = (stats.cache_hit_rate * 100 || 0).toFixed(1);
                document.getElementById('cacheHitRate').textContent = hitRate + '%';
                this.updateProgressRing(hitRate);

                // 更新变化指标
                document.getElementById('requestsChange').textContent = '+12.5%';
                document.getElementById('domainsChange').textContent = '+' + (stats.total_mappings - stats.active_mappings);
                
                // SSL警告
                const expiringSoon = (stats.total_certificates - stats.valid_certificates);
                document.getElementById('sslWarning').textContent = expiringSoon + ' 即将过期';
            }

            updateProgressRing(percentage) {
                const circle = document.getElementById('cacheProgressCircle');
                const radius = 52;
                const circumference = 2 * Math.PI * radius;
                const offset = circumference - (percentage / 100) * circumference;
                
                circle.style.strokeDasharray = circumference;
                circle.style.strokeDashoffset = offset;
            }

            loadActivityFeed() {
                const activities = [
                    {
                        type: 'success',
                        icon: '✓',
                        title: 'SSL证书自动续期成功',
                        time: '2分钟前'
                    },
                    {
                        type: 'success',
                        icon: '🌐',
                        title: '新增域名映射: example.com',
                        time: '5分钟前'
                    },
                    {
                        type: 'warning',
                        icon: '⚠',
                        title: '递归发现遇到超时',
                        time: '10分钟前'
                    },
                    {
                        type: 'success',
                        icon: '🔄',
                        title: '缓存清理完成',
                        time: '15分钟前'
                    },
                    {
                        type: 'error',
                        icon: '✗',
                        title: 'DNS验证失败: test.com',
                        time: '20分钟前'
                    }
                ];

                const feed = document.getElementById('activityFeed');
                feed.innerHTML = '';

                activities.forEach(activity => {
                    const item = document.createElement('div');
                    item.className = 'activity-item';
                    item.innerHTML = `
                        <div class="activity-icon activity-${activity.type}">
                            ${activity.icon}
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">${activity.title}</div>
                            <div class="activity-time">${activity.time}</div>
                        </div>
                    `;
                    feed.appendChild(item);
                });
            }

            setupRealTimeUpdates() {
                // 每30秒更新一次数据
                setInterval(() => {
                    this.loadDashboardData();
                }, 30000);
            }

            formatNumber(num) {
                if (num >= 1000000) {
                    return (num / 1000000).toFixed(1) + 'M';
                } else if (num >= 1000) {
                    return (num / 1000).toFixed(1) + 'K';
                }
                return num.toString();
            }
        }

        // 全局函数
        async function triggerDiscovery() {
            try {
                const response = await fetch('/api/auto-proxy/recursive/trigger', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    alert('递归发现已触发');
                } else {
                    alert('触发失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                alert('触发失败: ' + error.message);
            }
        }

        function clearCache() {
            if (confirm('确定要清空所有缓存吗？')) {
                alert('清空缓存功能开发中');
            }
        }

        function refreshStats() {
            dashboard.loadDashboardData();
        }

        function restartService() {
            if (confirm('确定要重启代理服务吗？这将导致短暂的服务中断。')) {
                alert('重启服务功能开发中');
            }
        }

        // 初始化仪表板
        const dashboard = new Dashboard();
    </script>
    <script src="js/auth-guard.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/navigation.js"></script>
</body>
</html>
