/** * 登录页面专用JavaScript */document.addEventListener('DOMContentLoaded',function(){initLoginPage();});function initLoginPage(){document.getElementById('loginForm').addEventListener('submit',async (e) =>{e.preventDefault();await performLogin();});document.getElementById('username').focus();if (window.AuthGuard && window.AuthGuard.isUserLoggedIn()){window.location.href = 'domains.html';}}async function performLogin(){const username = document.getElementById('username').value.trim();const password = document.getElementById('password').value;const loginButton = document.getElementById('loginButton');if (!username || !password){showError('请输入用户名和密码');return;}loginButton.disabled = true;loginButton.innerHTML = '<span class="loading"></span>登录中...';hideMessages();try{const response = await fetch('/api/auth/login',{method: 'POST',headers:{'Content-Type': 'application/json'},body: JSON.stringify({username,password})});const result = await response.json();if (result.success && result.data && result.data.token){localStorage.setItem('auth_token',result.data.token);showSuccess('登录成功！正在跳转...');const shouldRedirect = window.AuthGuard && window.AuthGuard.handleLoginSuccess();if (!shouldRedirect){setTimeout(() =>{window.location.href = 'domains.html';},1500);}}else{showError('登录失败: ' + (result.error || '用户名或密码错误'));}}catch (error){showError('登录请求失败: ' + error.message);}finally{loginButton.disabled = false;loginButton.innerHTML = '登录';}}function showError(message){const errorDiv = document.getElementById('errorMessage');errorDiv.textContent = message;errorDiv.style.display = 'block';document.getElementById('successMessage').style.display = 'none';}function showSuccess(message){const successDiv = document.getElementById('successMessage');successDiv.textContent = message;successDiv.style.display = 'block';document.getElementById('errorMessage').style.display = 'none';}function hideMessages(){document.getElementById('errorMessage').style.display = 'none';document.getElementById('successMessage').style.display = 'none';}