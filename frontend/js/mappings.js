// 代理映射管理JavaScript

let currentEditId = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 认证检查由auth-guard.js处理，直接加载数据
    loadMappings();
    loadStats();
    
    // 设置搜索和过滤事件
    document.getElementById('searchMappings').addEventListener('input', filterMappings);
    document.getElementById('statusFilter').addEventListener('change', filterMappings);
});

// 添加映射
async function addMapping() {
    const downstream = document.getElementById('mappingDownstream').value.trim();
    const upstream = document.getElementById('mappingUpstream').value.trim();
    const enableSSL = document.getElementById('enableSSL').checked;

    if (!downstream || !upstream) {
        showMessage('请填写完整的域名信息', 'error');
        return;
    }

    // 简单的域名格式验证
    if (!isValidDomain(downstream) || !isValidDomain(upstream)) {
        showMessage('请输入有效的域名格式', 'error');
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/mappings', {
            method: 'POST',
            body: JSON.stringify({
                downstream_domain: downstream,
                upstream_domain: upstream,
                ssl_enabled: enableSSL
            })
        });

        const result = await response.json();
        if (result.success) {
            showMessage('映射添加成功', 'success');
            clearMappingForm();
            loadMappings();
            loadStats();
        } else {
            // 后端会返回重复检测的错误信息
            showMessage('添加失败: ' + (result.error || result.message || '未知错误'), 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 加载映射列表
async function loadMappings() {
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/mappings');
        const result = await response.json();
        
        if (result.success) {
            displayMappings(result.data || []);
        } else {
            showMessage('加载映射失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('加载映射失败:', error);
        document.getElementById('mappingsTableBody').innerHTML = 
            '<tr><td colspan="8" style="text-align: center; color: #dc3545;">加载失败</td></tr>';
    }
}

// 显示映射列表
function displayMappings(mappings) {
    const tbody = document.getElementById('mappingsTableBody');
    
    if (mappings.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666;">暂无映射数据</td></tr>';
        return;
    }

    tbody.innerHTML = mappings.map(mapping => `
        <tr>
            <td>${mapping.downstream_domain}</td>
            <td>${mapping.upstream_domain}</td>
            <td>
                <span class="status-indicator ${getStatusClass(mapping.status)}"></span>
                ${getStatusText(mapping.status)}
            </td>
            <td>
                <span class="status-indicator ${mapping.ssl_enabled ? 'status-active' : 'status-inactive'}"></span>
                ${mapping.ssl_enabled ? '启用' : '禁用'}
            </td>
            <td>${mapping.request_count || 0}</td>
            <td>${formatDate(mapping.last_accessed)}</td>
            <td>${mapping.avg_response_time || 0}ms</td>
            <td>
                <button class="btn btn-secondary" onclick="editMapping('${mapping.id}')" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                <button class="btn btn-danger" onclick="deleteMapping('${mapping.id}')" style="padding: 6px 12px; font-size: 12px;">删除</button>
            </td>
        </tr>
    `).join('');
}

// 编辑映射
async function editMapping(mappingId) {
    try {
        const response = await window.AuthGuard.authenticatedFetch(`/api/auto-proxy/mappings/${mappingId}`);
        const result = await response.json();
        
        if (result.success) {
            const mapping = result.data;
            currentEditId = mappingId;
            
            document.getElementById('editDownstream').value = mapping.downstream_domain;
            document.getElementById('editUpstream').value = mapping.upstream_domain;
            document.getElementById('editSSL').checked = mapping.ssl_enabled;
            
            document.getElementById('editModal').style.display = 'block';
        } else {
            showMessage('获取映射信息失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 保存映射
async function saveMapping() {
    if (!currentEditId) return;

    const downstream = document.getElementById('editDownstream').value.trim();
    const upstream = document.getElementById('editUpstream').value.trim();
    const enableSSL = document.getElementById('editSSL').checked;

    if (!downstream) {
        showMessage('请填写下游域名', 'error');
        return;
    }

    if (!upstream) {
        showMessage('请填写上游域名', 'error');
        return;
    }

    if (!isValidDomain(downstream)) {
        showMessage('请输入有效的下游域名格式', 'error');
        return;
    }

    if (!isValidDomain(upstream)) {
        showMessage('请输入有效的上游域名格式', 'error');
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch(`/api/auto-proxy/mappings/${currentEditId}`, {
            method: 'PUT',
            body: JSON.stringify({
                downstream_domain: downstream,
                upstream_domain: upstream,
                ssl_enabled: enableSSL
            })
        });

        const result = await response.json();
        if (result.success) {
            showMessage('映射更新成功', 'success');
            closeEditModal();
            loadMappings();
            loadStats();
        } else {
            showMessage('更新失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 删除映射
async function deleteMapping(mappingId) {
    if (!confirm('确定要删除这个映射吗？')) {
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch(`/api/auto-proxy/mappings/${mappingId}`, {
            method: 'DELETE'
        });

        const result = await response.json();
        if (result.success) {
            showMessage('映射删除成功', 'success');
            loadMappings();
            loadStats();
        } else {
            showMessage('删除失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 加载统计数据
async function loadStats() {
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/statistics');
        const result = await response.json();
        
        if (result.success) {
            const data = result.data || {};
            document.getElementById('activeMappings').textContent = data.active_proxies || 0;
            document.getElementById('totalRequests').textContent = data.requests_today || 0;
            document.getElementById('successRate').textContent = (data.success_rate || 0) + '%';
            document.getElementById('avgResponseTime').textContent = (data.avg_response_time || 0) + 'ms';
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

// 过滤映射
function filterMappings() {
    const searchTerm = document.getElementById('searchMappings').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const rows = document.querySelectorAll('#mappingsTableBody tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length < 8) return; // 跳过空行

        const downstream = cells[0].textContent.toLowerCase();
        const upstream = cells[1].textContent.toLowerCase();
        const status = cells[2].textContent.toLowerCase();
        
        const matchesSearch = downstream.includes(searchTerm) || upstream.includes(searchTerm);
        const matchesStatus = !statusFilter || status.includes(statusFilter);
        
        row.style.display = matchesSearch && matchesStatus ? '' : 'none';
    });
}

// 清空映射表单
function clearMappingForm() {
    document.getElementById('mappingDownstream').value = '';
    document.getElementById('mappingUpstream').value = '';
    document.getElementById('enableSSL').checked = false;
}

// 清空所有映射
async function clearAllMappings() {
    if (!confirm('确定要清空所有映射吗？此操作不可恢复！')) {
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/mappings/clear', {
            method: 'DELETE'
        });

        const result = await response.json();
        if (result.success) {
            showMessage('所有映射已清空', 'success');
            loadMappings();
            loadStats();
        } else {
            showMessage('清空失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 关闭编辑模态框
function closeEditModal() {
    document.getElementById('editModal').style.display = 'none';
    currentEditId = null;
}

// 获取状态样式类
function getStatusClass(status) {
    switch (status) {
        case 'active': return 'status-active';
        case 'error': return 'status-inactive';
        default: return 'status-warning';
    }
}

// 获取状态文本
function getStatusText(status) {
    switch (status) {
        case 'active': return '活跃';
        case 'inactive': return '非活跃';
        case 'error': return '错误';
        default: return '未知';
    }
}

// 验证域名格式
function isValidDomain(domain) {
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.([a-zA-Z]{2,}\.)*[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
}

// 显示消息
function showMessage(message, type = 'success') {
    const messageArea = document.getElementById('messageArea');
    const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
    
    messageArea.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
    
    // 3秒后自动隐藏
    setTimeout(() => {
        messageArea.innerHTML = '';
    }, 3000);
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('editModal');
    if (event.target === modal) {
        closeEditModal();
    }
}
