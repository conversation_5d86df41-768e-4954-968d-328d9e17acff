/**
 * 登录页面专用JavaScript
 */

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initLoginPage();
});

// 初始化登录页面
function initLoginPage() {
    // 登录表单处理
    document.getElementById('loginForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        await performLogin();
    });

    // 页面加载时聚焦到用户名输入框
    document.getElementById('username').focus();

    // 如果用户已经登录，直接跳转
    if (window.AuthGuard && window.AuthGuard.isUserLoggedIn()) {
        window.location.href = 'domains.html';
    }
}

// 执行登录
async function performLogin() {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const loginButton = document.getElementById('loginButton');

    if (!username || !password) {
        showError('请输入用户名和密码');
        return;
    }

    // 显示加载状态
    loginButton.disabled = true;
    loginButton.innerHTML = '<span class="loading"></span>登录中...';
    hideMessages();

    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const result = await response.json();

        if (result.success && result.data && result.data.token) {
            // 登录成功
            localStorage.setItem('auth_token', result.data.token);
            
            showSuccess('登录成功！正在跳转...');
            
            // 检查是否需要跳转到之前的页面
            const shouldRedirect = window.AuthGuard && window.AuthGuard.handleLoginSuccess();
            
            if (!shouldRedirect) {
                // 没有返回页面，跳转到域名管理页面
                setTimeout(() => {
                    window.location.href = 'domains.html';
                }, 1500);
            }
        } else {
            showError('登录失败: ' + (result.error || '用户名或密码错误'));
        }
    } catch (error) {
        showError('登录请求失败: ' + error.message);
    } finally {
        // 恢复按钮状态
        loginButton.disabled = false;
        loginButton.innerHTML = '登录';
    }
}

// 显示错误消息
function showError(message) {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
    document.getElementById('successMessage').style.display = 'none';
}

// 显示成功消息
function showSuccess(message) {
    const successDiv = document.getElementById('successMessage');
    successDiv.textContent = message;
    successDiv.style.display = 'block';
    document.getElementById('errorMessage').style.display = 'none';
}

// 隐藏所有消息
function hideMessages() {
    document.getElementById('errorMessage').style.display = 'none';
    document.getElementById('successMessage').style.display = 'none';
}
