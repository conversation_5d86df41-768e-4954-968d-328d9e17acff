/** * 首页专用JavaScript */document.addEventListener('DOMContentLoaded',function(){initIndexPage();});function initIndexPage(){loadSystemStatus();loadStats();}async function loadSystemStatus(){try{const response = await fetch('/api/status');const result = await response.json();if (result.success){document.getElementById('systemStatus').textContent = '正常';document.getElementById('systemStatus').style.color = '#4CAF50';}else{document.getElementById('systemStatus').textContent = '异常';document.getElementById('systemStatus').style.color = '#f44336';}}catch (error){document.getElementById('systemStatus').textContent = '离线';document.getElementById('systemStatus').style.color = '#ff9800';}}async function loadStats(){try{let response;const token = localStorage.getItem('auth_token');if (token){response = await fetch('/api/auto-proxy/statistics',{headers:{'Authorization': `Bearer ${token}`}});}else{updateStatsDisplay(0,0,0);return;}const result = await response.json();if (result.success && result.data){const stats = result.data;updateStatsDisplay( stats.total_requests || 0,stats.active_mappings || 0,stats.valid_certificates || 0 );}else{updateStatsDisplay(0,0,0);}}catch (error){console.warn('加载统计数据失败:',error);updateStatsDisplay(0,0,0);}}function updateStatsDisplay(requests,domains,certificates){const totalDomainsEl = document.getElementById('totalDomains');const activeMappingsEl = document.getElementById('activeMappings');if (totalDomainsEl){totalDomainsEl.textContent = domains || 0;}if (activeMappingsEl){activeMappingsEl.textContent = formatNumber(requests) || 0;}}function formatNumber(num){if (num >= 1000000){return (num / 1000000).toFixed(1) + 'M';}else if (num >= 1000){return (num / 1000).toFixed(1) + 'K';}return num.toString();}setInterval(() =>{loadSystemStatus();loadStats();},30000);