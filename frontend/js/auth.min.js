class AuthManager{constructor(){this.token = localStorage.getItem('auth_token');this.isLoggedIn = !!this.token;}async login(username,password){try{const response = await fetch('/api/auth/login',{method: 'POST',headers:{'Content-Type': 'application/json',},body: JSON.stringify({username,password})});const result = await response.json();if (result.success && result.data && result.data.token){this.token = result.data.token;this.isLoggedIn = true;localStorage.setItem('auth_token',this.token);return{success: true};}else{return{success: false,message: result.message || '登录失败'};}}catch (error){console.error('登录错误:',error);return{success: false,message: '网络错误'};}}async logout(){try{if (this.token){await fetch('/api/auth/logout',{method: 'POST',headers:{'Authorization': `Bear<PERSON> ${this.token}`,'Content-Type': 'application/json',}});}}catch (error){console.error('登出错误:',error);}finally{this.token = null;this.isLoggedIn = false;localStorage.removeItem('auth_token');}}getAuthHeaders(){if (this.token){return{'Authorization': `Bearer ${this.token}`,'Content-Type': 'application/json'};}return{'Content-Type': 'application/json'};}async authenticatedFetch(url,options ={}){const headers ={...this.getAuthHeaders(),...(options.headers ||{})};const response = await fetch(url,{...options,headers});if (response.status === 401){this.token = null;this.isLoggedIn = false;localStorage.removeItem('auth_token');if (!window.location.pathname.includes('index.html') && !window.location.pathname.includes('login.html')){if (window.SM && window.SM.showNotification){window.SM.showNotification('登录已过期，请重新登录','warning',3000);}setTimeout(() =>{window.location.href = 'index.html';},1000);}}return response;}showLoginModal(){const modal = document.createElement('div');modal.className = 'auth-modal';modal.innerHTML = ` <div class="auth-modal-content"> <h3>请登录</h3> <form id="loginForm"> <div class="form-group"> <label>用户名:</label> <input type="text" id="loginUsername" placeholder="请输入用户名" required> </div> <div class="form-group"> <label>密码:</label> <input type="password" id="loginPassword" placeholder="请输入密码" required> </div> <div class="form-actions"> <button type="submit">登录</button> </div> </form> <div id="loginMessage"></div> </div> `;const style = document.createElement('style');style.textContent = ` .auth-modal{position: fixed;top: 0;left: 0;width: 100%;height: 100%;background: rgba(0,0,0,0.8);display: flex;align-items: center;justify-content: center;z-index: 10000;}.auth-modal-content{background: #2d2d2d;padding: 30px;border-radius: 10px;width: 300px;color: #e0e0e0;}.auth-modal h3{margin-bottom: 20px;color: #4CAF50;text-align: center;}.form-group{margin-bottom: 15px;}.form-group label{display: block;margin-bottom: 5px;color: #b0b0b0;}.form-group input{width: 100%;padding: 8px;border: 1px solid #555;border-radius: 4px;background: #1a1a1a;color: #e0e0e0;box-sizing: border-box;}.form-actions{text-align: center;margin-top: 20px;}.form-actions button{background: #4CAF50;color: white;border: none;padding: 10px 20px;border-radius: 4px;cursor: pointer;}.form-actions button:hover{background: #45a049;}#loginMessage{margin-top: 10px;text-align: center;color: #dc3545;}`;document.head.appendChild(style);document.body.appendChild(modal);document.getElementById('loginForm').addEventListener('submit',async (e) =>{e.preventDefault();const username = document.getElementById('loginUsername').value;const password = document.getElementById('loginPassword').value;const result = await this.login(username,password);if (result.success){document.body.removeChild(modal);document.head.removeChild(style);window.location.reload();}else{document.getElementById('loginMessage').textContent = result.message;}});}async autoLogin(){return this.isLoggedIn;}}window.authManager = new AuthManager();