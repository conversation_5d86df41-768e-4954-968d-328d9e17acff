let currentEditId = null;document.addEventListener('DOMContentLoaded',async function(){loadMappings();loadStats();document.getElementById('searchMappings').addEventListener('input',filterMappings);document.getElementById('statusFilter').addEventListener('change',filterMappings);});async function addMapping(){const downstream = document.getElementById('mappingDownstream').value.trim();const upstream = document.getElementById('mappingUpstream').value.trim();const enableSSL = document.getElementById('enableSSL').checked;if (!downstream || !upstream){showMessage('请填写完整的域名信息','error');return;}if (!isValidDomain(downstream) || !isValidDomain(upstream)){showMessage('请输入有效的域名格式','error');return;}try{const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/mappings',{method: 'POST',body: JSON.stringify({downstream_domain: downstream,upstream_domain: upstream,ssl_enabled: enableSSL})});const result = await response.json();if (result.success){showMessage('映射添加成功','success');clearMappingForm();loadMappings();loadStats();}else{showMessage('添加失败: ' + result.message,'error');}}catch (error){showMessage('网络错误: ' + error.message,'error');}}async function loadMappings(){try{const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/mappings');const result = await response.json();if (result.success){displayMappings(result.data || []);}else{showMessage('加载映射失败: ' + result.message,'error');}}catch (error){console.error('加载映射失败:',error);document.getElementById('mappingsTableBody').innerHTML = '<tr><td colspan="8" style="text-align: center;color: #dc3545;">加载失败</td></tr>';}}function displayMappings(mappings){const tbody = document.getElementById('mappingsTableBody');if (mappings.length === 0){tbody.innerHTML = '<tr><td colspan="8" style="text-align: center;color: #666;">暂无映射数据</td></tr>';return;}tbody.innerHTML = mappings.map(mapping => ` <tr> <td>${mapping.downstream_domain}</td> <td>${mapping.upstream_domain}</td> <td> <span class="status-indicator ${getStatusClass(mapping.status)}"></span> ${getStatusText(mapping.status)}</td> <td> <span class="status-indicator ${mapping.ssl_enabled ? 'status-active' : 'status-inactive'}"></span> ${mapping.ssl_enabled ? '启用' : '禁用'}</td> <td>${mapping.request_count || 0}</td> <td>${formatDate(mapping.last_accessed)}</td> <td>${mapping.avg_response_time || 0}ms</td> <td> <button class="btn btn-secondary" onclick="editMapping('${mapping.id}')" style="padding: 6px 12px;font-size: 12px;">编辑</button> <button class="btn btn-danger" onclick="deleteMapping('${mapping.id}')" style="padding: 6px 12px;font-size: 12px;">删除</button> </td> </tr> `).join('');}async function editMapping(mappingId){try{const response = await window.AuthGuard.authenticatedFetch(`/api/auto-proxy/mappings/${mappingId}`);const result = await response.json();if (result.success){const mapping = result.data;currentEditId = mappingId;document.getElementById('editDownstream').value = mapping.downstream_domain;document.getElementById('editUpstream').value = mapping.upstream_domain;document.getElementById('editSSL').checked = mapping.ssl_enabled;document.getElementById('editModal').style.display = 'block';}else{showMessage('获取映射信息失败: ' + result.message,'error');}}catch (error){showMessage('网络错误: ' + error.message,'error');}}async function saveMapping(){if (!currentEditId) return;const upstream = document.getElementById('editUpstream').value.trim();const enableSSL = document.getElementById('editSSL').checked;if (!upstream){showMessage('请填写上游域名','error');return;}if (!isValidDomain(upstream)){showMessage('请输入有效的域名格式','error');return;}try{const response = await window.AuthGuard.authenticatedFetch(`/api/auto-proxy/mappings/${currentEditId}`,{method: 'PUT',body: JSON.stringify({upstream_domain: upstream,ssl_enabled: enableSSL})});const result = await response.json();if (result.success){showMessage('映射更新成功','success');closeEditModal();loadMappings();loadStats();}else{showMessage('更新失败: ' + result.message,'error');}}catch (error){showMessage('网络错误: ' + error.message,'error');}}async function deleteMapping(mappingId){if (!confirm('确定要删除这个映射吗？')){return;}try{const response = await window.AuthGuard.authenticatedFetch(`/api/auto-proxy/mappings/${mappingId}`,{method: 'DELETE'});const result = await response.json();if (result.success){showMessage('映射删除成功','success');loadMappings();loadStats();}else{showMessage('删除失败: ' + result.message,'error');}}catch (error){showMessage('网络错误: ' + error.message,'error');}}async function loadStats(){try{const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/statistics');const result = await response.json();if (result.success){const data = result.data ||{};document.getElementById('activeMappings').textContent = data.active_proxies || 0;document.getElementById('totalRequests').textContent = data.requests_today || 0;document.getElementById('successRate').textContent = (data.success_rate || 0) + '%';document.getElementById('avgResponseTime').textContent = (data.avg_response_time || 0) + 'ms';}}catch (error){console.error('加载统计数据失败:',error);}}function filterMappings(){const searchTerm = document.getElementById('searchMappings').value.toLowerCase();const statusFilter = document.getElementById('statusFilter').value;const rows = document.querySelectorAll('#mappingsTableBody tr');rows.forEach(row =>{const cells = row.querySelectorAll('td');if (cells.length < 8) return;const downstream = cells[0].textContent.toLowerCase();const upstream = cells[1].textContent.toLowerCase();const status = cells[2].textContent.toLowerCase();const matchesSearch = downstream.includes(searchTerm) || upstream.includes(searchTerm);const matchesStatus = !statusFilter || status.includes(statusFilter);row.style.display = matchesSearch && matchesStatus ? '' : 'none';});}function clearMappingForm(){document.getElementById('mappingDownstream').value = '';document.getElementById('mappingUpstream').value = '';document.getElementById('enableSSL').checked = false;}async function clearAllMappings(){if (!confirm('确定要清空所有映射吗？此操作不可恢复！')){return;}try{const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/mappings/clear',{method: 'DELETE'});const result = await response.json();if (result.success){showMessage('所有映射已清空','success');loadMappings();loadStats();}else{showMessage('清空失败: ' + result.message,'error');}}catch (error){showMessage('网络错误: ' + error.message,'error');}}function closeEditModal(){document.getElementById('editModal').style.display = 'none';currentEditId = null;}function getStatusClass(status){switch (status){case 'active': return 'status-active';case 'error': return 'status-inactive';default: return 'status-warning';}}function getStatusText(status){switch (status){case 'active': return '活跃';case 'inactive': return '非活跃';case 'error': return '错误';default: return '未知';}}function isValidDomain(domain){const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.([a-zA-Z]{2,}\.)*[a-zA-Z]{2,}$/;return domainRegex.test(domain);}function showMessage(message,type = 'success'){const messageArea = document.getElementById('messageArea');const alertClass = type === 'success' ? 'alert-success' : 'alert-error';messageArea.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;setTimeout(() =>{messageArea.innerHTML = '';},3000);}function formatDate(dateString){if (!dateString) return '-';const date = new Date(dateString);return date.toLocaleString('zh-CN');}window.onclick = function(event){const modal = document.getElementById('editModal');if (event.target === modal){closeEditModal();}}