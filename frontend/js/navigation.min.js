class Navigation{constructor(){this.currentPage = this.getCurrentPage();this.init();}init(){this.createNavigation();this.setupEventListeners();}getCurrentPage(){const path = window.location.pathname;const page = path.split('/').pop() || 'index.html';return page.replace('.html','');}createNavigation(){const nav = document.createElement('nav');nav.className = 'main-navigation';nav.innerHTML = this.getNavigationHTML();document.body.insertBefore(nav,document.body.firstChild);this.addNavigationStyles();}getNavigationHTML(){const navItems = [{id: 'index',icon: '🏠',title: '首页',href: 'index.html'},{id: 'dashboard',icon: '📊',title: '监控仪表板',href: 'dashboard.html'},{id: 'auto-proxy',icon: '🤖',title: '自动代理',href: 'auto-proxy.html'},{id: 'ssl-monitor',icon: '🔒',title: 'SSL监控',href: 'ssl-monitor.html'},{id: 'domains',icon: '📦',title: '域名池',href: 'domains.html'},{id: 'mappings',icon: '🔗',title: '代理映射',href: 'mappings.html'},{id: 'recursive',icon: '🔄',title: '递归代理',href: 'recursive.html'}];return ` <div class="nav-container"> <div class="nav-brand"> <span class="nav-logo">🚀</span> <span class="nav-title">SM智能代理</span> </div> <div class="nav-menu" id="navMenu"> ${navItems.map(item => ` <a href="${item.href}" class="nav-item ${this.currentPage === item.id ? 'active' : ''}" data-page="${item.id}"> <span class="nav-icon">${item.icon}</span> <span class="nav-text">${item.title}</span> </a> `).join('')}</div> <div class="nav-actions"> <div class="nav-user" id="navUser"> <span class="user-info" id="userInfo">未登录</span> <button class="change-password-button" id="changePasswordBtn" onclick="showChangePasswordDialog()" style="display: none;">修改密码</button> <button class="auth-button" id="authButton" onclick="handleAuth()">登录</button> </div> <div class="nav-status" id="navStatus"> <span class="status-indicator"></span> <span class="status-text">检查中...</span> </div> <button class="nav-toggle" id="navToggle"> <span></span> <span></span> <span></span> </button> </div> </div> `;}addNavigationStyles(){const style = document.createElement('style');style.textContent = ` .main-navigation{background: #1a1a1a;border-bottom: 1px solid #333;position: sticky;top: 0;z-index: 1000;box-shadow: 0 2px 10px rgba(0,0,0,0.3);}.nav-container{max-width: 1600px;margin: 0 auto;display: flex;align-items: center;justify-content: space-between;padding: 0 20px;height: 70px;}.nav-brand{display: flex;align-items: center;gap: 10px;font-weight: bold;color: #4CAF50;text-decoration: none;}.nav-logo{font-size: 1.6rem;}.nav-title{font-size: 1.3rem;font-weight: 600;}.nav-menu{display: flex;align-items: center;gap: 8px;}.nav-item{display: flex;align-items: center;gap: 8px;padding: 10px 16px;border-radius: 6px;color: #b0b0b0;text-decoration: none;transition: all 0.3s ease;font-size: 16px;font-weight: 500;white-space: nowrap;}.nav-item:hover{background: #333;color: #4CAF50;}.nav-item.active{background: #4CAF50;color: white;}.nav-icon{font-size: 18px;}.nav-actions{display: flex;align-items: center;gap: 16px;}.nav-user{display: flex;align-items: center;gap: 10px;}.user-info{color: #b0b0b0;font-size: 14px;font-weight: 500;}.auth-button{background: #4caf50;color: white;border: none;padding: 6px 12px;border-radius: 4px;font-size: 14px;font-weight: 500;cursor: pointer;transition: all 0.3s ease;}.auth-button:hover{background: #45a049;transform: translateY(-1px);}.auth-button.logout{background: #f44336;}.auth-button.logout:hover{background: #da190b;}.change-password-button{background: #2196f3;color: white;border: none;padding: 6px 12px;border-radius: 4px;font-size: 12px;font-weight: 500;cursor: pointer;transition: all 0.3s ease;margin-right: 8px;}.change-password-button:hover{background: #1976d2;transform: translateY(-1px);}.nav-status{display: flex;align-items: center;gap: 6px;font-size: 12px;color: #b0b0b0;}.status-indicator{width: 8px;height: 8px;border-radius: 50%;background: #666;transition: background 0.3s ease;}.status-indicator.online{background: #4CAF50;}.status-indicator.offline{background: #f44336;}.nav-toggle{display: none;flex-direction: column;background: none;border: none;cursor: pointer;padding: 4px;}.nav-toggle span{width: 20px;height: 2px;background: #b0b0b0;margin: 2px 0;transition: 0.3s;}@media (max-width: 768px){.nav-menu{position: fixed;top: 70px;left: -100%;width: 100%;height: calc(100vh - 70px);background: #1a1a1a;flex-direction: column;align-items: stretch;padding: 20px;transition: left 0.3s ease;border-top: 1px solid #333;}.nav-menu.active{left: 0;}.nav-item{padding: 16px 20px;border-radius: 8px;margin-bottom: 8px;font-size: 18px;}.nav-toggle{display: flex;}.nav-user{order: -1;margin-right: auto;}.nav-text{display: block;}}@media (max-width: 480px){.nav-container{padding: 0 16px;}.nav-title{display: none;}.nav-status .status-text{display: none;}}`;document.head.appendChild(style);}setupEventListeners(){const navToggle = document.getElementById('navToggle');const navMenu = document.getElementById('navMenu');navToggle?.addEventListener('click',() =>{navMenu.classList.toggle('active');});document.querySelectorAll('.nav-item').forEach(item =>{item.addEventListener('click',() =>{navMenu.classList.remove('active');});});this.checkSystemStatus();setInterval(() =>{this.checkSystemStatus();},30000);this.checkAuthStatus();window.addEventListener('storage',(e) =>{if (e.key === 'auth_token'){this.checkAuthStatus();}});}async checkSystemStatus(){try{const response = await fetch('/api/status');const result = await response.json();const indicator = document.querySelector('.status-indicator');const statusText = document.querySelector('.status-text');if (result.success){indicator.className = 'status-indicator online';statusText.textContent = '系统正常';}else{indicator.className = 'status-indicator offline';statusText.textContent = '系统异常';}}catch (error){const indicator = document.querySelector('.status-indicator');const statusText = document.querySelector('.status-text');indicator.className = 'status-indicator offline';statusText.textContent = '连接失败';}}checkAuthStatus(){const token = localStorage.getItem('auth_token');const userInfo = document.getElementById('userInfo');const authButton = document.getElementById('authButton');const changePasswordBtn = document.getElementById('changePasswordBtn');if (token){try{const payload = JSON.parse(atob(token.split('.')[1]));const username = payload.sub || payload.username || 'admin';userInfo.textContent = `👤 ${username}`;authButton.textContent = '退出';authButton.className = 'auth-button logout';if (changePasswordBtn){changePasswordBtn.style.display = 'inline-block';}}catch (error){localStorage.removeItem('auth_token');this.setLoggedOutState();}}else{this.setLoggedOutState();}}setLoggedOutState(){const userInfo = document.getElementById('userInfo');const authButton = document.getElementById('authButton');const changePasswordBtn = document.getElementById('changePasswordBtn');userInfo.textContent = '未登录';authButton.textContent = '登录';authButton.className = 'auth-button';if (changePasswordBtn){changePasswordBtn.style.display = 'none';}}}document.addEventListener('DOMContentLoaded',() =>{window.navigationInstance = new Navigation();});window.SM ={showNotification: function(message,type = 'info',duration = 3000){const notification = document.createElement('div');notification.className = `notification notification-${type}`;notification.innerHTML = ` <div class="notification-content"> <span class="notification-icon">${this.getNotificationIcon(type)}</span> <span class="notification-message">${message}</span> <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button> </div> `;if (!document.querySelector('#notification-styles')){const style = document.createElement('style');style.id = 'notification-styles';style.textContent = ` .notification{position: fixed;top: 80px;right: 20px;z-index: 10000;min-width: 300px;max-width: 500px;border-radius: 8px;box-shadow: 0 4px 12px rgba(0,0,0,0.3);animation: slideIn 0.3s ease;}.notification-info{background: #2196F3;color: white;}.notification-success{background: #4CAF50;color: white;}.notification-warning{background: #ff9800;color: white;}.notification-error{background: #f44336;color: white;}.notification-content{display: flex;align-items: center;padding: 12px 16px;gap: 10px;}.notification-icon{font-size: 18px;}.notification-message{flex: 1;font-size: 14px;}.notification-close{background: none;border: none;color: inherit;font-size: 18px;cursor: pointer;padding: 0;width: 20px;height: 20px;display: flex;align-items: center;justify-content: center;}@keyframes slideIn{from{transform: translateX(100%);opacity: 0;}to{transform: translateX(0);opacity: 1;}}`;document.head.appendChild(style);}document.body.appendChild(notification);if (duration > 0){setTimeout(() =>{notification.remove();},duration);}},getNotificationIcon: function(type){const icons ={info: 'ℹ️',success: '✅',warning: '⚠️',error: '❌'};return icons[type] || icons.info;},formatNumber: function(num){if (num >= 1000000){return (num / 1000000).toFixed(1) + 'M';}else if (num >= 1000){return (num / 1000).toFixed(1) + 'K';}return num.toString();},formatDate: function(dateString){if (!dateString) return '-';const date = new Date(dateString);return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN');},confirm: function(message,callback){if (window.confirm(message)){callback();}}};async function handleAuth(){const token = localStorage.getItem('auth_token');if (token){await logout();}else{showLoginDialog();}}async function logout(){const token = localStorage.getItem('auth_token');if (token){try{await fetch('/api/auth/logout',{method: 'POST',headers:{'Authorization': `Bearer ${token}`}});}catch (error){console.warn('退出请求失败:',error);}}localStorage.removeItem('auth_token');const navigation = window.navigationInstance;if (navigation){navigation.checkAuthStatus();}if (window.SM && window.SM.showNotification){window.SM.showNotification('已成功退出登录','success',2000);}setTimeout(() =>{window.location.href = 'index.html';},1000);}function getCurrentPageId(){const path = window.location.pathname;const filename = path.split('/').pop() || 'index.html';return filename.replace('.html','');}function showLoginDialog(){const dialog = document.createElement('div');dialog.style.cssText = ` position: fixed;top: 0;left: 0;width: 100%;height: 100%;background: rgba(0,0,0,0.8);display: flex;align-items: center;justify-content: center;z-index: 10000;`;dialog.innerHTML = ` <div style=" background: #2a2a2a;padding: 30px;border-radius: 8px;border: 1px solid #404040;min-width: 300px;text-align: center;"> <h3 style="color: #4caf50;margin-bottom: 20px;">🔐 用户登录</h3> <div style="margin-bottom: 15px;"> <input type="text" id="loginUsername" placeholder="请输入用户名" style=" width: 100%;padding: 10px;border: 1px solid #555;border-radius: 4px;background: #1a1a1a;color: white;box-sizing: border-box;"> </div> <div style="margin-bottom: 20px;"> <input type="password" id="loginPassword" placeholder="请输入密码" style=" width: 100%;padding: 10px;border: 1px solid #555;border-radius: 4px;background: #1a1a1a;color: white;box-sizing: border-box;"> </div> <div style="display: flex;gap: 10px;justify-content: center;"> <button onclick="performLogin()" style=" background: #4caf50;color: white;border: none;padding: 10px 20px;border-radius: 4px;cursor: pointer;font-size: 14px;">登录</button> <button onclick="closeLoginDialog()" style=" background: #666;color: white;border: none;padding: 10px 20px;border-radius: 4px;cursor: pointer;font-size: 14px;">取消</button> </div> </div> `;document.body.appendChild(dialog);window.currentLoginDialog = dialog;setTimeout(() =>{document.getElementById('loginUsername').focus();},100);dialog.addEventListener('keypress',(e) =>{if (e.key === 'Enter'){performLogin();}});}async function performLogin(){const username = document.getElementById('loginUsername').value;const password = document.getElementById('loginPassword').value;if (!username || !password){alert('请输入用户名和密码');return;}try{const response = await fetch('/api/auth/login',{method: 'POST',headers:{'Content-Type': 'application/json'},body: JSON.stringify({username,password})});const result = await response.json();if (result.success && result.data && result.data.token){localStorage.setItem('auth_token',result.data.token);const navigation = window.navigationInstance;if (navigation){navigation.checkAuthStatus();}closeLoginDialog();if (window.SM && window.SM.showNotification){window.SM.showNotification('登录成功！','success',2000);}}else{alert('登录失败: ' + (result.error || '用户名或密码错误'));}}catch (error){alert('登录请求失败: ' + error.message);}}function closeLoginDialog(){if (window.currentLoginDialog){document.body.removeChild(window.currentLoginDialog);window.currentLoginDialog = null;}}function showChangePasswordDialog(){closeLoginDialog();const dialog = document.createElement('div');dialog.style.cssText = ` position: fixed;top: 0;left: 0;width: 100%;height: 100%;background: rgba(0,0,0,0.8);display: flex;align-items: center;justify-content: center;z-index: 10000;`;dialog.innerHTML = ` <div style=" background: #2a2a2a;padding: 30px;border-radius: 8px;border: 1px solid #404040;min-width: 350px;text-align: center;"> <h3 style="color: #2196f3;margin-bottom: 20px;">🔑 修改密码</h3> <div style="margin-bottom: 15px;"> <input type="text" id="changeUsername" placeholder="请输入用户名" style=" width: 100%;padding: 10px;border: 1px solid #555;border-radius: 4px;background: #1a1a1a;color: white;box-sizing: border-box;"> </div> <div style="margin-bottom: 15px;"> <input type="password" id="changeOldPassword" placeholder="请输入当前密码" style=" width: 100%;padding: 10px;border: 1px solid #555;border-radius: 4px;background: #1a1a1a;color: white;box-sizing: border-box;"> </div> <div style="margin-bottom: 15px;"> <input type="password" id="changeNewPassword" placeholder="请输入新密码" style=" width: 100%;padding: 10px;border: 1px solid #555;border-radius: 4px;background: #1a1a1a;color: white;box-sizing: border-box;"> </div> <div style="margin-bottom: 20px;"> <input type="password" id="changeConfirmPassword" placeholder="请确认新密码" style=" width: 100%;padding: 10px;border: 1px solid #555;border-radius: 4px;background: #1a1a1a;color: white;box-sizing: border-box;"> </div> <div style="display: flex;gap: 10px;justify-content: center;"> <button onclick="performChangePassword()" style=" background: #2196f3;color: white;border: none;padding: 10px 20px;border-radius: 4px;cursor: pointer;font-size: 14px;">确认修改</button> <button onclick="closeChangePasswordDialog()" style=" background: #666;color: white;border: none;padding: 10px 20px;border-radius: 4px;cursor: pointer;font-size: 14px;">取消</button> </div> </div> `;document.body.appendChild(dialog);window.currentChangePasswordDialog = dialog;setTimeout(() =>{document.getElementById('changeUsername').focus();},100);dialog.addEventListener('keypress',(e) =>{if (e.key === 'Enter'){performChangePassword();}});}async function performChangePassword(){const username = document.getElementById('changeUsername').value;const oldPassword = document.getElementById('changeOldPassword').value;const newPassword = document.getElementById('changeNewPassword').value;const confirmPassword = document.getElementById('changeConfirmPassword').value;if (!username || !oldPassword || !newPassword || !confirmPassword){alert('请填写所有字段');return;}if (newPassword !== confirmPassword){alert('新密码和确认密码不匹配');return;}if (newPassword.length < 6){alert('新密码长度至少6位');return;}if (newPassword === oldPassword){alert('新密码不能与当前密码相同');return;}try{const loginResponse = await fetch('/api/auth/login',{method: 'POST',headers:{'Content-Type': 'application/json'},body: JSON.stringify({username,password: oldPassword})});const loginResult = await loginResponse.json();if (!loginResult.success){alert('当前密码验证失败: ' + (loginResult.error || '用户名或密码错误'));return;}const token = loginResult.data.token;const changeResponse = await fetch('/api/auth/change-password',{method: 'POST',headers:{'Content-Type': 'application/json','Authorization': `Bearer ${token}`},body: JSON.stringify({old_password: oldPassword,new_password: newPassword})});const changeResult = await changeResponse.json();if (changeResult.success){closeChangePasswordDialog();if (window.SM && window.SM.showNotification){window.SM.showNotification('密码修改成功！请重新登录','success',3000);}else{alert('密码修改成功！请重新登录');}localStorage.removeItem('auth_token');const navigation = window.navigationInstance;if (navigation){navigation.checkAuthStatus();}}else{alert('密码修改失败: ' + (changeResult.error || '未知错误'));}}catch (error){alert('修改密码请求失败: ' + error.message);}}function closeChangePasswordDialog(){if (window.currentChangePasswordDialog){document.body.removeChild(window.currentChangePasswordDialog);window.currentChangePasswordDialog = null;}}