/** * 全局认证守卫 - 确保所有页面都需要登录才能访问 * 在页面加载时自动检查登录状态，未登录则跳转到登录页面 */const PUBLIC_PAGES = [ 'index.html','login.html','test-frontend.html' ];function getCurrentPageName(){const path = window.location.pathname;const filename = path.split('/').pop() || 'index.html';return filename;}function isPublicPage(){const currentPage = getCurrentPageName();return PUBLIC_PAGES.includes(currentPage);}function isUserLoggedIn(){const token = localStorage.getItem('auth_token');if (!token){return false;}try{const payload = JSON.parse(atob(token.split('.')[1]));const currentTime = Math.floor(Date.now() / 1000);if (payload.exp && payload.exp < currentTime){localStorage.removeItem('auth_token');return false;}return true;}catch (error){localStorage.removeItem('auth_token');return false;}}function redirectToLogin(){const currentUrl = window.location.href;const indexUrl = window.location.origin + '/index.html';if (!currentUrl.includes('index.html')){localStorage.setItem('auth_return_url',currentUrl);}if (window.SM && window.SM.showNotification){window.SM.showNotification('请先登录后再访问此页面','warning',3000);}setTimeout(() =>{window.location.href = indexUrl;},1000);}function handleLoginSuccess(){const returnUrl = localStorage.getItem('auth_return_url');if (returnUrl){localStorage.removeItem('auth_return_url');setTimeout(() =>{window.location.href = returnUrl;},1500);return true;}return false;}function checkAuthentication(){if (isPublicPage()){return;}if (!isUserLoggedIn()){redirectToLogin();return;}console.log('✅ 用户已登录，页面正常加载');}window.addEventListener('storage',(e) =>{if (e.key === 'auth_token'){if (!isPublicPage()){checkAuthentication();}}});document.addEventListener('DOMContentLoaded',() =>{checkAuthentication();});if (document.readyState === 'loading'){}else{checkAuthentication();}async function authenticatedFetch(url,options ={}){const token = localStorage.getItem('auth_token');if (!token){redirectToLogin();throw new Error('未登录');}const headers ={'Authorization': `Bearer ${token}`,'Content-Type': 'application/json',...(options.headers ||{})};const response = await fetch(url,{...options,headers});if (response.status === 401){localStorage.removeItem('auth_token');if (window.SM && window.SM.showNotification){window.SM.showNotification('登录已过期，请重新登录','warning',3000);}redirectToLogin();throw new Error('登录已过期');}return response;}window.AuthGuard ={isUserLoggedIn,isPublicPage,checkAuthentication,redirectToLogin,handleLoginSuccess,getCurrentPageName,authenticatedFetch};