class AutoProxyManager{constructor(){this.apiBase = '/api/auto-proxy';this.init();}async init(){await this.loadStatistics();await this.loadAutoProxyConfig();await this.loadMappings();await this.loadCertificates();setInterval(() =>{this.loadStatistics();},30000);}async loadStatistics(){try{const response = await window.AuthGuard.authenticatedFetch(`${this.apiBase}/statistics`);const result = await response.json();if (result.success && result.data){this.updateStatistics(result.data);}}catch (error){console.error('加载统计信息失败:',error);}}updateStatistics(stats){document.getElementById('totalMappings').textContent = stats.total_mappings || 0;document.getElementById('activeMappings').textContent = stats.active_mappings || 0;document.getElementById('totalCertificates').textContent = stats.total_certificates || 0;document.getElementById('validCertificates').textContent = stats.valid_certificates || 0;document.getElementById('totalRequests').textContent = this.formatNumber(stats.total_requests || 0);document.getElementById('cacheHitRate').textContent = `${(stats.cache_hit_rate * 100 || 0).toFixed(1)}%`;}async loadAutoProxyConfig(){try{const response = await window.AuthGuard.authenticatedFetch(`${this.apiBase}/config`);const result = await response.json();if (result.success && result.data){this.updateConfigForm(result.data);}}catch (error){console.error('加载配置失败:',error);}}updateConfigForm(config){document.getElementById('enableAutoSsl').checked = config.enable_auto_ssl || false;document.getElementById('enableContentReplacement').checked = config.enable_content_replacement || false;document.getElementById('maxSubdomains').value = config.max_subdomains_per_root || 100;document.getElementById('maxRedirectHops').value = config.max_redirect_hops || 3;document.getElementById('sslCertDir').value = config.ssl_cert_dir || 'certs';document.getElementById('contactEmail').value = config.contact_email || '';if (config.dns_provider){document.getElementById('dnsProvider').value = config.dns_provider.provider || 'aliyun';document.getElementById('dnsAccessKeyId').value = config.dns_provider.access_key_id || '';}if (config.recursive_config){const rc = config.recursive_config;document.getElementById('enableRecursive').checked = rc.enabled || false;document.getElementById('maxDepth').value = rc.max_depth || 3;document.getElementById('checkInterval').value = rc.check_interval_minutes || 5;document.getElementById('concurrency').value = rc.concurrency || 10;document.getElementById('timeoutSeconds').value = rc.timeout_seconds || 30;document.getElementById('contentThreshold').value = rc.content_threshold_kb || 300;}}async saveAutoProxyConfig(){const config ={enable_auto_ssl: document.getElementById('enableAutoSsl').checked,enable_content_replacement: document.getElementById('enableContentReplacement').checked,max_subdomains_per_root: parseInt(document.getElementById('maxSubdomains').value),max_redirect_hops: parseInt(document.getElementById('maxRedirectHops').value),ssl_cert_dir: document.getElementById('sslCertDir').value,contact_email: document.getElementById('contactEmail').value,dns_provider:{provider: document.getElementById('dnsProvider').value,access_key_id: document.getElementById('dnsAccessKeyId').value,access_key_secret: '',region: 'cn-hangzhou'},recursive_config:{enabled: document.getElementById('enableRecursive').checked,max_depth: parseInt(document.getElementById('maxDepth').value),check_interval_minutes: parseInt(document.getElementById('checkInterval').value),concurrency: parseInt(document.getElementById('concurrency').value),timeout_seconds: parseInt(document.getElementById('timeoutSeconds').value),content_threshold_kb: parseInt(document.getElementById('contentThreshold').value)}};try{const response = await window.AuthGuard.authenticatedFetch(`${this.apiBase}/config`,{method: 'PUT',body: JSON.stringify(config)});const result = await response.json();if (result.success){this.showAlert('配置保存成功','success');}else{this.showAlert('配置保存失败: ' + (result.error || '未知错误'),'error');}}catch (error){console.error('保存配置失败:',error);this.showAlert('配置保存失败: ' + error.message,'error');}}async loadMappings(){try{const response = await window.AuthGuard.authenticatedFetch(`${this.apiBase}/mappings`);const result = await response.json();if (result.success && result.data){this.updateMappingsTable(result.data);}}catch (error){console.error('加载域名映射失败:',error);}}updateMappingsTable(mappings){const tbody = document.getElementById('mappingsTableBody');tbody.innerHTML = '';mappings.forEach(mapping =>{const row = document.createElement('tr');row.innerHTML = ` <td>${mapping.upstream_domain}</td> <td>${mapping.downstream_domain}</td> <td><span class="status-badge status-${mapping.status}">${this.getStatusText(mapping.status)}</span></td> <td><span class="status-badge ${mapping.ssl_enabled ? 'status-active' : 'status-pending'}">${mapping.ssl_enabled ? 'SSL启用' : 'SSL未启用'}</span></td> <td>${this.formatNumber(mapping.request_count || 0)}</td> <td>${this.formatDate(mapping.created_at)}</td> <td> <button class="btn btn-danger" onclick="autoProxyManager.deleteMapping('${mapping.id}')">删除</button> </td> `;tbody.appendChild(row);});}async loadCertificates(){try{const response = await window.AuthGuard.authenticatedFetch(`${this.apiBase}/certificates`);const result = await response.json();if (result.success && result.data){this.updateCertificatesTable(result.data);}}catch (error){console.error('加载SSL证书失败:',error);}}updateCertificatesTable(certificates){const tbody = document.getElementById('certificatesTableBody');tbody.innerHTML = '';certificates.forEach(cert =>{const row = document.createElement('tr');const daysLeft = this.calculateDaysLeft(cert.expires_at);row.innerHTML = ` <td>${cert.domain}</td> <td><span class="status-badge status-${cert.status}">${this.getCertStatusText(cert.status)}</span></td> <td>${this.formatDate(cert.created_at)}</td> <td>${this.formatDate(cert.expires_at)}</td> <td>${daysLeft >= 0 ? daysLeft + '天' : '已过期'}</td> <td> <button class="btn btn-primary" onclick="autoProxyManager.renewCertificate('${cert.domain}')">续期</button> </td> `;tbody.appendChild(row);});}async createDomainMapping(){const upstreamDomain = document.getElementById('upstreamDomain').value.trim();if (!upstreamDomain){this.showAlert('请输入上游域名','warning');return;}try{const response = await window.AuthGuard.authenticatedFetch(`${this.apiBase}/mappings`,{method: 'POST',body: JSON.stringify({upstream_domain: upstreamDomain})});const result = await response.json();if (result.success){this.showAlert('域名映射创建成功','success');this.closeModal('createMappingModal');this.loadMappings();this.loadStatistics();}else{this.showAlert('域名映射创建失败: ' + (result.error || '未知错误'),'error');}}catch (error){console.error('创建域名映射失败:',error);this.showAlert('域名映射创建失败: ' + error.message,'error');}}async requestSslCertificate(){const domain = document.getElementById('certDomain').value.trim();const forceRenewal = document.getElementById('forceRenewal').checked;if (!domain){this.showAlert('请输入域名','warning');return;}try{const response = await window.AuthGuard.authenticatedFetch(`${this.apiBase}/certificates`,{method: 'POST',body: JSON.stringify({domain: domain,force_renewal: forceRenewal})});const result = await response.json();if (result.success){this.showAlert('SSL证书申请已提交','success');this.closeModal('requestCertModal');this.loadCertificates();this.loadStatistics();}else{this.showAlert('SSL证书申请失败: ' + (result.error || '未知错误'),'error');}}catch (error){console.error('申请SSL证书失败:',error);this.showAlert('SSL证书申请失败: ' + error.message,'error');}}async renewCertificate(domain){if (!confirm(`确定要续期域名 ${domain}的SSL证书吗？`)){return;}try{const response = await window.AuthGuard.authenticatedFetch(`${this.apiBase}/certificates/renew`,{method: 'POST',body: JSON.stringify({domain})});const result = await response.json();if (result.success){this.showAlert('SSL证书续期已提交','success');this.loadCertificates();this.loadStatistics();}else{this.showAlert('SSL证书续期失败: ' + (result.error || '未知错误'),'error');}}catch (error){this.showAlert('SSL证书续期失败: ' + error.message,'error');}}async triggerRecursiveDiscovery(){try{const response = await window.AuthGuard.authenticatedFetch(`${this.apiBase}/recursive/trigger`,{method: 'POST'});const result = await response.json();if (result.success){this.showAlert('递归代理发现已触发','success');}else{this.showAlert('触发失败: ' + (result.error || '未知错误'),'error');}}catch (error){console.error('触发递归发现失败:',error);this.showAlert('触发失败: ' + error.message,'error');}}async clearCache(){if (!confirm('确定要清空所有缓存吗？')){return;}try{this.showAlert('缓存清空功能开发中','warning');}catch (error){console.error('清空缓存失败:',error);this.showAlert('清空缓存失败: ' + error.message,'error');}}formatNumber(num){if (num >= 1000000){return (num / 1000000).toFixed(1) + 'M';}else if (num >= 1000){return (num / 1000).toFixed(1) + 'K';}return num.toString();}formatDate(dateString){if (!dateString) return '-';const date = new Date(dateString);return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN');}calculateDaysLeft(expiresAt){if (!expiresAt) return 0;const now = new Date();const expires = new Date(expiresAt);const diffTime = expires - now;return Math.ceil(diffTime / (1000 * 60 * 60 * 24));}getStatusText(status){const statusMap ={'active': '活跃','pending': '待处理','error': '错误','disabled': '已禁用'};return statusMap[status] || status;}getCertStatusText(status){const statusMap ={'valid': '有效','pending': '申请中','expired': '已过期','error': '错误'};return statusMap[status] || status;}showAlert(message,type = 'info'){const alert = document.createElement('div');alert.className = `alert alert-${type}`;alert.textContent = message;const container = document.querySelector('.auto-proxy-container');container.insertBefore(alert,container.firstChild);setTimeout(() =>{alert.remove();},3000);}closeModal(modalId){document.getElementById(modalId).style.display = 'none';}}function showCreateMappingModal(){document.getElementById('createMappingModal').style.display = 'block';document.getElementById('upstreamDomain').value = '';}function showRequestCertModal(){document.getElementById('requestCertModal').style.display = 'block';document.getElementById('certDomain').value = '';document.getElementById('forceRenewal').checked = false;}function closeModal(modalId){autoProxyManager.closeModal(modalId);}function saveAutoProxyConfig(){autoProxyManager.saveAutoProxyConfig();}function loadAutoProxyConfig(){autoProxyManager.loadAutoProxyConfig();}function createDomainMapping(){autoProxyManager.createDomainMapping();}function requestSslCertificate(){autoProxyManager.requestSslCertificate();}function triggerRecursiveDiscovery(){autoProxyManager.triggerRecursiveDiscovery();}function clearCache(){autoProxyManager.clearCache();}function refreshMappings(){autoProxyManager.loadMappings();}function refreshCertificates(){autoProxyManager.loadCertificates();}const autoProxyManager = new AutoProxyManager();window.onclick = function(event){const modals = document.querySelectorAll('.modal');modals.forEach(modal =>{if (event.target === modal){modal.style.display = 'none';}});}