let autoMirrorEnabled = false;document.addEventListener('DOMContentLoaded',async function(){loadDomains();loadStats();loadAutoMirrorStatus();const upstreamSearch = document.getElementById('upstreamSearchInput');const downstreamSearch = document.getElementById('downstreamSearchInput');if (upstreamSearch){upstreamSearch.addEventListener('input',() => filterDomains('upstream'));}if (downstreamSearch){downstreamSearch.addEventListener('input',() => filterDomains('downstream'));}});async function addUpstreamDomains(){const domainsText = document.getElementById('upstreamDomains').value.trim();if (!domainsText){showMessage('请输入上游域名','error');return;}const domains = domainsText.split('\n').map(d => d.trim()).filter(d => d);if (domains.length === 0){showMessage('请输入有效的域名','error');return;}try{const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/batch-add',{method: 'POST',body: JSON.stringify({domains: domains,domain_type: 'upstream'})});const result = await response.json();if (result.success){showMessage(`成功添加 ${domains.length}个上游域名`,'success');document.getElementById('upstreamDomains').value = '';loadDomains();loadStats();}else{showMessage('添加失败: ' + result.message,'error');}}catch (error){showMessage('网络错误: ' + error.message,'error');}}async function addDownstreamDomains(){const domainsText = document.getElementById('downstreamDomains').value.trim();if (!domainsText){showMessage('请输入下游域名','error');return;}const domains = domainsText.split('\n').map(d => d.trim()).filter(d => d);if (domains.length === 0){showMessage('请输入有效的域名','error');return;}try{const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/batch-add',{method: 'POST',body: JSON.stringify({domains: domains,domain_type: 'downstream'})});const result = await response.json();if (result.success){showMessage(`成功添加 ${domains.length}个下游域名`,'success');document.getElementById('downstreamDomains').value = '';loadDomains();loadStats();}else{showMessage('添加失败: ' + result.message,'error');}}catch (error){showMessage('网络错误: ' + error.message,'error');}}async function toggleAutoMirror(){const enabled = document.getElementById('autoMirrorSwitch').checked;try{const response = await window.AuthGuard.authenticatedFetch('/api/config',{method: 'POST',body: JSON.stringify({auto_mirror_enabled: enabled})});const result = await response.json();if (result.success){autoMirrorEnabled = enabled;document.getElementById('mirrorStatus').textContent = enabled ? '开启' : '关闭';showMessage(`自动镜像已${enabled ? '开启' : '关闭'}`,'success');}else{document.getElementById('autoMirrorSwitch').checked = !enabled;showMessage('设置失败: ' + result.message,'error');}}catch (error){document.getElementById('autoMirrorSwitch').checked = !enabled;showMessage('网络错误: ' + error.message,'error');}}async function loadDomains(){try{const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/domains');const result = await response.json();if (result.success){let domains = [];if (result.data){if (Array.isArray(result.data)){domains = result.data;}else if (result.data.domains && Array.isArray(result.data.domains)){domains = result.data.domains;}else{console.warn('未知的域名数据结构:',result.data);}}displayDomains(domains);}else{showMessage('加载域名失败: ' + result.message,'error');}}catch (error){console.error('加载域名失败:',error);const upstreamBody = document.getElementById('upstreamTableBody');const downstreamBody = document.getElementById('downstreamTableBody');if (upstreamBody){upstreamBody.innerHTML = '<tr><td colspan="4" style="text-align: center;color: #dc3545;">加载失败</td></tr>';}if (downstreamBody){downstreamBody.innerHTML = '<tr><td colspan="4" style="text-align: center;color: #dc3545;">加载失败</td></tr>';}}}function displayDomains(domains){if (!Array.isArray(domains)){console.warn('域名数据不是数组:',domains);domains = [];}const upstreamDomains = domains.filter(domain => domain.type === 'upstream');const downstreamDomains = domains.filter(domain => domain.type === 'downstream');displayUpstreamDomains(upstreamDomains);displayDownstreamDomains(downstreamDomains);updateDomainStats(upstreamDomains.length,downstreamDomains.length);}function displayUpstreamDomains(domains){const tbody = document.getElementById('upstreamTableBody');if (domains.length === 0){tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;color: #666;">暂无上游域名</td></tr>';return;}tbody.innerHTML = domains.map(domain => ` <tr> <td>${domain.domain}</td> <td> <span class="status-indicator status-active"></span> 正常 </td> <td>${formatDate(domain.created_at)}</td> <td> <button class="btn btn-sm btn-danger" onclick="deleteDomain('${domain.id}')">删除</button> </td> </tr> `).join('');}function displayDownstreamDomains(domains){const tbody = document.getElementById('downstreamTableBody');if (domains.length === 0){tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;color: #666;">暂无下游域名</td></tr>';return;}tbody.innerHTML = domains.map(domain => ` <tr> <td>${domain.domain}</td> <td> <span class="status-indicator status-warning"></span> 正常 </td> <td>${formatDate(domain.created_at)}</td> <td> <button class="btn btn-sm btn-danger" onclick="deleteDomain('${domain.id}')">删除</button> </td> </tr> `).join('');}function updateDomainStats(upstreamCount,downstreamCount){document.getElementById('upstreamCount').textContent = upstreamCount;document.getElementById('downstreamCount').textContent = downstreamCount;}async function clearUpstreamDomains(){if (!confirm('确定要清空所有上游域名吗？此操作不可恢复！')){return;}try{const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/clear',{method: 'DELETE',body: JSON.stringify({type: 'upstream'})});const result = await response.json();if (result.success){showMessage('上游域名已清空','success');loadDomains();}else{showMessage('清空失败: ' + result.message,'error');}}catch (error){showMessage('清空失败: ' + error.message,'error');}}async function clearDownstreamDomains(){if (!confirm('确定要清空所有下游域名吗？此操作不可恢复！')){return;}try{const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/clear',{method: 'DELETE',body: JSON.stringify({type: 'downstream'})});const result = await response.json();if (result.success){showMessage('下游域名已清空','success');loadDomains();}else{showMessage('清空失败: ' + result.message,'error');}}catch (error){showMessage('清空失败: ' + error.message,'error');}}async function deleteDomain(domainId){if (!confirm('确定要删除这个域名吗？')){return;}try{const response = await window.AuthGuard.authenticatedFetch(`/api/domain-pool/domains/${domainId}`,{method: 'DELETE'});const result = await response.json();if (result.success){showMessage('域名删除成功','success');loadDomains();loadStats();}else{showMessage('删除失败: ' + result.message,'error');}}catch (error){showMessage('网络错误: ' + error.message,'error');}}async function loadStats(){try{const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/statistics');const result = await response.json();if (result.success){const data = result.data ||{};document.getElementById('upstreamCount').textContent = data.upstream_count || 0;document.getElementById('downstreamCount').textContent = data.downstream_count || 0;document.getElementById('totalMappings').textContent = data.total_mappings || 0;}}catch (error){console.error('加载统计数据失败:',error);}}async function loadAutoMirrorStatus(){try{const response = await window.AuthGuard.authenticatedFetch('/api/config');const result = await response.json();if (result.success){const config = result.data ||{};autoMirrorEnabled = config.auto_mirror_enabled || false;document.getElementById('autoMirrorSwitch').checked = autoMirrorEnabled;document.getElementById('mirrorStatus').textContent = autoMirrorEnabled ? '开启' : '关闭';}}catch (error){console.error('加载自动镜像状态失败:',error);}}function filterDomains(){console.warn('filterDomains() 已废弃，请使用 filterDomains(type) 替代');}function clearUpstreamInput(){document.getElementById('upstreamDomains').value = '';}function clearDownstreamInput(){document.getElementById('downstreamDomains').value = '';}async function clearAllDomains(){if (!confirm('确定要清空所有域名吗？此操作不可恢复！')){return;}try{const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/clear',{method: 'DELETE'});const result = await response.json();if (result.success){showMessage('所有域名已清空','success');loadDomains();loadStats();}else{showMessage('清空失败: ' + result.message,'error');}}catch (error){showMessage('网络错误: ' + error.message,'error');}}function showMessage(message,type = 'success'){const messageArea = document.getElementById('messageArea');const alertClass = type === 'success' ? 'alert-success' : 'alert-error';messageArea.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;setTimeout(() =>{messageArea.innerHTML = '';},3000);}function formatDate(dateString){if (!dateString) return '-';const date = new Date(dateString);return date.toLocaleString('zh-CN');}function filterDomains(type){const searchInput = document.getElementById(type + 'SearchInput');if (!searchInput) return;const searchTerm = searchInput.value.toLowerCase();const tableBody = document.getElementById(type + 'TableBody');if (!tableBody) return;const rows = tableBody.getElementsByTagName('tr');for (let row of rows){const domainCell = row.cells[0];if (domainCell){const domainText = domainCell.textContent.toLowerCase();if (domainText.includes(searchTerm)){row.style.display = '';}else{row.style.display = 'none';}}}}