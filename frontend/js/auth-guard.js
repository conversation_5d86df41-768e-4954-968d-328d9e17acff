/**
 * 全局认证守卫 - 确保所有页面都需要登录才能访问
 * 在页面加载时自动检查登录状态，未登录则跳转到登录页面
 */

// 公共页面列表（不需要登录即可访问）
const PUBLIC_PAGES = [
    'index.html',
    'login.html',
    'test-frontend.html'  // 测试页面保持公开
];

// 获取当前页面文件名
function getCurrentPageName() {
    const path = window.location.pathname;
    const filename = path.split('/').pop() || 'index.html';
    return filename;
}

// 检查当前页面是否为公共页面
function isPublicPage() {
    const currentPage = getCurrentPageName();
    return PUBLIC_PAGES.includes(currentPage);
}

// 检查用户是否已登录
function isUserLoggedIn() {
    const token = localStorage.getItem('auth_token');
    
    if (!token) {
        return false;
    }
    
    try {
        // 解析JWT token检查是否过期
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);
        
        // 检查token是否过期
        if (payload.exp && payload.exp < currentTime) {
            // Token已过期，清除
            localStorage.removeItem('auth_token');
            return false;
        }
        
        return true;
    } catch (error) {
        // Token格式错误，清除
        localStorage.removeItem('auth_token');
        return false;
    }
}

// 跳转到首页（登录入口）
function redirectToLogin() {
    // 保存当前页面URL，登录后可以跳转回来
    const currentUrl = window.location.href;
    const indexUrl = window.location.origin + '/index.html';

    // 如果当前不是首页，保存返回URL
    if (!currentUrl.includes('index.html')) {
        localStorage.setItem('auth_return_url', currentUrl);
    }

    // 显示提示信息
    if (window.SM && window.SM.showNotification) {
        window.SM.showNotification('请先登录后再访问此页面', 'warning', 3000);
    }

    // 延迟跳转，让用户看到提示
    setTimeout(() => {
        window.location.href = indexUrl;
    }, 1000);
}

// 登录成功后的跳转处理
function handleLoginSuccess() {
    const returnUrl = localStorage.getItem('auth_return_url');
    
    if (returnUrl) {
        localStorage.removeItem('auth_return_url');
        // 延迟跳转，让用户看到登录成功提示
        setTimeout(() => {
            window.location.href = returnUrl;
        }, 1500);
        return true; // 表示将要跳转
    }
    
    return false; // 表示不需要跳转
}

// 主要的认证检查函数
function checkAuthentication() {
    // 如果是公共页面，不需要检查登录状态
    if (isPublicPage()) {
        return;
    }
    
    // 检查是否已登录
    if (!isUserLoggedIn()) {
        // 未登录，跳转到登录页面
        redirectToLogin();
        return;
    }
    
    // 已登录，继续正常加载页面
    console.log('✅ 用户已登录，页面正常加载');
}

// 监听存储变化（其他标签页登录/退出）
window.addEventListener('storage', (e) => {
    if (e.key === 'auth_token') {
        // Token发生变化，重新检查认证状态
        if (!isPublicPage()) {
            checkAuthentication();
        }
    }
});

// 页面加载时立即检查认证状态
document.addEventListener('DOMContentLoaded', () => {
    checkAuthentication();
});

// 如果页面已经加载完成，立即检查
if (document.readyState === 'loading') {
    // 文档还在加载中，等待DOMContentLoaded事件
} else {
    // 文档已经加载完成，立即检查
    checkAuthentication();
}

// 简单的认证fetch函数，不包含自动登录逻辑
async function authenticatedFetch(url, options = {}) {
    const token = localStorage.getItem('auth_token');

    if (!token) {
        // 没有token，跳转到首页
        redirectToLogin();
        throw new Error('未登录');
    }

    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...(options.headers || {})
    };

    const response = await fetch(url, {
        ...options,
        headers
    });

    // 如果返回401，说明token过期，清除认证状态并跳转
    if (response.status === 401) {
        localStorage.removeItem('auth_token');

        // 显示提示信息
        if (window.SM && window.SM.showNotification) {
            window.SM.showNotification('登录已过期，请重新登录', 'warning', 3000);
        }

        // 跳转到首页
        redirectToLogin();
        throw new Error('登录已过期');
    }

    return response;
}

// 导出函数供其他脚本使用
window.AuthGuard = {
    isUserLoggedIn,
    isPublicPage,
    checkAuthentication,
    redirectToLogin,
    handleLoginSuccess,
    getCurrentPageName,
    authenticatedFetch
};
