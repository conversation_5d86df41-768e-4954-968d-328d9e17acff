// 递归代理控制JavaScript

let recursiveEnabled = false;
let autoScrollEnabled = false;
let logUpdateInterval = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadRecursiveSettings();
    loadRecursiveStatus();
    loadHistory();
    
    // 设置搜索和过滤事件
    document.getElementById('searchHistory').addEventListener('input', filterHistory);
    document.getElementById('depthFilter').addEventListener('change', filterHistory);
    
    // 启动日志更新
    startLogUpdates();
});

// 保存递归设置
async function saveRecursiveSettings() {
    const settings = {
        max_depth: parseInt(document.getElementById('recursiveDepth').value),
        check_interval_minutes: parseInt(document.getElementById('recursiveFrequency').value),
        concurrency: parseInt(document.getElementById('recursiveConcurrency').value),
        timeout_seconds: parseInt(document.getElementById('recursiveTimeout').value),
        content_threshold_kb: parseInt(document.getElementById('contentThreshold').value)
    };

    // 验证设置
    if (settings.max_depth < 1 || settings.max_depth > 10) {
        showMessage('递归深度必须在1-10之间', 'error');
        return;
    }

    if (settings.check_interval_minutes < 1 || settings.check_interval_minutes > 60) {
        showMessage('检查频率必须在1-60分钟之间', 'error');
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/config', {
            method: 'PUT',
            body: JSON.stringify({
                recursive_config: settings
            })
        });

        const result = await response.json();
        if (result.success) {
            showMessage('递归设置保存成功', 'success');
        } else {
            showMessage('保存失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 切换递归代理
async function toggleRecursive() {
    const enabled = document.getElementById('recursiveSwitch').checked;
    
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/config', {
            method: 'PUT',
            body: JSON.stringify({
                enable_recursive_discovery: enabled
            })
        });

        const result = await response.json();
        if (result.success) {
            recursiveEnabled = enabled;
            document.getElementById('recursiveStatus').textContent = enabled ? '运行中' : '停止';
            showMessage(`递归代理已${enabled ? '启动' : '停止'}`, 'success');
            
            if (enabled) {
                addLogEntry('递归代理已启动', 'info');
            } else {
                addLogEntry('递归代理已停止', 'info');
            }
        } else {
            // 恢复开关状态
            document.getElementById('recursiveSwitch').checked = !enabled;
            showMessage('操作失败: ' + result.message, 'error');
        }
    } catch (error) {
        // 恢复开关状态
        document.getElementById('recursiveSwitch').checked = !enabled;
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 加载递归设置
async function loadRecursiveSettings() {
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/config');
        const result = await response.json();
        
        if (result.success) {
            const config = result.data?.recursive_config || {};
            document.getElementById('recursiveDepth').value = config.max_depth || 3;
            document.getElementById('recursiveFrequency').value = config.check_interval_minutes || 5;
            document.getElementById('recursiveConcurrency').value = config.concurrency || 10;
            document.getElementById('recursiveTimeout').value = config.timeout_seconds || 30;
            document.getElementById('contentThreshold').value = config.content_threshold_kb || 300;
        }
    } catch (error) {
        console.error('加载递归设置失败:', error);
    }
}

// 加载递归状态
async function loadRecursiveStatus() {
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/statistics');
        const result = await response.json();
        
        if (result.success) {
            const status = result.data || {};
            recursiveEnabled = status.recursive_enabled || false;

            document.getElementById('recursiveSwitch').checked = recursiveEnabled;
            document.getElementById('recursiveStatus').textContent = recursiveEnabled ? '运行中' : '停止';
            document.getElementById('discoveredDomains').textContent = status.total_mappings || 0;
            document.getElementById('successfulMappings').textContent = status.active_mappings || 0;
            document.getElementById('recursiveErrors').textContent = 0; // 暂时设为0
        }
    } catch (error) {
        console.error('加载递归状态失败:', error);
    }
}

// 加载递归历史
async function loadHistory() {
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/mappings');
        const result = await response.json();
        
        if (result.success) {
            displayHistory(result.data || []);
        } else {
            showMessage('加载历史失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('加载递归历史失败:', error);
        document.getElementById('historyTableBody').innerHTML = 
            '<tr><td colspan="7" style="text-align: center; color: #dc3545;">加载失败</td></tr>';
    }
}

// 显示递归历史
function displayHistory(history) {
    const tbody = document.getElementById('historyTableBody');
    
    if (history.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #666;">暂无递归发现记录</td></tr>';
        return;
    }

    tbody.innerHTML = history.map(record => `
        <tr>
            <td>${record.discovered_domain}</td>
            <td>${record.source_domain}</td>
            <td>${record.depth}</td>
            <td>${formatBytes(record.content_size)}</td>
            <td>${record.response_time}ms</td>
            <td>${formatDate(record.discovered_at)}</td>
            <td>
                <span class="status-indicator ${record.status === 'success' ? 'status-active' : 'status-inactive'}"></span>
                ${record.status === 'success' ? '成功' : '失败'}
            </td>
        </tr>
    `).join('');
}

// 手动触发递归
async function manualTrigger() {
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/recursive/trigger', {
            method: 'POST'
        });

        const result = await response.json();
        if (result.success) {
            showMessage('手动触发成功', 'success');
            addLogEntry('手动触发递归代理发现', 'info');
            loadRecursiveStatus();
        } else {
            showMessage('触发失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 清空递归数据
async function clearRecursiveData() {
    if (!confirm('确定要清空所有递归数据吗？此操作不可恢复！')) {
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/mappings/clear', {
            method: 'DELETE'
        });

        const result = await response.json();
        if (result.success) {
            showMessage('递归数据已清空', 'success');
            loadHistory();
            loadRecursiveStatus();
            addLogEntry('递归数据已清空', 'warning');
        } else {
            showMessage('清空失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 清空历史记录
async function clearHistory() {
    if (!confirm('确定要清空历史记录吗？')) {
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/mappings', {
            method: 'DELETE'
        });

        const result = await response.json();
        if (result.success) {
            showMessage('历史记录已清空', 'success');
            loadHistory();
        } else {
            showMessage('清空失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 重置设置
function resetSettings() {
    document.getElementById('recursiveDepth').value = 3;
    document.getElementById('recursiveFrequency').value = 5;
    document.getElementById('recursiveConcurrency').value = 10;
    document.getElementById('recursiveTimeout').value = 30;
    document.getElementById('contentThreshold').value = 300;
    showMessage('设置已重置为默认值', 'success');
}

// 过滤历史记录
function filterHistory() {
    const searchTerm = document.getElementById('searchHistory').value.toLowerCase();
    const depthFilter = document.getElementById('depthFilter').value;
    const rows = document.querySelectorAll('#historyTableBody tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length < 7) return; // 跳过空行

        const discoveredDomain = cells[0].textContent.toLowerCase();
        const sourceDomain = cells[1].textContent.toLowerCase();
        const depth = cells[2].textContent;
        
        const matchesSearch = discoveredDomain.includes(searchTerm) || sourceDomain.includes(searchTerm);
        const matchesDepth = !depthFilter || depth === depthFilter;
        
        row.style.display = matchesSearch && matchesDepth ? '' : 'none';
    });
}

// 清空日志
function clearLog() {
    document.getElementById('recursiveLog').innerHTML = '<div style="color: #666;">日志已清空</div>';
}

// 切换自动滚动
function toggleAutoScroll() {
    autoScrollEnabled = !autoScrollEnabled;
    document.getElementById('autoScrollText').textContent = autoScrollEnabled ? '关闭自动滚动' : '开启自动滚动';
}

// 启动日志更新
function startLogUpdates() {
    if (logUpdateInterval) {
        clearInterval(logUpdateInterval);
    }
    
    logUpdateInterval = setInterval(async () => {
        if (recursiveEnabled) {
            await updateLogs();
        }
    }, 2000); // 每2秒更新一次日志
}

// 更新日志
async function updateLogs() {
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/status');
        const result = await response.json();
        
        if (result.success && result.data) {
            const logs = result.data.logs || [];
            const logContainer = document.getElementById('recursiveLog');
            
            // 添加新日志
            logs.forEach(log => {
                addLogEntry(log.message, log.level, log.timestamp);
            });
        }
    } catch (error) {
        console.error('更新日志失败:', error);
    }
}

// 添加日志条目
function addLogEntry(message, level = 'info', timestamp = null) {
    const logContainer = document.getElementById('recursiveLog');
    const time = timestamp ? new Date(timestamp) : new Date();
    const timeStr = time.toLocaleTimeString('zh-CN');
    
    const levelColors = {
        info: '#4CAF50',
        warning: '#ffc107',
        error: '#dc3545',
        debug: '#6c757d'
    };
    
    const color = levelColors[level] || '#e0e0e0';
    
    const logEntry = document.createElement('div');
    logEntry.innerHTML = `<span style="color: #666;">[${timeStr}]</span> <span style="color: ${color};">[${level.toUpperCase()}]</span> ${message}`;
    
    logContainer.appendChild(logEntry);
    
    // 限制日志条数
    const logEntries = logContainer.children;
    if (logEntries.length > 100) {
        logContainer.removeChild(logEntries[0]);
    }
    
    // 自动滚动到底部
    if (autoScrollEnabled) {
        logContainer.scrollTop = logContainer.scrollHeight;
    }
}

// 格式化字节数
function formatBytes(bytes) {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// 显示消息
function showMessage(message, type = 'success') {
    const messageArea = document.getElementById('messageArea');
    const alertClass = type === 'success' ? 'alert-success' : (type === 'warning' ? 'alert-warning' : 'alert-error');
    
    messageArea.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
    
    // 3秒后自动隐藏
    setTimeout(() => {
        messageArea.innerHTML = '';
    }, 3000);
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}
