// 域名池管理JavaScript

let autoMirrorEnabled = false;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 认证检查由auth-guard.js处理，直接加载数据
    loadDomains();
    loadStats();
    loadAutoMirrorStatus();
    
    // 设置搜索事件（如果元素存在）
    const upstreamSearch = document.getElementById('upstreamSearchInput');
    const downstreamSearch = document.getElementById('downstreamSearchInput');

    if (upstreamSearch) {
        upstreamSearch.addEventListener('input', () => filterDomains('upstream'));
    }
    if (downstreamSearch) {
        downstreamSearch.addEventListener('input', () => filterDomains('downstream'));
    }
});

// 添加上游域名
async function addUpstreamDomains() {
    const domainsText = document.getElementById('upstreamDomains').value.trim();
    if (!domainsText) {
        showMessage('请输入上游域名', 'error');
        return;
    }

    const domains = domainsText.split('\n').map(d => d.trim()).filter(d => d);
    if (domains.length === 0) {
        showMessage('请输入有效的域名', 'error');
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/batch-add', {
            method: 'POST',
            body: JSON.stringify({
                domains: domains,
                domain_type: 'upstream'
            })
        });

        const result = await response.json();
        if (result.success) {
            // 使用后端返回的信息
            showMessage(result.data?.message || '添加成功',
                       result.data?.duplicate_count > 0 ? 'warning' : 'success');

            document.getElementById('upstreamDomains').value = '';
            loadDomains();
            loadStats();
        } else {
            showMessage('添加失败: ' + (result.error || result.message || '未知错误'), 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 添加下游域名
async function addDownstreamDomains() {
    const domainsText = document.getElementById('downstreamDomains').value.trim();
    if (!domainsText) {
        showMessage('请输入下游域名', 'error');
        return;
    }

    const domains = domainsText.split('\n').map(d => d.trim()).filter(d => d);
    if (domains.length === 0) {
        showMessage('请输入有效的域名', 'error');
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/batch-add', {
            method: 'POST',
            body: JSON.stringify({
                domains: domains,
                domain_type: 'downstream'
            })
        });

        const result = await response.json();
        if (result.success) {
            // 使用后端返回的信息
            showMessage(result.data?.message || '添加成功',
                       result.data?.duplicate_count > 0 ? 'warning' : 'success');

            document.getElementById('downstreamDomains').value = '';
            loadDomains();
            loadStats();
        } else {
            showMessage('添加失败: ' + (result.error || result.message || '未知错误'), 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 切换自动镜像
async function toggleAutoMirror() {
    const enabled = document.getElementById('autoMirrorSwitch').checked;
    
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/config', {
            method: 'PUT',
            body: JSON.stringify({
                enable_recursive_discovery: enabled
            })
        });

        const result = await response.json();
        if (result.success) {
            autoMirrorEnabled = enabled;
            document.getElementById('mirrorStatus').textContent = enabled ? '开启' : '关闭';
            showMessage(`自动镜像已${enabled ? '开启' : '关闭'}`, 'success');
        } else {
            // 恢复开关状态
            document.getElementById('autoMirrorSwitch').checked = !enabled;
            showMessage('设置失败: ' + result.message, 'error');
        }
    } catch (error) {
        // 恢复开关状态
        document.getElementById('autoMirrorSwitch').checked = !enabled;
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 加载域名列表
async function loadDomains() {
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/domains');
        const result = await response.json();
        
        if (result.success) {
            // 处理不同的数据结构
            let domains = [];
            if (result.data) {
                if (Array.isArray(result.data)) {
                    domains = result.data;
                } else if (result.data.domains && Array.isArray(result.data.domains)) {
                    domains = result.data.domains;
                } else {
                    console.warn('未知的域名数据结构:', result.data);
                }
            }
            displayDomains(domains);
        } else {
            showMessage('加载域名失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('加载域名失败:', error);
        // 更新两个表格的错误状态
        const upstreamBody = document.getElementById('upstreamTableBody');
        const downstreamBody = document.getElementById('downstreamTableBody');

        if (upstreamBody) {
            upstreamBody.innerHTML = '<tr><td colspan="3" style="text-align: center; color: #dc3545;">加载失败</td></tr>';
        }
        if (downstreamBody) {
            downstreamBody.innerHTML = '<tr><td colspan="3" style="text-align: center; color: #dc3545;">加载失败</td></tr>';
        }
    }
}

// 显示域名列表
function displayDomains(domains) {
    // 确保domains是数组
    if (!Array.isArray(domains)) {
        console.warn('域名数据不是数组:', domains);
        domains = [];
    }

    // 分离上游和下游域名
    const upstreamDomains = domains.filter(domain => domain.type === 'upstream');
    const downstreamDomains = domains.filter(domain => domain.type === 'downstream');

    // 显示上游域名
    displayUpstreamDomains(upstreamDomains);

    // 显示下游域名
    displayDownstreamDomains(downstreamDomains);

    // 更新统计数据
    updateDomainStats(upstreamDomains.length, downstreamDomains.length);
}

// 显示上游域名
function displayUpstreamDomains(domains) {
    const tbody = document.getElementById('upstreamTableBody');

    if (domains.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" style="text-align: center; color: #666;">暂无上游域名</td></tr>';
        return;
    }

    tbody.innerHTML = domains.map(domain => `
        <tr>
            <td>${domain.domain}</td>
            <td>${formatDate(domain.created_at)}</td>
            <td>
                <button class="btn btn-sm btn-danger" onclick="deleteDomain('${domain.id}')">删除</button>
            </td>
        </tr>
    `).join('');
}

// 显示下游域名
function displayDownstreamDomains(domains) {
    const tbody = document.getElementById('downstreamTableBody');

    if (domains.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" style="text-align: center; color: #666;">暂无下游域名</td></tr>';
        return;
    }

    tbody.innerHTML = domains.map(domain => `
        <tr>
            <td>${domain.domain}</td>
            <td>${formatDate(domain.created_at)}</td>
            <td>
                <button class="btn btn-sm btn-danger" onclick="deleteDomain('${domain.id}')">删除</button>
            </td>
        </tr>
    `).join('');
}

// 更新域名统计
function updateDomainStats(upstreamCount, downstreamCount) {
    document.getElementById('upstreamCount').textContent = upstreamCount;
    document.getElementById('downstreamCount').textContent = downstreamCount;
}

// 清空上游域名
async function clearUpstreamDomains() {
    if (!confirm('确定要清空所有上游域名吗？此操作不可恢复！')) {
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/clear', {
            method: 'DELETE',
            body: JSON.stringify({ type: 'upstream' })
        });

        const result = await response.json();
        if (result.success) {
            showMessage('上游域名已清空', 'success');
            loadDomains();
        } else {
            showMessage('清空失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('清空失败: ' + error.message, 'error');
    }
}

// 清空下游域名
async function clearDownstreamDomains() {
    if (!confirm('确定要清空所有下游域名吗？此操作不可恢复！')) {
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/clear', {
            method: 'DELETE',
            body: JSON.stringify({ type: 'downstream' })
        });

        const result = await response.json();
        if (result.success) {
            showMessage('下游域名已清空', 'success');
            loadDomains();
        } else {
            showMessage('清空失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('清空失败: ' + error.message, 'error');
    }
}

// 删除域名
async function deleteDomain(domainId) {
    if (!confirm('确定要删除这个域名吗？')) {
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch(`/api/domain-pool/domains/${domainId}`, {
            method: 'DELETE'
        });

        const result = await response.json();
        if (result.success) {
            showMessage('域名删除成功', 'success');
            loadDomains();
            loadStats();
        } else {
            showMessage('删除失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 加载统计数据
async function loadStats() {
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/stats');
        const result = await response.json();
        
        if (result.success) {
            const data = result.data || {};
            document.getElementById('upstreamCount').textContent = data.upstream_count || 0;
            document.getElementById('downstreamCount').textContent = data.downstream_count || 0;
            document.getElementById('totalMappings').textContent = data.total_mappings || 0;
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

// 加载自动镜像状态
async function loadAutoMirrorStatus() {
    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/auto-proxy/config');
        const result = await response.json();
        
        if (result.success) {
            const config = result.data || {};
            autoMirrorEnabled = config.enable_recursive_discovery || false;
            document.getElementById('autoMirrorSwitch').checked = autoMirrorEnabled;
            document.getElementById('mirrorStatus').textContent = autoMirrorEnabled ? '开启' : '关闭';
        }
    } catch (error) {
        console.error('加载自动镜像状态失败:', error);
    }
}

// 旧的过滤函数（已废弃，保留以防兼容性问题）
function filterDomains() {
    // 这个函数已被新的分离过滤函数替代
    console.warn('filterDomains() 已废弃，请使用 filterDomains(type) 替代');
}

// 清空输入框
function clearUpstreamInput() {
    document.getElementById('upstreamDomains').value = '';
}

function clearDownstreamInput() {
    document.getElementById('downstreamDomains').value = '';
}

// 清空所有域名
async function clearAllDomains() {
    if (!confirm('确定要清空所有域名吗？此操作不可恢复！')) {
        return;
    }

    try {
        const response = await window.AuthGuard.authenticatedFetch('/api/domain-pool/clear', {
            method: 'DELETE'
        });

        const result = await response.json();
        if (result.success) {
            showMessage('所有域名已清空', 'success');
            loadDomains();
            loadStats();
        } else {
            showMessage('清空失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, 'error');
    }
}

// 显示消息
function showMessage(message, type = 'success') {
    const messageArea = document.getElementById('messageArea');
    const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
    
    messageArea.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
    
    // 3秒后自动隐藏
    setTimeout(() => {
        messageArea.innerHTML = '';
    }, 3000);
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 新的过滤函数（用于分离的表格）
function filterDomains(type) {
    const searchInput = document.getElementById(type + 'SearchInput');
    if (!searchInput) return;

    const searchTerm = searchInput.value.toLowerCase();
    const tableBody = document.getElementById(type + 'TableBody');
    if (!tableBody) return;

    const rows = tableBody.getElementsByTagName('tr');

    for (let row of rows) {
        const domainCell = row.cells[0];
        if (domainCell) {
            const domainText = domainCell.textContent.toLowerCase();
            if (domainText.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    }
}
