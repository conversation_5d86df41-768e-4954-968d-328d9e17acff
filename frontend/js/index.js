/**
 * 首页专用JavaScript
 */

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initIndexPage();
});

// 初始化首页
function initIndexPage() {
    loadSystemStatus();
    loadStats();
}



// 加载系统状态
async function loadSystemStatus() {
    try {
        const response = await fetch('/api/status');
        const result = await response.json();
        
        if (result.success) {
            document.getElementById('systemStatus').textContent = '正常';
            document.getElementById('systemStatus').style.color = '#4CAF50';
        } else {
            document.getElementById('systemStatus').textContent = '异常';
            document.getElementById('systemStatus').style.color = '#f44336';
        }
    } catch (error) {
        document.getElementById('systemStatus').textContent = '离线';
        document.getElementById('systemStatus').style.color = '#ff9800';
    }
}

// 加载统计数据
async function loadStats() {
    try {
        // 尝试获取统计数据（可能需要认证）
        let response;
        const token = localStorage.getItem('auth_token');
        
        if (token) {
            response = await fetch('/api/auto-proxy/statistics', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
        } else {
            // 未登录时显示默认值
            updateStatsDisplay(0, 0, 0);
            return;
        }
        
        const result = await response.json();
        
        if (result.success && result.data) {
            const stats = result.data;
            updateStatsDisplay(
                stats.total_requests || 0,
                stats.active_mappings || 0,
                stats.valid_certificates || 0
            );
        } else {
            updateStatsDisplay(0, 0, 0);
        }
    } catch (error) {
        console.warn('加载统计数据失败:', error);
        updateStatsDisplay(0, 0, 0);
    }
}

// 更新统计数据显示
function updateStatsDisplay(requests, domains, certificates) {
    // 更新实际存在的元素
    const totalDomainsEl = document.getElementById('totalDomains');
    const activeMappingsEl = document.getElementById('activeMappings');

    if (totalDomainsEl) {
        totalDomainsEl.textContent = domains || 0;
    }
    if (activeMappingsEl) {
        activeMappingsEl.textContent = formatNumber(requests) || 0;
    }
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 定时刷新数据
setInterval(() => {
    loadSystemStatus();
    loadStats();
}, 30000); // 30秒刷新一次
