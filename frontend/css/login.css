/* 登录页面专用样式 */
body {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.login-container {
    background: #2a2a2a;
    border-radius: 12px;
    border: 1px solid #404040;
    padding: 40px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo {
    font-size: 3rem;
    margin-bottom: 10px;
}

.login-title {
    color: #4caf50;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.login-subtitle {
    color: #b0b0b0;
    font-size: 0.9rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    color: #e0e0e0;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #555;
    border-radius: 6px;
    background: #1a1a1a;
    color: white;
    font-size: 16px;
    box-sizing: border-box;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #4caf50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.form-input::placeholder {
    color: #888;
}

.login-button {
    width: 100%;
    background: #4caf50;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.login-button:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.login-button:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
}

.error-message {
    background: #b71c1c;
    color: #f44336;
    border: 1px solid #f44336;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
    display: none;
}

.success-message {
    background: #1b5e20;
    color: #4caf50;
    border: 1px solid #4caf50;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
    display: none;
}

.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.footer {
    text-align: center;
    margin-top: 30px;
    color: #888;
    font-size: 12px;
}

@media (max-width: 480px) {
    .login-container {
        margin: 20px;
        padding: 30px 20px;
    }
}
