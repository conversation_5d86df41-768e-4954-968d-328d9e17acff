*{margin:0;padding:0;box-sizing:border-box;}body{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Robot<PERSON>,sans-serif;background:#1a1a1a;color:#e0e0e0;line-height:1.6;min-height:100vh;margin:0;padding:20px;}.container{max-width:1200px;margin:0 auto;padding:40px 20px;text-align:center;}.header{margin-bottom:50px;}.header h1{font-size:3rem;color:#4CAF50;margin-bottom:20px;text-shadow:0 2px 4px rgba(0,0,0,0.3);}.header p{font-size:1.2rem;color:#b0b0b0;margin-bottom:30px;}.nav-cards{display:grid;grid-template-columns:1fr 1fr;gap:30px;margin-bottom:50px;}.nav-card{background:linear-gradient(135deg,#2a2a2a 0%,#333 100%);border:1px solid #404040;border-radius:12px;padding:30px;text-decoration:none;color:inherit;transition:all 0.3s ease;position:relative;overflow:hidden;}.nav-card::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(76,175,80,0.1),transparent);transition:left 0.5s;}.nav-card:hover::before{left:100%;}.nav-card:hover{transform:translateY(-5px);border-color:#4CAF50;box-shadow:0 10px 30px rgba(76,175,80,0.2);}.nav-card-icon{font-size:3rem;margin-bottom:15px;display:block;}.nav-card-title{font-size:1.5rem;font-weight:600;margin-bottom:10px;color:#4CAF50;}.nav-card-description{color:#b0b0b0;line-height:1.5;}.status-bar{display:flex;justify-content:space-around;margin-bottom:40px;padding:20px;background:#2a2a2a;border-radius:8px;border:1px solid #404040;}.status-item{text-align:center;}.status-value{font-size:2rem;font-weight:bold;color:#4CAF50;display:block;}.status-label{color:#b0b0b0;font-size:0.9rem;margin-top:5px;}.footer{margin-top:50px;padding-top:30px;border-top:1px solid #404040;color:#666;font-size:0.9rem;}@media (max-width:768px){.nav-cards{grid-template-columns:1fr;gap:20px;}.header h1{font-size:2rem;}.status-bar{flex-direction:column;gap:20px;}.container{padding:20px 10px;}}@media (max-width:480px){.nav-card{padding:20px;}.nav-card-icon{font-size:2rem;}.nav-card-title{font-size:1.2rem;}}