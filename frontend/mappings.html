<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理映射管理 - SM智能代理系统</title>
    <link rel="stylesheet" href="css/common.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 代理映射管理</h1>
        </div>

        <div class="card">
            <h3>手工添加映射</h3>
            <div class="grid">
                <div class="form-group">
                    <label>下游域名:</label>
                    <input type="text" id="mappingDownstream" class="form-control" placeholder="target.com">
                    <small class="help-text">用户访问的目标域名</small>
                </div>
                <div class="form-group">
                    <label>上游域名:</label>
                    <input type="text" id="mappingUpstream" class="form-control" placeholder="source.com">
                    <small class="help-text">实际提供服务的源域名</small>
                </div>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enableSSL"> 启用SSL
                </label>
                <small class="help-text">为此映射启用HTTPS支持</small>
            </div>
            <button class="btn btn-primary" onclick="addMapping()">添加映射</button>
            <button class="btn btn-secondary" onclick="clearMappingForm()">清空表单</button>
        </div>

        <div class="card">
            <h3>映射统计</h3>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value" id="activeMappings">0</div>
                    <div class="stat-label">活跃映射</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalRequests">0</div>
                    <div class="stat-label">总请求数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="successRate">0%</div>
                    <div class="stat-label">成功率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgResponseTime">0ms</div>
                    <div class="stat-label">平均响应时间</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>活跃映射列表</h3>
            <div class="table-controls">
                <input type="text" id="searchMappings" class="form-control" placeholder="搜索映射..." style="width: 300px; display: inline-block;">
                <select id="statusFilter" class="form-control" style="width: 150px; display: inline-block; margin-left: 10px;">
                    <option value="">所有状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">非活跃</option>
                    <option value="error">错误</option>
                </select>
                <button class="btn btn-secondary" onclick="loadMappings()" style="margin-left: 10px;">刷新</button>
                <button class="btn btn-danger" onclick="clearAllMappings()" style="margin-left: 10px;">清空所有</button>
            </div>
            
            <div class="table-container">
                <table class="table" id="mappingsTable">
                    <thead>
                        <tr>
                            <th>下游域名</th>
                            <th>上游域名</th>
                            <th>状态</th>
                            <th>SSL</th>
                            <th>请求数</th>
                            <th>最后访问</th>
                            <th>响应时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="mappingsTableBody">
                        <tr>
                            <td colspan="8" style="text-align: center; color: #666;">正在加载映射数据...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="messageArea"></div>
    </div>

    <!-- 编辑映射模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑映射</h3>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>下游域名:</label>
                    <input type="text" id="editDownstream" class="form-control" placeholder="target.com">
                    <small class="help-text">用户访问的目标域名</small>
                </div>
                <div class="form-group">
                    <label>上游域名:</label>
                    <input type="text" id="editUpstream" class="form-control" placeholder="source.com">
                    <small class="help-text">实际提供服务的源域名</small>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="editSSL"> 启用SSL
                    </label>
                    <small class="help-text">为此映射启用HTTPS支持</small>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="saveMapping()">保存</button>
                <button class="btn btn-secondary" onclick="closeEditModal()">取消</button>
            </div>
        </div>
    </div>

    <script src="js/auth-guard.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/mappings.js"></script>
    <script src="js/navigation.js"></script>
</body>
</html>
