<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>递归代理控制 - SM智能代理系统</title>
    <link rel="stylesheet" href="css/common.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 递归反向代理控制</h1>
        </div>

        <div class="grid">
            <div class="card">
                <h3>递归设置</h3>
                <div class="form-group">
                    <label>递归深度:</label>
                    <input type="number" id="recursiveDepth" class="form-control" value="3" min="1" max="10">
                    <small class="help-text">最大递归层数，建议1-5层</small>
                </div>
                <div class="form-group">
                    <label>检查频率 (分钟):</label>
                    <input type="number" id="recursiveFrequency" class="form-control" value="5" min="1" max="60">
                    <small class="help-text">每隔多少分钟执行一次递归检查</small>
                </div>
                <div class="form-group">
                    <label>并发数量:</label>
                    <input type="number" id="recursiveConcurrency" class="form-control" value="10" min="1" max="100">
                    <small class="help-text">同时处理的最大连接数</small>
                </div>
                <div class="form-group">
                    <label>超时时间 (秒):</label>
                    <input type="number" id="recursiveTimeout" class="form-control" value="30" min="5" max="300">
                    <small class="help-text">单个请求的超时时间</small>
                </div>
                <div class="form-group">
                    <label>内容大小阈值 (KB):</label>
                    <input type="number" id="contentThreshold" class="form-control" value="300" min="100" max="10000">
                    <small class="help-text">认为代理成功的最小内容大小</small>
                </div>
                <button class="btn btn-primary" onclick="saveRecursiveSettings()">保存设置</button>
                <button class="btn btn-secondary" onclick="resetSettings()">重置默认</button>
            </div>

            <div class="card">
                <h3>递归控制</h3>
                <div class="form-group">
                    <label>
                        递归代理开关:
                        <label class="switch">
                            <input type="checkbox" id="recursiveSwitch" onchange="toggleRecursive()">
                            <span class="slider"></span>
                        </label>
                    </label>
                    <p class="help-text">开启后系统将按设定参数自动执行递归代理发现</p>
                </div>
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-value" id="recursiveStatus">停止</div>
                        <div class="stat-label">运行状态</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="discoveredDomains">0</div>
                        <div class="stat-label">发现域名</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="successfulMappings">0</div>
                        <div class="stat-label">成功映射</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="recursiveErrors">0</div>
                        <div class="stat-label">错误次数</div>
                    </div>
                </div>
                <button class="btn btn-secondary" onclick="loadRecursiveStatus()">刷新状态</button>
                <button class="btn btn-danger" onclick="clearRecursiveData()">清空数据</button>
                <button class="btn btn-primary" onclick="manualTrigger()">手动触发</button>
            </div>
        </div>

        <div class="card">
            <h3>递归发现历史</h3>
            <div class="table-controls">
                <input type="text" id="searchHistory" class="form-control" placeholder="搜索历史记录..." style="width: 300px; display: inline-block;">
                <select id="depthFilter" class="form-control" style="width: 150px; display: inline-block; margin-left: 10px;">
                    <option value="">所有深度</option>
                    <option value="1">深度 1</option>
                    <option value="2">深度 2</option>
                    <option value="3">深度 3</option>
                    <option value="4">深度 4</option>
                    <option value="5">深度 5</option>
                </select>
                <button class="btn btn-secondary" onclick="loadHistory()" style="margin-left: 10px;">刷新</button>
                <button class="btn btn-danger" onclick="clearHistory()" style="margin-left: 10px;">清空历史</button>
            </div>
            
            <div class="table-container">
                <table class="table" id="historyTable">
                    <thead>
                        <tr>
                            <th>发现域名</th>
                            <th>来源域名</th>
                            <th>递归深度</th>
                            <th>内容大小</th>
                            <th>响应时间</th>
                            <th>发现时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="historyTableBody">
                        <tr>
                            <td colspan="7" style="text-align: center; color: #666;">暂无递归发现记录</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="card">
            <h3>递归日志</h3>
            <div class="log-controls">
                <button class="btn btn-secondary" onclick="clearLog()">清空日志</button>
                <button class="btn btn-secondary" onclick="toggleAutoScroll()">
                    <span id="autoScrollText">开启自动滚动</span>
                </button>
                <label>
                    <input type="checkbox" id="verboseLog"> 详细日志
                </label>
            </div>
            <div id="recursiveLog" class="log-container">
                <div style="color: #666;">等待递归代理启动...</div>
            </div>
        </div>

        <div id="messageArea"></div>
    </div>

    <script src="js/auth-guard.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/recursive.js"></script>
    <script src="js/navigation.js"></script>
</body>
</html>
