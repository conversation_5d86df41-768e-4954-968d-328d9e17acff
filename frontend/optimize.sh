#!/bin/bash

# 前端资源优化脚本
# 压缩CSS和JavaScript文件以提高加载速度

echo "🚀 开始优化前端资源..."

# 检查是否安装了必要的工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装: sudo apt install $2"
        return 1
    fi
    return 0
}

# 创建压缩版本的CSS文件
compress_css() {
    local input_file=$1
    local output_file="${input_file%.css}.min.css"
    
    if [ -f "$input_file" ]; then
        echo "📦 压缩 $input_file..."
        # 简单的CSS压缩：移除注释、多余空格和换行
        sed 's|/\*[^*]*\*\+\([^/*][^*]*\*\+\)*/||g' "$input_file" | \
        tr -d '\n\r' | \
        sed 's/  \+/ /g' | \
        sed 's/ *{ */{/g' | \
        sed 's/ *} */}/g' | \
        sed 's/ *; */;/g' | \
        sed 's/ *: */:/g' | \
        sed 's/ *, */,/g' > "$output_file"
        
        echo "✅ 生成 $output_file"
        
        # 显示压缩比例
        original_size=$(wc -c < "$input_file")
        compressed_size=$(wc -c < "$output_file")
        reduction=$((100 - (compressed_size * 100 / original_size)))
        echo "   压缩率: ${reduction}% (${original_size} → ${compressed_size} bytes)"
    fi
}

# 压缩JavaScript文件
compress_js() {
    local input_file=$1
    local output_file="${input_file%.js}.min.js"
    
    if [ -f "$input_file" ]; then
        echo "📦 压缩 $input_file..."
        # 简单的JS压缩：移除注释、多余空格和换行
        sed 's|//.*$||g' "$input_file" | \
        sed 's|/\*[^*]*\*\+\([^/*][^*]*\*\+\)*/||g' | \
        tr -d '\n\r' | \
        sed 's/  \+/ /g' | \
        sed 's/ *{ */{/g' | \
        sed 's/ *} */}/g' | \
        sed 's/ *; */;/g' | \
        sed 's/ *, */,/g' > "$output_file"
        
        echo "✅ 生成 $output_file"
        
        # 显示压缩比例
        original_size=$(wc -c < "$input_file")
        compressed_size=$(wc -c < "$output_file")
        reduction=$((100 - (compressed_size * 100 / original_size)))
        echo "   压缩率: ${reduction}% (${original_size} → ${compressed_size} bytes)"
    fi
}

# 进入前端目录
cd "$(dirname "$0")"

echo "📁 当前目录: $(pwd)"

# 压缩CSS文件
echo ""
echo "🎨 压缩CSS文件..."
for css_file in css/*.css; do
    if [[ "$css_file" != *.min.css ]]; then
        compress_css "$css_file"
    fi
done

# 压缩JavaScript文件
echo ""
echo "⚡ 压缩JavaScript文件..."
for js_file in js/*.js; do
    if [[ "$js_file" != *.min.js ]]; then
        compress_js "$js_file"
    fi
done

# 创建资源版本文件（用于缓存控制）
echo ""
echo "🏷️  生成资源版本信息..."
version=$(date +%Y%m%d%H%M%S)
echo "RESOURCE_VERSION=$version" > version.txt
echo "✅ 资源版本: $version"

echo ""
echo "🎉 前端资源优化完成！"
echo ""
echo "📊 优化建议："
echo "   1. 在生产环境中使用 .min.css 和 .min.js 文件"
echo "   2. 配置Web服务器启用gzip压缩"
echo "   3. 设置适当的缓存头"
echo "   4. 考虑使用CDN加速静态资源"
