<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端错误检测工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
    </style>
</head>
<body>
    <h1>🔍 SM代理系统前端错误检测工具</h1>
    
    <div class="test-section info">
        <h3>🛠️ 检测工具</h3>
        <button class="btn-primary" onclick="runAllTests()">运行所有检测</button>
        <button class="btn-success" onclick="testAPIs()">测试API连接</button>
        <button class="btn-warning" onclick="testAuth()">测试认证</button>
        <button class="btn-danger" onclick="testDOMElements()">检查DOM元素</button>
    </div>

    <div id="results"></div>

    <script>
        // 测试结果容器
        const resultsContainer = document.getElementById('results');

        // 添加测试结果
        function addResult(title, status, message, details = '') {
            const div = document.createElement('div');
            div.className = `test-section ${status}`;
            div.innerHTML = `
                <h3>${getStatusIcon(status)} ${title}</h3>
                <p>${message}</p>
                ${details ? `<pre>${details}</pre>` : ''}
            `;
            resultsContainer.appendChild(div);
        }

        function getStatusIcon(status) {
            switch(status) {
                case 'success': return '✅';
                case 'error': return '❌';
                case 'warning': return '⚠️';
                case 'info': return 'ℹ️';
                default: return '🔍';
            }
        }

        // 清空结果
        function clearResults() {
            resultsContainer.innerHTML = '';
        }

        // 测试API连接
        async function testAPIs() {
            addResult('API连接测试', 'info', '开始测试API连接...');

            const apis = [
                { name: '系统状态', url: '/api/status' },
                { name: '管理员检查', url: '/api/auth/check_admin_exists' },
                { name: '自动代理配置', url: '/api/auto-proxy/config' },
                { name: '域名列表', url: '/api/domain-pool/domains', needAuth: true },
                { name: '映射列表', url: '/api/auto-proxy/mappings', needAuth: true }
            ];

            for (const api of apis) {
                try {
                    const options = {};
                    if (api.needAuth) {
                        const token = localStorage.getItem('auth_token');
                        if (token) {
                            options.headers = { 'Authorization': `Bearer ${token}` };
                        }
                    }

                    const response = await fetch(api.url, options);
                    const data = await response.json();
                    
                    if (response.ok) {
                        addResult(
                            `API: ${api.name}`, 
                            'success', 
                            `状态码: ${response.status}`,
                            JSON.stringify(data, null, 2)
                        );
                    } else {
                        addResult(
                            `API: ${api.name}`, 
                            'warning', 
                            `状态码: ${response.status}`,
                            JSON.stringify(data, null, 2)
                        );
                    }
                } catch (error) {
                    addResult(
                        `API: ${api.name}`, 
                        'error', 
                        `连接失败: ${error.message}`
                    );
                }
            }
        }

        // 测试认证状态
        async function testAuth() {
            addResult('认证状态测试', 'info', '检查认证状态...');

            // 检查localStorage中的token
            const token = localStorage.getItem('auth_token');
            if (token) {
                addResult('认证Token', 'success', 'Token存在', `Token: ${token.substring(0, 20)}...`);
                
                // 测试token有效性
                try {
                    const response = await fetch('/api/domain-pool/domains', {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });
                    
                    if (response.status === 401) {
                        addResult('Token验证', 'error', 'Token已过期或无效');
                    } else if (response.ok) {
                        addResult('Token验证', 'success', 'Token有效');
                    } else {
                        addResult('Token验证', 'warning', `未知状态: ${response.status}`);
                    }
                } catch (error) {
                    addResult('Token验证', 'error', `验证失败: ${error.message}`);
                }
            } else {
                addResult('认证Token', 'warning', '未找到认证Token，需要登录');
            }

            // 检查认证相关函数
            if (window.AuthGuard) {
                addResult('AuthGuard', 'success', 'AuthGuard对象存在');
                
                if (typeof window.AuthGuard.authenticatedFetch === 'function') {
                    addResult('authenticatedFetch', 'success', 'authenticatedFetch函数可用');
                } else {
                    addResult('authenticatedFetch', 'error', 'authenticatedFetch函数不存在');
                }
            } else {
                addResult('AuthGuard', 'error', 'AuthGuard对象不存在');
            }
        }

        // 检查DOM元素
        function testDOMElements() {
            addResult('DOM元素检测', 'info', '检查页面DOM元素...');

            const requiredElements = [
                'upstreamTableBody',
                'downstreamTableBody', 
                'upstreamDomains',
                'downstreamDomains',
                'messageArea'
            ];

            for (const elementId of requiredElements) {
                const element = document.getElementById(elementId);
                if (element) {
                    addResult(
                        `DOM元素: ${elementId}`, 
                        'success', 
                        `元素存在: ${element.tagName}`
                    );
                } else {
                    addResult(
                        `DOM元素: ${elementId}`, 
                        'error', 
                        '元素不存在'
                    );
                }
            }

            // 检查JavaScript函数
            const requiredFunctions = [
                'loadDomains',
                'addUpstreamDomains',
                'addDownstreamDomains',
                'displayDomains'
            ];

            for (const funcName of requiredFunctions) {
                if (typeof window[funcName] === 'function') {
                    addResult(
                        `函数: ${funcName}`, 
                        'success', 
                        '函数存在'
                    );
                } else {
                    addResult(
                        `函数: ${funcName}`, 
                        'error', 
                        '函数不存在'
                    );
                }
            }
        }

        // 运行所有测试
        async function runAllTests() {
            clearResults();
            addResult('开始检测', 'info', '正在运行所有检测项目...');
            
            testDOMElements();
            await testAuth();
            await testAPIs();
            
            addResult('检测完成', 'success', '所有检测项目已完成');
        }

        // 页面加载时的初始化
        window.addEventListener('DOMContentLoaded', () => {
            addResult('检测工具就绪', 'success', '点击上方按钮开始检测');
        });
    </script>
</body>
</html>
