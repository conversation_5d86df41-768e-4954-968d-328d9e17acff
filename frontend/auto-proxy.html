<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动代理管理 - SM智能代理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/managers.css">
    <style>
        .auto-proxy-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .section-card {
            background: #2a2a2a;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #404040;
        }

        .section-title {
            font-size: 1.5rem;
            color: #4CAF50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #333;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #444;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 4px;
        }

        .stat-label {
            color: #b0b0b0;
            font-size: 0.9rem;
        }

        .config-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            color: #e0e0e0;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 6px;
            background: #333;
            color: #e0e0e0;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #555;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #4CAF50;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
        }

        .btn-secondary {
            background: #666;
            color: white;
        }

        .btn-secondary:hover {
            background: #777;
        }

        .btn-danger {
            background: #f44336;
            color: white;
        }

        .btn-danger:hover {
            background: #da190b;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            border: 1px solid #444;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #333;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #444;
        }

        .table th {
            background: #2a2a2a;
            color: #4CAF50;
            font-weight: 600;
        }

        .table tr:hover {
            background: #3a3a3a;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #4CAF50;
            color: white;
        }

        .status-pending {
            background: #ff9800;
            color: white;
        }

        .status-error {
            background: #f44336;
            color: white;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #444;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            background: #4CAF50;
            transition: width 0.3s ease;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 16px;
            border-left: 4px solid;
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .alert-warning {
            background: rgba(255, 152, 0, 0.1);
            border-color: #ff9800;
            color: #ff9800;
        }

        .alert-error {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #2a2a2a;
            margin: 5% auto;
            padding: 24px;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            border: 1px solid #444;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #fff;
        }

        @media (max-width: 768px) {
            .config-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="auto-proxy-container">
        <!-- 页面标题 -->
        <div class="section-card">
            <h1 class="section-title">
                🤖 自动代理管理
            </h1>
            <p style="color: #b0b0b0; margin-bottom: 0;">
                智能域名映射、SSL证书自动申请、内容替换和缓存优化
            </p>
        </div>

        <!-- 系统状态统计 -->
        <div class="section-card">
            <h2 class="section-title">📊 系统状态</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalMappings">0</div>
                    <div class="stat-label">域名映射</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="activeMappings">0</div>
                    <div class="stat-label">活跃映射</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalCertificates">0</div>
                    <div class="stat-label">SSL证书</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="validCertificates">0</div>
                    <div class="stat-label">有效证书</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalRequests">0</div>
                    <div class="stat-label">总请求数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="cacheHitRate">0%</div>
                    <div class="stat-label">缓存命中率</div>
                </div>
            </div>
        </div>

        <!-- 自动代理配置 -->
        <div class="section-card">
            <h2 class="section-title">⚙️ 自动代理配置</h2>
            <div class="config-grid">
                <div>
                    <div class="form-group">
                        <label>启用自动SSL申请</label>
                        <label class="toggle-switch">
                            <input type="checkbox" id="enableAutoSsl" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label>启用内容替换</label>
                        <label class="toggle-switch">
                            <input type="checkbox" id="enableContentReplacement" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label>每个根域名最大子域名数量</label>
                        <input type="number" id="maxSubdomains" value="100" min="1" max="1000">
                    </div>
                    
                    <div class="form-group">
                        <label>最大重定向跳数</label>
                        <input type="number" id="maxRedirectHops" value="3" min="1" max="10">
                    </div>
                </div>
                
                <div>
                    <div class="form-group">
                        <label>SSL证书存储目录</label>
                        <input type="text" id="sslCertDir" value="certs" placeholder="证书存储路径">
                    </div>
                    
                    <div class="form-group">
                        <label>联系邮箱</label>
                        <input type="email" id="contactEmail" placeholder="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label>DNS提供商</label>
                        <select id="dnsProvider">
                            <option value="aliyun">阿里云DNS</option>
                            <option value="cloudflare">Cloudflare</option>
                            <option value="dnspod">DNSPod</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>DNS Access Key ID</label>
                        <input type="text" id="dnsAccessKeyId" placeholder="DNS API密钥ID">
                    </div>
                </div>
            </div>
            
            <div style="margin-top: 20px;">
                <button class="btn btn-primary" onclick="saveAutoProxyConfig()">保存配置</button>
                <button class="btn btn-secondary" onclick="loadAutoProxyConfig()">重新加载</button>
            </div>
        </div>

        <!-- 递归代理配置 -->
        <div class="section-card">
            <h2 class="section-title">🔄 递归代理配置</h2>
            <div class="config-grid">
                <div>
                    <div class="form-group">
                        <label>启用递归代理</label>
                        <label class="toggle-switch">
                            <input type="checkbox" id="enableRecursive" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label>最大递归深度</label>
                        <input type="number" id="maxDepth" value="3" min="1" max="10">
                    </div>
                    
                    <div class="form-group">
                        <label>检查间隔（分钟）</label>
                        <input type="number" id="checkInterval" value="5" min="1" max="60">
                    </div>
                </div>
                
                <div>
                    <div class="form-group">
                        <label>并发数量</label>
                        <input type="number" id="concurrency" value="10" min="1" max="100">
                    </div>
                    
                    <div class="form-group">
                        <label>超时时间（秒）</label>
                        <input type="number" id="timeoutSeconds" value="30" min="5" max="300">
                    </div>
                    
                    <div class="form-group">
                        <label>内容大小阈值（KB）</label>
                        <input type="number" id="contentThreshold" value="300" min="1" max="10000">
                    </div>
                </div>
            </div>
            
            <div style="margin-top: 20px;">
                <button class="btn btn-primary" onclick="triggerRecursiveDiscovery()">触发发现</button>
                <button class="btn btn-secondary" onclick="clearCache()">清空缓存</button>
            </div>
        </div>

        <!-- 域名映射管理 -->
        <div class="section-card">
            <h2 class="section-title">🌐 域名映射管理</h2>
            <div style="margin-bottom: 16px;">
                <button class="btn btn-primary" onclick="showCreateMappingModal()">创建映射</button>
                <button class="btn btn-secondary" onclick="refreshMappings()">刷新列表</button>
            </div>
            
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>上游域名</th>
                            <th>下游域名</th>
                            <th>状态</th>
                            <th>SSL状态</th>
                            <th>请求数</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="mappingsTableBody">
                        <!-- 动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- SSL证书管理 -->
        <div class="section-card">
            <h2 class="section-title">🔒 SSL证书管理</h2>
            <div style="margin-bottom: 16px;">
                <button class="btn btn-primary" onclick="showRequestCertModal()">申请证书</button>
                <button class="btn btn-secondary" onclick="refreshCertificates()">刷新列表</button>
            </div>
            
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>域名</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>过期时间</th>
                            <th>剩余天数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="certificatesTableBody">
                        <!-- 动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 创建域名映射模态框 -->
    <div id="createMappingModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('createMappingModal')">&times;</span>
            <h2>创建域名映射</h2>
            <div class="form-group">
                <label>上游域名</label>
                <input type="text" id="upstreamDomain" placeholder="example.com">
            </div>
            <div style="margin-top: 20px;">
                <button class="btn btn-primary" onclick="createDomainMapping()">创建映射</button>
                <button class="btn btn-secondary" onclick="closeModal('createMappingModal')">取消</button>
            </div>
        </div>
    </div>

    <!-- 申请SSL证书模态框 -->
    <div id="requestCertModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('requestCertModal')">&times;</span>
            <h2>申请SSL证书</h2>
            <div class="form-group">
                <label>域名</label>
                <input type="text" id="certDomain" placeholder="example.com">
            </div>
            <div class="form-group">
                <label>强制续期</label>
                <label class="toggle-switch">
                    <input type="checkbox" id="forceRenewal">
                    <span class="slider"></span>
                </label>
            </div>
            <div style="margin-top: 20px;">
                <button class="btn btn-primary" onclick="requestSslCertificate()">申请证书</button>
                <button class="btn btn-secondary" onclick="closeModal('requestCertModal')">取消</button>
            </div>
        </div>
    </div>

    <script src="js/auth-guard.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/auto-proxy.js"></script>
</body>
</html>
