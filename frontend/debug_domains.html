<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>域名管理页面检测工具</title>
    <link rel="stylesheet" href="css/common.css">
    <style>
        .debug-panel { 
            position: fixed; 
            top: 10px; 
            right: 10px; 
            width: 400px; 
            background: white; 
            border: 2px solid #007bff; 
            border-radius: 8px; 
            padding: 15px; 
            box-shadow: 0 4px 8px rgba(0,0,0,0.2); 
            z-index: 9999;
            max-height: 80vh;
            overflow-y: auto;
        }
        .debug-title { 
            color: #007bff; 
            margin: 0 0 10px 0; 
            font-size: 16px; 
            font-weight: bold; 
        }
        .debug-result { 
            margin: 8px 0; 
            padding: 8px; 
            border-radius: 4px; 
            font-size: 12px; 
        }
        .debug-success { background: #d4edda; color: #155724; }
        .debug-error { background: #f8d7da; color: #721c24; }
        .debug-warning { background: #fff3cd; color: #856404; }
        .debug-info { background: #d1ecf1; color: #0c5460; }
        .debug-btn { 
            padding: 5px 10px; 
            margin: 2px; 
            border: none; 
            border-radius: 3px; 
            cursor: pointer; 
            font-size: 11px; 
        }
        .debug-btn-primary { background: #007bff; color: white; }
        .debug-btn-success { background: #28a745; color: white; }
        .debug-btn-danger { background: #dc3545; color: white; }
        .debug-close { 
            position: absolute; 
            top: 5px; 
            right: 10px; 
            background: none; 
            border: none; 
            font-size: 18px; 
            cursor: pointer; 
        }
    </style>
</head>
<body>
    <!-- 包含完整的domains.html内容 -->
    <div class="container">
        <h1>🌐 域名池管理</h1>

        <!-- 统计信息 -->
        <div class="card">
            <h3>📊 域名池统计</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="upstreamCount">0</div>
                    <div class="stat-label">上游域名</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="downstreamCount">0</div>
                    <div class="stat-label">下游域名</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="mirrorStatus">关闭</div>
                    <div class="stat-label">自动镜像</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalMappings">0</div>
                    <div class="stat-label">已创建映射</div>
                </div>
            </div>
        </div>

        <!-- 批量添加 -->
        <div class="grid">
            <div class="card">
                <h3>批量添加下游域名</h3>
                <div class="form-group">
                    <label>下游域名列表（每行一个）:</label>
                    <textarea id="downstreamDomains" class="form-control" placeholder="target1.com&#10;target2.com&#10;target3.com" rows="8"></textarea>
                </div>
                <button class="btn btn-primary" onclick="addDownstreamDomains()">添加下游域名</button>
                <button class="btn btn-secondary" onclick="clearDownstreamInput()">清空输入</button>
            </div>

            <div class="card">
                <h3>批量添加上游域名</h3>
                <div class="form-group">
                    <label>上游域名列表（每行一个）:</label>
                    <textarea id="upstreamDomains" class="form-control" placeholder="example1.com&#10;example2.com&#10;example3.com" rows="8"></textarea>
                </div>
                <button class="btn btn-primary" onclick="addUpstreamDomains()">添加上游域名</button>
                <button class="btn btn-secondary" onclick="clearUpstreamInput()">清空输入</button>
            </div>
        </div>

        <!-- 域名列表 - 并排显示 -->
        <div style="display: flex; gap: 20px;">
            <!-- 下游域名列表 (左侧) -->
            <div class="card" style="flex: 1;">
                <h3>🔽 下游域名列表</h3>
                <div class="table-controls">
                    <input type="text" id="downstreamSearchInput" class="form-control" placeholder="搜索下游域名..." style="width: 250px; display: inline-block;">
                    <button class="btn btn-secondary" onclick="loadDomains()" style="margin-left: 10px;">刷新</button>
                    <button class="btn btn-danger" onclick="clearDownstreamDomains()" style="margin-left: 10px;">清空下游</button>
                </div>

                <div class="table-container">
                    <table class="table" id="downstreamTable">
                        <thead>
                            <tr>
                                <th>域名</th>
                                <th>添加时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="downstreamTableBody">
                            <tr>
                                <td colspan="3" style="text-align: center; color: #666;">正在加载下游域名...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 上游域名列表 (右侧) -->
            <div class="card" style="flex: 1;">
                <h3>🔼 上游域名列表</h3>
                <div class="table-controls">
                    <input type="text" id="upstreamSearchInput" class="form-control" placeholder="搜索上游域名..." style="width: 250px; display: inline-block;">
                    <button class="btn btn-secondary" onclick="loadDomains()" style="margin-left: 10px;">刷新</button>
                    <button class="btn btn-danger" onclick="clearUpstreamDomains()" style="margin-left: 10px;">清空上游</button>
                </div>

                <div class="table-container">
                    <table class="table" id="upstreamTable">
                        <thead>
                            <tr>
                                <th>域名</th>
                                <th>添加时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="upstreamTableBody">
                            <tr>
                                <td colspan="3" style="text-align: center; color: #666;">正在加载上游域名...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div id="messageArea"></div>
    </div>

    <!-- 调试面板 -->
    <div class="debug-panel" id="debugPanel">
        <button class="debug-close" onclick="toggleDebugPanel()">×</button>
        <h3 class="debug-title">🔍 实时检测面板</h3>
        <div>
            <button class="debug-btn debug-btn-primary" onclick="runAllChecks()">运行所有检测</button>
            <button class="debug-btn debug-btn-success" onclick="checkDOMElements()">检查DOM</button>
            <button class="debug-btn debug-btn-danger" onclick="checkJSFunctions()">检查函数</button>
        </div>
        <div id="debugResults"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/auth-guard.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/domains.js"></script>
    <script src="js/navigation.js"></script>

    <!-- 调试脚本 -->
    <script>
        const debugResults = document.getElementById('debugResults');

        function addDebugResult(type, message) {
            const div = document.createElement('div');
            div.className = `debug-result debug-${type}`;
            div.textContent = message;
            debugResults.appendChild(div);
            
            // 限制结果数量
            if (debugResults.children.length > 20) {
                debugResults.removeChild(debugResults.firstChild);
            }
        }

        function clearDebugResults() {
            debugResults.innerHTML = '';
        }

        function checkDOMElements() {
            clearDebugResults();
            addDebugResult('info', '开始检查DOM元素...');

            const elements = [
                'upstreamTableBody', 'downstreamTableBody', 
                'upstreamDomains', 'downstreamDomains', 'messageArea'
            ];

            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    addDebugResult('success', `✅ ${id}: 存在`);
                } else {
                    addDebugResult('error', `❌ ${id}: 不存在`);
                }
            });
        }

        function checkJSFunctions() {
            addDebugResult('info', '开始检查JavaScript函数...');

            const functions = [
                'loadDomains', 'addUpstreamDomains', 
                'addDownstreamDomains', 'displayDomains'
            ];

            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addDebugResult('success', `✅ ${funcName}: 存在`);
                } else {
                    addDebugResult('error', `❌ ${funcName}: 不存在`);
                }
            });

            // 检查AuthGuard
            if (window.AuthGuard) {
                addDebugResult('success', '✅ AuthGuard: 存在');
            } else {
                addDebugResult('error', '❌ AuthGuard: 不存在');
            }
        }

        async function testAPI() {
            addDebugResult('info', '开始测试API...');
            
            try {
                const response = await fetch('/api/domain-pool/domains');
                if (response.ok) {
                    const data = await response.json();
                    addDebugResult('success', `✅ API调用成功: ${JSON.stringify(data).substring(0, 50)}...`);
                } else {
                    addDebugResult('warning', `⚠️ API返回: ${response.status}`);
                }
            } catch (error) {
                addDebugResult('error', `❌ API错误: ${error.message}`);
            }
        }

        function runAllChecks() {
            clearDebugResults();
            checkDOMElements();
            checkJSFunctions();
            testAPI();
        }

        function toggleDebugPanel() {
            const panel = document.getElementById('debugPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        // 页面加载时自动运行检测
        window.addEventListener('DOMContentLoaded', () => {
            setTimeout(runAllChecks, 1000); // 延迟1秒确保所有脚本加载完成
        });
    </script>
</body>
</html>
