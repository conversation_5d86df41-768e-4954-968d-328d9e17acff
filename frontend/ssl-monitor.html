<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL证书监控 - SM智能代理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        .ssl-monitor-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .section-card {
            background: #2a2a2a;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #404040;
        }

        .section-title {
            font-size: 1.5rem;
            color: #4CAF50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .cert-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .cert-card {
            background: #333;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #444;
            transition: all 0.3s ease;
        }

        .cert-card:hover {
            border-color: #4CAF50;
            transform: translateY(-2px);
        }

        .cert-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 16px;
        }

        .cert-domain {
            font-size: 1.2rem;
            font-weight: bold;
            color: #e0e0e0;
        }

        .cert-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-valid {
            background: #4CAF50;
            color: white;
        }

        .status-warning {
            background: #ff9800;
            color: white;
        }

        .status-expired {
            background: #f44336;
            color: white;
        }

        .status-pending {
            background: #2196F3;
            color: white;
        }

        .cert-info {
            margin-bottom: 16px;
        }

        .cert-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .cert-info-label {
            color: #b0b0b0;
        }

        .cert-info-value {
            color: #e0e0e0;
            font-weight: 500;
        }

        .cert-progress {
            margin-bottom: 16px;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
            font-size: 14px;
            color: #b0b0b0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #444;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .progress-valid {
            background: #4CAF50;
        }

        .progress-warning {
            background: #ff9800;
        }

        .progress-danger {
            background: #f44336;
        }

        .cert-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
        }

        .btn-secondary {
            background: #666;
            color: white;
        }

        .btn-secondary:hover {
            background: #777;
        }

        .btn-warning {
            background: #ff9800;
            color: white;
        }

        .btn-warning:hover {
            background: #f57c00;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #333;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #444;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #b0b0b0;
            font-size: 0.9rem;
        }

        .stat-valid .stat-value {
            color: #4CAF50;
        }

        .stat-warning .stat-value {
            color: #ff9800;
        }

        .stat-expired .stat-value {
            color: #f44336;
        }

        .stat-pending .stat-value {
            color: #2196F3;
        }

        .filter-bar {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-group label {
            color: #b0b0b0;
            font-size: 14px;
        }

        .filter-group select,
        .filter-group input {
            padding: 6px 12px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #333;
            color: #e0e0e0;
            font-size: 14px;
        }

        .auto-renewal-info {
            background: #1a4d1a;
            border: 1px solid #4CAF50;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .auto-renewal-info h3 {
            color: #4CAF50;
            margin-bottom: 8px;
        }

        .auto-renewal-info p {
            color: #b0b0b0;
            margin: 0;
        }

        @media (max-width: 768px) {
            .cert-grid {
                grid-template-columns: 1fr;
            }
            
            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .filter-bar {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="ssl-monitor-container">
        <!-- 统一导航栏 -->
        <div class="section-card">
            <h1 class="section-title">
                🔒 SSL证书监控
            </h1>
            <p style="color: #b0b0b0; margin-bottom: 0;">
                实时监控SSL证书状态，自动续期管理，确保服务安全稳定运行
            </p>
        </div>

        <!-- 自动续期信息 -->
        <div class="auto-renewal-info">
            <h3>🤖 自动续期已启用</h3>
            <p>系统将在证书过期前30天自动开始续期流程，确保服务不中断。支持通配符证书和DNS-01验证。</p>
        </div>

        <!-- 统计概览 -->
        <div class="section-card">
            <h2 class="section-title">📊 证书统计</h2>
            <div class="summary-stats">
                <div class="stat-card stat-valid">
                    <div class="stat-value" id="validCerts">0</div>
                    <div class="stat-label">有效证书</div>
                </div>
                <div class="stat-card stat-warning">
                    <div class="stat-value" id="warningCerts">0</div>
                    <div class="stat-label">即将过期</div>
                </div>
                <div class="stat-card stat-expired">
                    <div class="stat-value" id="expiredCerts">0</div>
                    <div class="stat-label">已过期</div>
                </div>
                <div class="stat-card stat-pending">
                    <div class="stat-value" id="pendingCerts">0</div>
                    <div class="stat-label">申请中</div>
                </div>
            </div>
        </div>

        <!-- 过滤器 -->
        <div class="section-card">
            <div class="filter-bar">
                <div class="filter-group">
                    <label>状态筛选:</label>
                    <select id="statusFilter">
                        <option value="">全部</option>
                        <option value="valid">有效</option>
                        <option value="warning">即将过期</option>
                        <option value="expired">已过期</option>
                        <option value="pending">申请中</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>域名搜索:</label>
                    <input type="text" id="domainSearch" placeholder="输入域名关键词">
                </div>
                <div class="filter-group">
                    <button class="btn btn-primary" onclick="refreshCertificates()">刷新</button>
                    <button class="btn btn-secondary" onclick="exportCertificates()">导出</button>
                </div>
            </div>
        </div>

        <!-- 证书列表 -->
        <div class="section-card">
            <h2 class="section-title">🔐 证书详情</h2>
            <div class="cert-grid" id="certificatesGrid">
                <!-- 动态加载证书卡片 -->
            </div>
        </div>
    </div>

    <script>
        class SSLMonitor {
            constructor() {
                this.certificates = [];
                this.init();
            }

            async init() {
                await this.loadCertificates();
                this.setupEventListeners();
                
                // 设置定时刷新
                setInterval(() => {
                    this.loadCertificates();
                }, 60000); // 1分钟刷新一次
            }

            setupEventListeners() {
                document.getElementById('statusFilter').addEventListener('change', () => {
                    this.filterCertificates();
                });
                
                document.getElementById('domainSearch').addEventListener('input', () => {
                    this.filterCertificates();
                });
            }

            async loadCertificates() {
                try {
                    const response = await fetch('/api/auto-proxy/certificates');
                    const result = await response.json();
                    
                    if (result.success && result.data) {
                        this.certificates = result.data;
                        this.updateStatistics();
                        this.renderCertificates();
                    }
                } catch (error) {
                    console.error('加载证书失败:', error);
                }
            }

            updateStatistics() {
                const stats = {
                    valid: 0,
                    warning: 0,
                    expired: 0,
                    pending: 0
                };

                this.certificates.forEach(cert => {
                    const daysLeft = this.calculateDaysLeft(cert.expires_at);
                    
                    if (cert.status === 'pending') {
                        stats.pending++;
                    } else if (daysLeft < 0) {
                        stats.expired++;
                    } else if (daysLeft <= 30) {
                        stats.warning++;
                    } else {
                        stats.valid++;
                    }
                });

                document.getElementById('validCerts').textContent = stats.valid;
                document.getElementById('warningCerts').textContent = stats.warning;
                document.getElementById('expiredCerts').textContent = stats.expired;
                document.getElementById('pendingCerts').textContent = stats.pending;
            }

            renderCertificates() {
                const grid = document.getElementById('certificatesGrid');
                grid.innerHTML = '';

                this.certificates.forEach(cert => {
                    const certCard = this.createCertificateCard(cert);
                    grid.appendChild(certCard);
                });
            }

            createCertificateCard(cert) {
                const daysLeft = this.calculateDaysLeft(cert.expires_at);
                const status = this.getCertificateStatus(cert, daysLeft);
                const progressPercent = this.calculateProgress(cert.created_at, cert.expires_at);

                const card = document.createElement('div');
                card.className = 'cert-card';
                card.innerHTML = `
                    <div class="cert-header">
                        <div class="cert-domain">${cert.domain}</div>
                        <div class="cert-status status-${status}">${this.getStatusText(status)}</div>
                    </div>
                    
                    <div class="cert-info">
                        <div class="cert-info-item">
                            <span class="cert-info-label">创建时间:</span>
                            <span class="cert-info-value">${this.formatDate(cert.created_at)}</span>
                        </div>
                        <div class="cert-info-item">
                            <span class="cert-info-label">过期时间:</span>
                            <span class="cert-info-value">${this.formatDate(cert.expires_at)}</span>
                        </div>
                        <div class="cert-info-item">
                            <span class="cert-info-label">剩余天数:</span>
                            <span class="cert-info-value">${daysLeft >= 0 ? daysLeft + '天' : '已过期'}</span>
                        </div>
                    </div>
                    
                    <div class="cert-progress">
                        <div class="progress-label">
                            <span>证书有效期</span>
                            <span>${progressPercent}%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill progress-${this.getProgressClass(daysLeft)}" 
                                 style="width: ${progressPercent}%"></div>
                        </div>
                    </div>
                    
                    <div class="cert-actions">
                        <button class="btn btn-primary" onclick="renewCertificate('${cert.domain}')">续期</button>
                        <button class="btn btn-secondary" onclick="viewCertDetails('${cert.domain}')">详情</button>
                        ${daysLeft <= 30 ? '<button class="btn btn-warning">即将过期</button>' : ''}
                    </div>
                `;

                return card;
            }

            filterCertificates() {
                const statusFilter = document.getElementById('statusFilter').value;
                const domainSearch = document.getElementById('domainSearch').value.toLowerCase();

                const filteredCerts = this.certificates.filter(cert => {
                    const daysLeft = this.calculateDaysLeft(cert.expires_at);
                    const status = this.getCertificateStatus(cert, daysLeft);
                    
                    const statusMatch = !statusFilter || status === statusFilter;
                    const domainMatch = !domainSearch || cert.domain.toLowerCase().includes(domainSearch);
                    
                    return statusMatch && domainMatch;
                });

                this.renderFilteredCertificates(filteredCerts);
            }

            renderFilteredCertificates(certificates) {
                const grid = document.getElementById('certificatesGrid');
                grid.innerHTML = '';

                certificates.forEach(cert => {
                    const certCard = this.createCertificateCard(cert);
                    grid.appendChild(certCard);
                });
            }

            getCertificateStatus(cert, daysLeft) {
                if (cert.status === 'pending') return 'pending';
                if (daysLeft < 0) return 'expired';
                if (daysLeft <= 30) return 'warning';
                return 'valid';
            }

            getStatusText(status) {
                const statusMap = {
                    'valid': '有效',
                    'warning': '即将过期',
                    'expired': '已过期',
                    'pending': '申请中'
                };
                return statusMap[status] || status;
            }

            getProgressClass(daysLeft) {
                if (daysLeft < 0) return 'danger';
                if (daysLeft <= 30) return 'warning';
                return 'valid';
            }

            calculateDaysLeft(expiresAt) {
                if (!expiresAt) return 0;
                const now = new Date();
                const expires = new Date(expiresAt);
                const diffTime = expires - now;
                return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            }

            calculateProgress(createdAt, expiresAt) {
                if (!createdAt || !expiresAt) return 0;
                
                const created = new Date(createdAt);
                const expires = new Date(expiresAt);
                const now = new Date();
                
                const totalDuration = expires - created;
                const elapsed = now - created;
                
                return Math.max(0, Math.min(100, (elapsed / totalDuration) * 100)).toFixed(1);
            }

            formatDate(dateString) {
                if (!dateString) return '-';
                const date = new Date(dateString);
                return date.toLocaleDateString('zh-CN');
            }
        }

        // 全局函数
        async function renewCertificate(domain) {
            if (!confirm(`确定要续期域名 ${domain} 的证书吗？`)) {
                return;
            }

            try {
                const response = await fetch('/api/auto-proxy/certificates', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        domain: domain,
                        force_renewal: true
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    alert('证书续期已提交');
                    sslMonitor.loadCertificates();
                } else {
                    alert('证书续期失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                console.error('续期证书失败:', error);
                alert('证书续期失败: ' + error.message);
            }
        }

        function viewCertDetails(domain) {
            // 跳转到证书详情页面或显示详情模态框
            alert(`查看 ${domain} 证书详情功能开发中`);
        }

        function refreshCertificates() {
            sslMonitor.loadCertificates();
        }

        function exportCertificates() {
            // 导出证书列表功能
            alert('导出证书列表功能开发中');
        }

        // 初始化
        const sslMonitor = new SSLMonitor();
    </script>
    <script src="js/auth-guard.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/navigation.js"></script>
</body>
</html>
