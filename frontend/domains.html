<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>域名池管理 - SM智能代理系统</title>
    <link rel="stylesheet" href="css/common.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📦 域名池管理</h1>
        </div>

        <div class="grid">
            <div class="card">
                <h3>批量添加下游域名</h3>
                <div class="form-group">
                    <label>下游域名列表（每行一个）:</label>
                    <textarea id="downstreamDomains" class="form-control" placeholder="target1.com&#10;target2.com&#10;target3.com" rows="8"></textarea>
                </div>
                <button class="btn btn-primary" onclick="addDownstreamDomains()">添加下游域名</button>
                <button class="btn btn-secondary" onclick="clearDownstreamInput()">清空输入</button>
            </div>

            <div class="card">
                <h3>批量添加上游域名</h3>
                <div class="form-group">
                    <label>上游域名列表（每行一个）:</label>
                    <textarea id="upstreamDomains" class="form-control" placeholder="example1.com&#10;example2.com&#10;example3.com" rows="8"></textarea>
                </div>
                <button class="btn btn-primary" onclick="addUpstreamDomains()">添加上游域名</button>
                <button class="btn btn-secondary" onclick="clearUpstreamInput()">清空输入</button>
            </div>
        </div>

        <div class="card" style="padding: 20px; margin-bottom: 15px;">
            <h3 style="margin-bottom: 15px;">自动镜像设置</h3>
            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label style="margin: 0; display: flex; align-items: center; gap: 8px;">
                        自动镜像开关:
                        <label class="switch">
                            <input type="checkbox" id="autoMirrorSwitch" onchange="toggleAutoMirror()">
                            <span class="slider"></span>
                        </label>
                    </label>
                    <small class="help-text" style="margin: 0;">开启后系统将自动配对上下游域名创建镜像映射</small>
                </div>
            </div>
            <div class="stats" style="margin: 10px 0; gap: 10px;">
                <div class="stat-item" style="min-width: 80px; padding: 5px;">
                    <div class="stat-value" id="upstreamCount" style="font-size: 18px; margin-bottom: 2px;">0</div>
                    <div class="stat-label" style="font-size: 11px;">上游域名</div>
                </div>
                <div class="stat-item" style="min-width: 80px; padding: 5px;">
                    <div class="stat-value" id="downstreamCount" style="font-size: 18px; margin-bottom: 2px;">0</div>
                    <div class="stat-label" style="font-size: 11px;">下游域名</div>
                </div>
                <div class="stat-item" style="min-width: 80px; padding: 5px;">
                    <div class="stat-value" id="mirrorStatus" style="font-size: 18px; margin-bottom: 2px;">关闭</div>
                    <div class="stat-label" style="font-size: 11px;">自动镜像</div>
                </div>
                <div class="stat-item" style="min-width: 80px; padding: 5px;">
                    <div class="stat-value" id="totalMappings" style="font-size: 18px; margin-bottom: 2px;">0</div>
                    <div class="stat-label" style="font-size: 11px;">已创建映射</div>
                </div>
            </div>
        </div>

        <!-- 域名列表 - 并排显示 -->
        <div style="display: flex; gap: 20px;">
            <!-- 下游域名列表 (左侧) -->
            <div class="card" style="flex: 1;">
                <h3>🔽 下游域名列表</h3>
                <div class="table-controls">
                    <input type="text" id="downstreamSearchInput" class="form-control" placeholder="搜索下游域名..." style="width: 250px; display: inline-block;">
                    <button class="btn btn-secondary" onclick="loadDomains()" style="margin-left: 10px;">刷新</button>
                    <button class="btn btn-danger" onclick="clearDownstreamDomains()" style="margin-left: 10px;">清空下游</button>
                </div>

                <div class="table-container">
                    <table class="table" id="downstreamTable">
                        <thead>
                            <tr>
                                <th>域名</th>
                                <th>添加时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="downstreamTableBody">
                            <tr>
                                <td colspan="3" style="text-align: center; color: #666;">正在加载下游域名...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 上游域名列表 (右侧) -->
            <div class="card" style="flex: 1;">
                <h3>🔼 上游域名列表</h3>
                <div class="table-controls">
                    <input type="text" id="upstreamSearchInput" class="form-control" placeholder="搜索上游域名..." style="width: 250px; display: inline-block;">
                    <button class="btn btn-secondary" onclick="loadDomains()" style="margin-left: 10px;">刷新</button>
                    <button class="btn btn-danger" onclick="clearUpstreamDomains()" style="margin-left: 10px;">清空上游</button>
                </div>

                <div class="table-container">
                    <table class="table" id="upstreamTable">
                        <thead>
                            <tr>
                                <th>域名</th>
                                <th>添加时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="upstreamTableBody">
                            <tr>
                                <td colspan="3" style="text-align: center; color: #666;">正在加载上游域名...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>



        <div id="messageArea"></div>
    </div>

    <script src="js/auth-guard.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/domains.js"></script>
    <script src="js/navigation.js"></script>
</body>
</html>
