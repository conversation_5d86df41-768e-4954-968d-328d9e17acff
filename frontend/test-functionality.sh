#!/bin/bash

# 前端功能测试脚本
# 测试实际的功能是否正常工作

echo "🧪 开始前端功能测试..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 错误计数
ERROR_COUNT=0
WARNING_COUNT=0

# 检查函数
test_error() {
    echo -e "${RED}❌ $1${NC}"
    ((ERROR_COUNT++))
}

test_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((WARNING_COUNT++))
}

test_ok() {
    echo -e "${GREEN}✅ $1${NC}"
}

test_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 进入前端目录
cd "$(dirname "$0")"

echo ""
echo "📁 当前目录: $(pwd)"

# 1. 测试后端API是否可访问
echo ""
echo "🌐 测试后端API连接..."

# 检查后端是否运行
if curl -s http://localhost:1319/api/status > /dev/null 2>&1; then
    test_ok "后端API服务正常运行"
else
    test_error "后端API服务无法访问 (http://localhost:1319)"
    echo "   请确保后端服务已启动"
fi

# 2. 测试登录功能
echo ""
echo "🔐 测试登录功能..."

# 尝试登录
login_response=$(curl -s -X POST http://localhost:1319/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"admin123"}' 2>/dev/null)

if echo "$login_response" | grep -q '"success":true'; then
    test_ok "登录API正常工作"
    # 提取token
    token=$(echo "$login_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    if [[ -n "$token" ]]; then
        test_ok "成功获取认证token"
    else
        test_warning "无法提取认证token"
    fi
else
    test_error "登录API失败"
    echo "   响应: $login_response"
fi

# 3. 测试域名API
echo ""
echo "📦 测试域名管理API..."

if [[ -n "$token" ]]; then
    # 测试获取域名列表
    domains_response=$(curl -s -H "Authorization: Bearer $token" \
        http://localhost:1319/api/domains 2>/dev/null)
    
    if echo "$domains_response" | grep -q '"success":true'; then
        test_ok "域名列表API正常工作"
    else
        test_error "域名列表API失败"
        echo "   响应: $domains_response"
    fi
    
    # 测试批量添加域名API
    batch_response=$(curl -s -X POST \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        -d '{"domains":["test.example.com"],"type":"upstream"}' \
        http://localhost:1319/api/domains/batch 2>/dev/null)
    
    if echo "$batch_response" | grep -q '"success":true'; then
        test_ok "批量添加域名API正常工作"
    else
        test_warning "批量添加域名API可能有问题"
        echo "   响应: $batch_response"
    fi
else
    test_warning "跳过域名API测试（无认证token）"
fi

# 4. 检查前端页面是否可访问
echo ""
echo "🌍 测试前端页面访问..."

pages=("index.html" "login.html" "domains.html" "mappings.html" "auto-proxy.html" "recursive.html")

for page in "${pages[@]}"; do
    if curl -s http://localhost:1319/$page > /dev/null 2>&1; then
        test_ok "页面可访问: $page"
    else
        test_error "页面无法访问: $page"
    fi
done

# 5. 检查JavaScript控制台错误
echo ""
echo "🔧 检查JavaScript运行时问题..."

# 创建一个简单的HTML测试页面
cat > test_js_errors.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>JS Error Test</title>
</head>
<body>
    <div id="upstreamDomains"></div>
    <div id="downstreamDomains"></div>
    <div id="messageArea"></div>
    
    <script>
        // 模拟localStorage
        if (typeof localStorage === 'undefined') {
            window.localStorage = {
                getItem: function() { return null; },
                setItem: function() {},
                removeItem: function() {}
            };
        }
        
        // 模拟AuthGuard
        window.AuthGuard = {
            authenticatedFetch: function(url, options) {
                return fetch(url, {
                    ...options,
                    headers: {
                        'Authorization': 'Bearer test-token',
                        'Content-Type': 'application/json',
                        ...(options.headers || {})
                    }
                });
            }
        };
        
        // 测试域名添加函数
        let errorCount = 0;
        
        // 重定义console.error来捕获错误
        const originalError = console.error;
        console.error = function(...args) {
            errorCount++;
            originalError.apply(console, args);
        };
        
        // 重定义window.onerror来捕获错误
        window.onerror = function(msg, url, line, col, error) {
            errorCount++;
            console.error('JavaScript Error:', msg, 'at', url + ':' + line + ':' + col);
            return false;
        };
        
        // 加载domains.js并测试
        const script = document.createElement('script');
        script.src = 'js/domains.js';
        script.onload = function() {
            console.log('domains.js loaded successfully');
            
            // 测试函数是否存在
            if (typeof addUpstreamDomains === 'function') {
                console.log('✅ addUpstreamDomains function exists');
            } else {
                console.error('❌ addUpstreamDomains function missing');
                errorCount++;
            }
            
            if (typeof addDownstreamDomains === 'function') {
                console.log('✅ addDownstreamDomains function exists');
            } else {
                console.error('❌ addDownstreamDomains function missing');
                errorCount++;
            }
            
            // 输出错误计数
            setTimeout(() => {
                if (errorCount === 0) {
                    console.log('✅ No JavaScript errors detected');
                } else {
                    console.error('❌ Found ' + errorCount + ' JavaScript errors');
                }
                
                // 将结果写入页面
                document.body.innerHTML += '<div id="result">JS_ERROR_COUNT:' + errorCount + '</div>';
            }, 1000);
        };
        script.onerror = function() {
            console.error('❌ Failed to load domains.js');
            errorCount++;
            document.body.innerHTML += '<div id="result">JS_ERROR_COUNT:' + errorCount + '</div>';
        };
        document.head.appendChild(script);
    </script>
</body>
</html>
EOF

# 启动一个临时HTTP服务器来测试JavaScript
if command -v python3 &> /dev/null; then
    echo "启动临时测试服务器..."
    python3 -m http.server 8888 > /dev/null 2>&1 &
    SERVER_PID=$!
    sleep 2
    
    # 使用headless浏览器测试（如果可用）
    if command -v node &> /dev/null; then
        # 创建Node.js测试脚本
        cat > test_js.js << 'EOF'
const http = require('http');

// 获取测试页面
const options = {
    hostname: 'localhost',
    port: 8888,
    path: '/test_js_errors.html',
    method: 'GET'
};

const req = http.request(options, (res) => {
    let data = '';
    res.on('data', (chunk) => {
        data += chunk;
    });
    res.on('end', () => {
        if (data.includes('JS_ERROR_COUNT:0')) {
            console.log('✅ JavaScript功能测试通过');
        } else if (data.includes('JS_ERROR_COUNT:')) {
            const match = data.match(/JS_ERROR_COUNT:(\d+)/);
            if (match) {
                console.log('❌ JavaScript功能测试失败，发现 ' + match[1] + ' 个错误');
            }
        } else {
            console.log('⚠️  JavaScript功能测试无法完成');
        }
    });
});

req.on('error', (e) => {
    console.log('⚠️  无法测试JavaScript功能: ' + e.message);
});

req.end();
EOF
        
        sleep 3
        node test_js.js
        rm -f test_js.js
    else
        test_warning "Node.js不可用，跳过JavaScript功能测试"
    fi
    
    # 清理
    kill $SERVER_PID 2>/dev/null
    rm -f test_js_errors.html
else
    test_warning "Python3不可用，跳过JavaScript功能测试"
fi

# 6. 检查网络请求
echo ""
echo "🌐 检查网络请求模式..."

# 检查是否有混合的认证方式
auth_issues=$(grep -r "await fetch(" js/ | grep -v "AuthGuard.authenticatedFetch" | grep -v "auth.js" | grep -v "login.js" | grep -v "navigation.js" | wc -l)

if [[ $auth_issues -gt 0 ]]; then
    test_warning "发现 $auth_issues 个可能的未认证API调用"
    grep -r "await fetch(" js/ | grep -v "AuthGuard.authenticatedFetch" | grep -v "auth.js" | grep -v "login.js" | grep -v "navigation.js" | head -5
else
    test_ok "所有API调用都使用了正确的认证方式"
fi

# 总结
echo ""
echo "📊 功能测试结果总结:"
echo "====================="
if [[ $ERROR_COUNT -eq 0 && $WARNING_COUNT -eq 0 ]]; then
    test_ok "所有功能测试都通过了！"
else
    if [[ $ERROR_COUNT -gt 0 ]]; then
        test_error "发现 $ERROR_COUNT 个严重问题需要修复"
    fi
    if [[ $WARNING_COUNT -gt 0 ]]; then
        test_warning "发现 $WARNING_COUNT 个警告需要注意"
    fi
fi

echo ""
echo "🔧 问题排查建议:"
echo "1. 确保后端服务正在运行 (端口1319)"
echo "2. 检查浏览器控制台是否有JavaScript错误"
echo "3. 验证网络请求是否正确发送"
echo "4. 检查API响应格式是否正确"
echo "5. 确认认证token是否有效"

exit $ERROR_COUNT
