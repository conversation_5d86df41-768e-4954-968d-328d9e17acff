<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 600px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 15px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔐 SM代理系统登录测试工具</h1>
    
    <div class="form-group">
        <label for="username">用户名:</label>
        <input type="text" id="username" value="admin" placeholder="输入用户名">
    </div>
    
    <div class="form-group">
        <label for="password">密码:</label>
        <input type="password" id="password" placeholder="输入密码">
    </div>
    
    <button onclick="testLogin()">测试登录</button>
    <button onclick="checkAdminExists()">检查管理员是否存在</button>
    <button onclick="testCurrentToken()">测试当前Token</button>
    <button onclick="clearToken()">清除Token</button>
    
    <div id="results"></div>

    <script>
        const resultsDiv = document.getElementById('results');

        function addResult(type, title, message, details = '') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `
                <h3>${title}</h3>
                <p>${message}</p>
                ${details ? `<pre>${details}</pre>` : ''}
            `;
            resultsDiv.appendChild(div);
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function checkAdminExists() {
            clearResults();
            try {
                const response = await fetch('/api/auth/check_admin_exists');
                const data = await response.json();
                
                if (response.ok) {
                    addResult('success', '管理员检查', 
                        data.data ? '管理员账户存在' : '管理员账户不存在',
                        JSON.stringify(data, null, 2)
                    );
                } else {
                    addResult('error', '管理员检查失败', `状态码: ${response.status}`, JSON.stringify(data, null, 2));
                }
            } catch (error) {
                addResult('error', '管理员检查失败', error.message);
            }
        }

        async function testLogin() {
            clearResults();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                addResult('error', '登录失败', '请输入用户名和密码');
                return;
            }

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    // 保存token
                    if (data.data && data.data.token) {
                        localStorage.setItem('auth_token', data.data.token);
                        addResult('success', '登录成功', 
                            `Token已保存: ${data.data.token.substring(0, 20)}...`,
                            JSON.stringify(data, null, 2)
                        );
                    } else {
                        addResult('error', '登录失败', '响应中没有token', JSON.stringify(data, null, 2));
                    }
                } else {
                    addResult('error', '登录失败', 
                        data.message || data.error || '未知错误',
                        JSON.stringify(data, null, 2)
                    );
                }
            } catch (error) {
                addResult('error', '登录失败', error.message);
            }
        }

        async function testCurrentToken() {
            clearResults();
            const token = localStorage.getItem('auth_token');
            
            if (!token) {
                addResult('error', 'Token测试', '没有找到Token，请先登录');
                return;
            }

            addResult('info', 'Token信息', `当前Token: ${token.substring(0, 20)}...`);

            try {
                const response = await fetch('/api/domain-pool/domains', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    addResult('success', 'Token验证', 'Token有效，API调用成功', JSON.stringify(data, null, 2));
                } else if (response.status === 401) {
                    addResult('error', 'Token验证', 'Token已过期或无效');
                } else {
                    addResult('error', 'Token验证', `API调用失败，状态码: ${response.status}`, JSON.stringify(data, null, 2));
                }
            } catch (error) {
                addResult('error', 'Token验证', error.message);
            }
        }

        function clearToken() {
            localStorage.removeItem('auth_token');
            addResult('info', 'Token清除', 'Token已从localStorage中清除');
        }

        // 页面加载时显示当前状态
        window.addEventListener('DOMContentLoaded', () => {
            const token = localStorage.getItem('auth_token');
            if (token) {
                addResult('info', '当前状态', `已有Token: ${token.substring(0, 20)}...`);
            } else {
                addResult('info', '当前状态', '未登录状态');
            }
        });
    </script>
</body>
</html>
