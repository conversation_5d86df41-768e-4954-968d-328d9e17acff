<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试 - SM智能代理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .test-success {
            background: #1b5e20;
            color: #4caf50;
            border: 1px solid #4caf50;
        }
        
        .test-error {
            background: #b71c1c;
            color: #f44336;
            border: 1px solid #f44336;
        }
        
        .test-info {
            background: #0d47a1;
            color: #2196f3;
            border: 1px solid #2196f3;
        }
        
        .test-button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .test-button:disabled {
            background: #666;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🧪 前端功能测试</h1>
        </div>

        <div class="test-section">
            <h3>🔐 认证功能测试</h3>
            <button class="test-button" onclick="testLogin()">测试登录</button>
            <button class="test-button" onclick="testLogout()">测试登出</button>
            <div id="authResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 API连接测试</h3>
            <button class="test-button" onclick="testStatusAPI()">测试状态API</button>
            <button class="test-button" onclick="testDomainsAPI()">测试域名API</button>
            <button class="test-button" onclick="testConfigAPI()">测试配置API</button>
            <button class="test-button" onclick="testStatsAPI()">测试统计API</button>
            <div id="apiResults"></div>
        </div>

        <div class="test-section">
            <h3>🎨 前端组件测试</h3>
            <button class="test-button" onclick="testNavigation()">测试导航栏</button>
            <button class="test-button" onclick="testNotifications()">测试通知系统</button>
            <button class="test-button" onclick="testLocalStorage()">测试本地存储</button>
            <div id="componentResults"></div>
        </div>

        <div class="test-section">
            <h3>📱 响应式设计测试</h3>
            <button class="test-button" onclick="testResponsive()">测试响应式布局</button>
            <div id="responsiveResults"></div>
        </div>

        <div class="test-section">
            <h3>🔄 全面测试</h3>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <div id="allTestResults"></div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/navigation.js"></script>
    <script>
        let authToken = null;

        // 显示测试结果
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result test-${type}`;
            result.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(result);
            container.scrollTop = container.scrollHeight;
        }

        // 清空结果
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 测试登录功能
        async function testLogin() {
            clearResults('authResults');
            showResult('authResults', '开始测试登录功能...', 'info');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin888'
                    })
                });

                const result = await response.json();
                
                if (result.success && result.data && result.data.token) {
                    authToken = result.data.token;
                    localStorage.setItem('auth_token', authToken);
                    showResult('authResults', '✅ 登录成功！Token已保存', 'success');
                    showResult('authResults', `Token: ${authToken.substring(0, 50)}...`, 'info');
                } else {
                    showResult('authResults', `❌ 登录失败: ${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('authResults', `❌ 登录请求失败: ${error.message}`, 'error');
            }
        }

        // 测试登出功能
        async function testLogout() {
            showResult('authResults', '开始测试登出功能...', 'info');
            
            if (!authToken) {
                showResult('authResults', '⚠️ 请先登录', 'error');
                return;
            }

            try {
                const response = await fetch('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    authToken = null;
                    localStorage.removeItem('auth_token');
                    showResult('authResults', '✅ 登出成功！Token已清除', 'success');
                } else {
                    showResult('authResults', `❌ 登出失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('authResults', `❌ 登出请求失败: ${error.message}`, 'error');
            }
        }

        // 测试状态API
        async function testStatusAPI() {
            clearResults('apiResults');
            showResult('apiResults', '测试状态API...', 'info');
            await testAPI('/api/status', 'apiResults', '状态API');
        }

        // 测试域名API
        async function testDomainsAPI() {
            showResult('apiResults', '测试域名API...', 'info');
            await testAPI('/api/domains', 'apiResults', '域名API');
        }

        // 测试配置API
        async function testConfigAPI() {
            showResult('apiResults', '测试配置API...', 'info');
            await testAPI('/api/config', 'apiResults', '配置API');
        }

        // 测试统计API
        async function testStatsAPI() {
            showResult('apiResults', '测试统计API...', 'info');
            await testAPI('/api/auto-proxy/statistics', 'apiResults', '统计API');
        }

        // 通用API测试函数
        async function testAPI(endpoint, containerId, name) {
            if (!authToken) {
                authToken = localStorage.getItem('auth_token');
                if (!authToken) {
                    showResult(containerId, `⚠️ ${name}: 需要先登录`, 'error');
                    return;
                }
            }

            try {
                const response = await fetch(endpoint, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult(containerId, `✅ ${name}: 成功`, 'success');
                    showResult(containerId, `数据: ${JSON.stringify(result.data).substring(0, 100)}...`, 'info');
                } else {
                    showResult(containerId, `❌ ${name}: ${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(containerId, `❌ ${name}: ${error.message}`, 'error');
            }
        }

        // 测试导航栏
        function testNavigation() {
            clearResults('componentResults');
            showResult('componentResults', '测试导航栏功能...', 'info');
            
            const nav = document.querySelector('.main-navigation');
            if (nav) {
                showResult('componentResults', '✅ 导航栏已加载', 'success');
                
                const navItems = nav.querySelectorAll('.nav-item');
                showResult('componentResults', `✅ 找到 ${navItems.length} 个导航项`, 'success');
                
                const activeItem = nav.querySelector('.nav-item.active');
                if (activeItem) {
                    showResult('componentResults', `✅ 当前页面高亮: ${activeItem.textContent.trim()}`, 'success');
                } else {
                    showResult('componentResults', '⚠️ 未找到当前页面高亮', 'error');
                }
            } else {
                showResult('componentResults', '❌ 导航栏未找到', 'error');
            }
        }

        // 测试通知系统
        function testNotifications() {
            showResult('componentResults', '测试通知系统...', 'info');
            
            if (window.SM && window.SM.showNotification) {
                window.SM.showNotification('这是一个测试通知', 'success', 2000);
                showResult('componentResults', '✅ 通知系统正常工作', 'success');
            } else {
                showResult('componentResults', '❌ 通知系统未找到', 'error');
            }
        }

        // 测试本地存储
        function testLocalStorage() {
            showResult('componentResults', '测试本地存储...', 'info');
            
            try {
                const testKey = 'sm_test_key';
                const testValue = 'test_value_' + Date.now();
                
                localStorage.setItem(testKey, testValue);
                const retrieved = localStorage.getItem(testKey);
                
                if (retrieved === testValue) {
                    showResult('componentResults', '✅ 本地存储读写正常', 'success');
                    localStorage.removeItem(testKey);
                } else {
                    showResult('componentResults', '❌ 本地存储读写失败', 'error');
                }
            } catch (error) {
                showResult('componentResults', `❌ 本地存储错误: ${error.message}`, 'error');
            }
        }

        // 测试响应式设计
        function testResponsive() {
            clearResults('responsiveResults');
            showResult('responsiveResults', '测试响应式设计...', 'info');
            
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            showResult('responsiveResults', `当前窗口尺寸: ${width}x${height}`, 'info');
            
            if (width <= 768) {
                showResult('responsiveResults', '✅ 移动端布局', 'success');
            } else if (width <= 1024) {
                showResult('responsiveResults', '✅ 平板端布局', 'success');
            } else {
                showResult('responsiveResults', '✅ 桌面端布局', 'success');
            }
            
            // 测试CSS媒体查询
            const testElement = document.createElement('div');
            testElement.style.display = 'none';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            showResult('responsiveResults', '✅ CSS样式计算正常', 'success');
            
            document.body.removeChild(testElement);
        }

        // 运行所有测试
        async function runAllTests() {
            clearResults('allTestResults');
            showResult('allTestResults', '🚀 开始运行所有测试...', 'info');
            
            // 认证测试
            await testLogin();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // API测试
            await testStatusAPI();
            await testDomainsAPI();
            await testConfigAPI();
            await testStatsAPI();
            
            // 组件测试
            testNavigation();
            testNotifications();
            testLocalStorage();
            testResponsive();
            
            showResult('allTestResults', '✅ 所有测试完成！请查看各个测试区域的详细结果', 'success');
        }

        // 页面加载时自动获取token
        window.addEventListener('load', () => {
            authToken = localStorage.getItem('auth_token');
            if (authToken) {
                showResult('authResults', '✅ 从本地存储恢复了认证Token', 'success');
            }
        });
    </script>
</body>
</html>
