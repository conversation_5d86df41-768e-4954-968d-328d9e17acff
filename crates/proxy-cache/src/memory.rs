use crate::Cache;
use crate::Result;
use async_trait::async_trait;
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};

/// 通过 `proxy_cache::CacheKey` 直接导入。
#[derive(<PERSON>bu<PERSON>, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct CacheKey {
    /// 主键（如缓存条目的唯一标识）
    pub key: String,
    /// 命名空间（可选，用于逻辑分组或多租户隔离）
    pub namespace: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct CacheEntry {
    pub value: Vec<u8>,
    pub expires_at: Option<SystemTime>,
}

pub struct MemoryCache {
    store: Arc<RwLock<HashMap<String, CacheEntry>>>,
    #[allow(dead_code)] // 预留字段，用于将来的缓存大小限制功能
    max_size: usize,
}

impl MemoryCache {
    pub fn new(max_size: usize) -> Self {
        Self {
            store: Arc::new(RwLock::new(HashMap::new())),
            max_size,
        }
    }

    fn build_key(&self, key: &CacheKey) -> String {
        match &key.namespace {
            Some(ns) => format!("{}:{}", ns, key.key),
            None => key.key.clone(),
        }
    }

    #[allow(dead_code)] // 预留方法，用于将来的过期清理功能
    async fn cleanup_expired(&self) {
        let mut store = self.store.write();
        let now = SystemTime::now();

        store.retain(|_, entry| match entry.expires_at {
            Some(expires) => expires > now,
            None => true,
        });
    }
}

// 只保留 trait 实现，移除本地 get_entry/set_entry/delete_entry/exists_entry/clear_entries 等冗余方法
#[async_trait]
impl Cache for MemoryCache {
    async fn get(&self, key: &CacheKey) -> Result<Option<Vec<u8>>> {
        let store = self.store.read();
        let cache_key = self.build_key(key);
        if let Some(entry) = store.get(&cache_key) {
            if let Some(expires_at) = entry.expires_at {
                if expires_at < SystemTime::now() {
                    return Ok(None);
                }
            }
            Ok(Some(entry.value.clone()))
        } else {
            Ok(None)
        }
    }
    async fn set(&self, key: &CacheKey, value: Vec<u8>, ttl: Duration) -> Result<()> {
        let expires_at = if ttl.as_secs() > 0 {
            Some(SystemTime::now() + ttl)
        } else {
            None
        };
        let entry = CacheEntry { value, expires_at };
        let cache_key = self.build_key(key);
        let mut store = self.store.write();
        store.insert(cache_key, entry);
        Ok(())
    }
    async fn delete(&self, key: &CacheKey) -> Result<()> {
        let cache_key = self.build_key(key);
        let mut store = self.store.write();
        store.remove(&cache_key);
        Ok(())
    }
    async fn exists(&self, key: &CacheKey) -> Result<bool> {
        let cache_key = self.build_key(key);
        let store = self.store.read();
        Ok(store.contains_key(&cache_key))
    }
    async fn clear(&self) -> Result<()> {
        let mut store = self.store.write();
        store.clear();
        Ok(())
    }
}
