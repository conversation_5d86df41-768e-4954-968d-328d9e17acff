/*!
# 监控模块

整合了原来 proxy-monitor crate 的所有功能：
- Prometheus 指标
- 健康检查
- 链路追踪
- 性能监控
*/

use crate::{ProxyError, Result};
use parking_lot::RwLock;
use prometheus::{Counter, Encoder, Gauge, Histogram, Registry, TextEncoder};
use std::sync::Arc;
use std::time::{Duration, Instant};

/// 监控服务
pub struct MonitoringService {
    registry: Registry,
    metrics: Arc<RwLock<SystemMetrics>>,
    start_time: Instant,
}

/// 系统指标
pub struct SystemMetrics {
    // HTTP请求指标
    pub http_requests_total: Counter,
    pub http_request_duration: Histogram,
    pub http_errors_total: Counter,

    // 系统指标
    pub active_connections: Gauge,
    pub memory_usage: Gauge,
    pub cpu_usage: Gauge,

    // 缓存指标
    pub cache_hits_total: Counter,
    pub cache_misses_total: Counter,
}

impl MonitoringService {
    pub fn new() -> Result<Self> {
        let registry = Registry::new();
        let metrics = Arc::new(RwLock::new(SystemMetrics::new(&registry)?));

        Ok(Self {
            registry,
            metrics,
            start_time: Instant::now(),
        })
    }

    /// 启动监控服务
    pub async fn start(&self) -> Result<()> {
        tracing::info!("监控服务已启动");

        // 启动系统指标收集
        let metrics = self.metrics.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(5));
            loop {
                interval.tick().await;
                if let Err(e) = Self::collect_system_metrics(&metrics).await {
                    tracing::warn!("收集系统指标失败: {}", e);
                }
            }
        });

        Ok(())
    }

    /// 记录HTTP请求
    pub async fn record_http_request(&self, method: &str, status: u16, duration: Duration) {
        let metrics = self.metrics.read().await;

        metrics
            .http_requests_total
            .with_label_values(&[method, &status.to_string()])
            .inc();

        metrics
            .http_request_duration
            .observe(duration.as_secs_f64());

        if status >= 400 {
            metrics
                .http_errors_total
                .with_label_values(&[method, &status.to_string()])
                .inc();
        }
    }

    /// 记录缓存操作
    pub async fn record_cache_operation(&self, hit: bool) {
        let metrics = self.metrics.read().await;

        if hit {
            metrics.cache_hits_total.inc();
        } else {
            metrics.cache_misses_total.inc();
        }
    }

    /// 更新活跃连接数
    pub async fn update_active_connections(&self, count: f64) {
        let metrics = self.metrics.read().await;
        metrics.active_connections.set(count);
    }

    /// 获取Prometheus指标
    pub fn get_metrics(&self) -> Result<String> {
        let encoder = TextEncoder::new();
        let metric_families = self.registry.gather();

        encoder
            .encode_to_string(&metric_families)
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))
    }

    /// 获取系统运行时间
    pub fn uptime(&self) -> Duration {
        self.start_time.elapsed()
    }

    /// 收集系统指标
    async fn collect_system_metrics(metrics: &Arc<RwLock<SystemMetrics>>) -> Result<()> {
        // 这里可以集成 sysinfo 等库收集实际的系统指标
        // 为了简化，这里只是示例
        let metrics_guard = metrics.read().await;

        // 模拟内存使用率
        metrics_guard.memory_usage.set(0.75);

        // 模拟CPU使用率
        metrics_guard.cpu_usage.set(0.45);

        Ok(())
    }
}

impl SystemMetrics {
    fn new(registry: &Registry) -> Result<Self> {
        let http_requests_total =
            Counter::new("http_requests_total", "Total number of HTTP requests")
                .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;

        let http_request_duration = Histogram::new(
            "http_request_duration_seconds",
            "HTTP request duration in seconds",
        )
        .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;

        let http_errors_total = Counter::new("http_errors_total", "Total number of HTTP errors")
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;

        let active_connections = Gauge::new("active_connections", "Number of active connections")
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;

        let memory_usage = Gauge::new("memory_usage_ratio", "Memory usage ratio")
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;

        let cpu_usage = Gauge::new("cpu_usage_ratio", "CPU usage ratio")
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;

        let cache_hits_total = Counter::new("cache_hits_total", "Total number of cache hits")
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;

        let cache_misses_total = Counter::new("cache_misses_total", "Total number of cache misses")
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;

        // 注册所有指标
        registry
            .register(Box::new(http_requests_total.clone()))
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;
        registry
            .register(Box::new(http_request_duration.clone()))
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;
        registry
            .register(Box::new(http_errors_total.clone()))
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;
        registry
            .register(Box::new(active_connections.clone()))
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;
        registry
            .register(Box::new(memory_usage.clone()))
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;
        registry
            .register(Box::new(cpu_usage.clone()))
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;
        registry
            .register(Box::new(cache_hits_total.clone()))
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;
        registry
            .register(Box::new(cache_misses_total.clone()))
            .map_err(|e| ProxyError::Monitoring(Box::new(e)))?;

        Ok(Self {
            http_requests_total,
            http_request_duration,
            http_errors_total,
            active_connections,
            memory_usage,
            cpu_usage,
            cache_hits_total,
            cache_misses_total,
        })
    }
}

/// 健康检查服务
pub struct HealthChecker {
    checks: Vec<Box<dyn HealthCheck>>,
}

#[async_trait::async_trait]
pub trait HealthCheck: Send + Sync {
    async fn check(&self) -> HealthStatus;
    fn name(&self) -> &str;
}

#[derive(Debug, Clone)]
pub enum HealthStatus {
    Healthy,
    Unhealthy(String),
    Degraded(String),
}

impl HealthChecker {
    pub fn new() -> Self {
        Self { checks: Vec::new() }
    }

    pub fn add_check(&mut self, check: Box<dyn HealthCheck>) {
        self.checks.push(check);
    }

    /// 执行所有健康检查
    pub async fn check_all(&self) -> Vec<(String, HealthStatus)> {
        let mut results = Vec::new();

        for check in &self.checks {
            let status = check.check().await;
            results.push((check.name().to_string(), status));
        }

        results
    }

    /// 获取整体健康状态
    pub async fn overall_health(&self) -> HealthStatus {
        let results = self.check_all().await;

        let mut has_degraded = false;

        for (_, status) in results {
            match status {
                HealthStatus::Unhealthy(msg) => return HealthStatus::Unhealthy(msg),
                HealthStatus::Degraded(_) => has_degraded = true,
                HealthStatus::Healthy => {}
            }
        }

        if has_degraded {
            HealthStatus::Degraded("Some services are degraded".to_string())
        } else {
            HealthStatus::Healthy
        }
    }
}

impl Default for HealthChecker {
    fn default() -> Self {
        Self::new()
    }
}
