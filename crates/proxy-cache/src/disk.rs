use crate::memory::<PERSON><PERSON><PERSON><PERSON>;
use crate::<PERSON><PERSON>;
use async_trait::async_trait;
use proxy_core::ProxyError;
use std::path::PathBuf;
use std::time::Duration;

#[derive(Debug, Clone)]
pub struct CacheEntry {
    pub value: Vec<u8>,
    pub expires_at: Option<std::time::SystemTime>,
}

pub type Result<T> = std::result::Result<T, ProxyError>;

pub struct DiskCache {
    _path: PathBuf,
    _max_size: Option<u64>,
}

impl DiskCache {
    pub fn new(path: String, max_size: Option<u64>) -> Self {
        Self {
            _path: PathBuf::from(path),
            _max_size: max_size,
        }
    }

    // 直接实现方法，不再实现不存在的 CacheProvider trait
    pub async fn get_entry(&self, _key: &CacheKey) -> Result<Option<CacheEntry>> {
        // TODO: 实现磁盘缓存读取
        Ok(None)
    }
    pub async fn set_entry(
        &self,
        _key: &CacheKey,
        _entry: CacheEntry,
        _ttl: Option<Duration>,
    ) -> Result<()> {
        // TODO: 实现磁盘缓存写入
        Ok(())
    }
    pub async fn delete_entry(&self, _key: &CacheKey) -> Result<()> {
        // TODO: 实现磁盘缓存删除
        Ok(())
    }
    pub async fn exists_entry(&self, _key: &CacheKey) -> Result<bool> {
        // TODO: 实现磁盘缓存存在检查
        Ok(false)
    }
    pub async fn clear_entries(&self) -> Result<()> {
        // TODO: 实现磁盘缓存清空
        Ok(())
    }
}

#[async_trait]
impl Cache for DiskCache {
    async fn get(&self, key: &CacheKey) -> Result<Option<Vec<u8>>> {
        match self.get_entry(key).await? {
            Some(entry) => Ok(Some(entry.value)),
            None => Ok(None),
        }
    }
    async fn set(&self, key: &CacheKey, value: Vec<u8>, ttl: Duration) -> Result<()> {
        let entry = CacheEntry {
            value,
            expires_at: if ttl.as_secs() > 0 {
                Some(std::time::SystemTime::now() + ttl)
            } else {
                None
            },
        };
        self.set_entry(key, entry, Some(ttl)).await
    }
    async fn delete(&self, key: &CacheKey) -> Result<()> {
        self.delete_entry(key).await
    }
    async fn exists(&self, key: &CacheKey) -> Result<bool> {
        self.exists_entry(key).await
    }
    async fn clear(&self) -> Result<()> {
        self.clear_entries().await
    }
}

// 可选：将 get_entry/set_entry/delete_entry/exists_entry/clear_entries 设为私有辅助方法，或保留为 pub(crate) 以便测试。
