//! 基于 Pingora 的代理核心服务
//!
//! 这个模块提供了核心代理功能，直接基于 Pingora 官方实现

pub mod error;
pub mod traits;
pub mod types;

// 重新导出统一的错误类型，供其他crate使用
pub use error::{ProxyError, Result};

use async_trait::async_trait;
use pingora::prelude::*;
use pingora_core::upstreams::peer::HttpPeer;
use pingora_core::upstreams::peer::Peer;
use pingora_http::{RequestHeader, ResponseHeader};
// 移除负载均衡相关导入
use pingora_proxy::{ProxyHttp, Session};
use proxy_config::unified::{ProxyConfig, UnifiedConfig};
use std::net::SocketAddr;
// 移除未使用的 Arc 导入
use tracing::{debug, error, info, instrument};

/// 基于 Pingora 的代理服务
pub struct ProxyService {
    #[allow(dead_code)]
    config: ProxyConfig,
    #[allow(dead_code)]
    backend_addr: Option<(String, u16)>,
}

impl ProxyService {
    pub fn new(config: ProxyConfig) -> crate::error::Result<Self> {
        // 允许空的上游配置，支持后续通过多种方式添加上游服务器
        let backend_addr = if let Some(upstream) = config.upstreams.first() {
            if let Some(server) = upstream.servers.first() {
                // 尝试解析地址，如果失败则记录警告但不阻止启动
                match server.addr.parse::<SocketAddr>() {
                    Ok(addr) => {
                        tracing::info!("配置了上游服务器: {}", server.addr);
                        Some((addr.ip().to_string(), addr.port()))
                    }
                    Err(_) => {
                        // 记录警告但允许继续启动，支持后续动态配置
                        tracing::warn!(
                            "上游地址格式无效，将被忽略: {}。可通过API或其他方式后续添加",
                            server.addr
                        );
                        None
                    }
                }
            } else {
                tracing::info!("上游配置为空，可通过多种方式后续添加");
                None
            }
        } else {
            // 没有上游配置是完全允许的，支持多种添加方式
            tracing::info!("未配置上游服务器，支持后续通过以下方式添加：");
            tracing::info!("  1. 递归代理自动发现");
            tracing::info!("  2. Web管理界面手工添加");
            tracing::info!("  3. API接口动态配置");
            tracing::info!("  4. 配置文件更新后重启");
            None
        };

        Ok(Self {
            config,
            backend_addr,
        })
    }

    /// 获取后端服务器地址
    pub fn get_backend(&self) -> Option<(String, u16)> {
        self.backend_addr.clone()
    }
}

/// Pingora ProxyHttp trait 实现
pub struct ReverseProxy {
    proxy_service: ProxyService,
}

impl ReverseProxy {
    pub fn new(proxy_service: ProxyService) -> Self {
        Self { proxy_service }
    }
}

#[async_trait]
impl ProxyHttp for ReverseProxy {
    type CTX = ();
    fn new_ctx(&self) -> Self::CTX {}

    #[instrument(skip(self, _session))]
    async fn upstream_peer(
        &self,
        _session: &mut Session,
        _ctx: &mut Self::CTX,
    ) -> std::result::Result<Box<HttpPeer>, Box<pingora::Error>> {
        match self.proxy_service.get_backend() {
            Some(backend) => {
                let (ip, port) = backend;
                let addr = format!("{}:{}", ip, port);
                let peer = HttpPeer::new(&addr, false, String::new());
                info!("Using backend: {}", peer.address());
                Ok(Box::new(peer))
            }
            None => {
                error!("No backend available");
                Err(pingora_core::Error::new_str("No backend available"))
            }
        }
    }

    #[instrument(skip(self, session))]
    async fn upstream_request_filter(
        &self,
        session: &mut Session,
        upstream_request: &mut RequestHeader,
        _ctx: &mut Self::CTX,
    ) -> std::result::Result<(), Box<pingora::Error>> {
        // 移除hop-by-hop头部
        upstream_request.remove_header("connection");
        upstream_request.remove_header("proxy-connection");
        upstream_request.remove_header("upgrade");
        upstream_request.remove_header("proxy-authorization");
        upstream_request.remove_header("proxy-authenticate");
        upstream_request.remove_header("te");
        upstream_request.remove_header("trailer");
        upstream_request.remove_header("transfer-encoding"); // 设置 X-Forwarded-For
        if let Some(client_ip) = session.client_addr() {
            upstream_request.insert_header("X-Forwarded-For", client_ip.to_string())?;
        }

        // 设置 X-Forwarded-Proto
        let scheme = if session
            .req_header()
            .uri
            .scheme()
            .map(|s| s == "https")
            .unwrap_or(false)
        {
            "https"
        } else {
            "http"
        };
        upstream_request.insert_header("X-Forwarded-Proto", scheme)?;

        debug!("Modified upstream request headers");
        Ok(())
    }

    #[instrument(skip(self, _session, upstream_response))]
    async fn response_filter(
        &self,
        _session: &mut Session,
        upstream_response: &mut ResponseHeader,
        _ctx: &mut Self::CTX,
    ) -> std::result::Result<(), Box<pingora::Error>> {
        for (k, v) in [
            ("X-Frame-Options", "DENY"),
            ("X-Content-Type-Options", "nosniff"),
            ("X-XSS-Protection", "1; mode=block"),
            ("X-Proxy-By", "Pingora-SM"),
        ] {
            upstream_response.insert_header(k, v)?;
        }

        debug!("Added response headers");
        Ok(())
    }

    #[instrument(skip(self, session))]
    async fn logging(
        &self,
        session: &mut Session,
        _e: Option<&pingora_core::Error>,
        _ctx: &mut Self::CTX,
    ) {
        let response_code = session
            .response_written()
            .map_or(0, |resp| resp.status.as_u16());

        info!(
            method = %session.req_header().method,
            uri = %session.req_header().uri,
            status = response_code,
            "Request processed"
        );
    }
}

/// 代理工厂 - 用于创建代理服务实例
pub struct ProxyFactory {
    config: ProxyConfig,
}

impl ProxyFactory {
    pub fn new(config: ProxyConfig) -> Self {
        Self { config }
    }

    pub fn create_proxy(&self) -> crate::error::Result<ReverseProxy> {
        let proxy_service = ProxyService::new(self.config.clone())?;
        Ok(ReverseProxy::new(proxy_service))
    }
}

/// 代理服务器构建器
pub struct ProxyServerBuilder {
    config: ProxyConfig,
    listen_addr: String,
}

impl ProxyServerBuilder {
    pub fn new(unified: &UnifiedConfig) -> Self {
        let config = unified.clone();
        Self {
            config,
            listen_addr: "0.0.0.0:8080".to_string(),
        }
    }

    pub fn with_listen_addr(mut self, addr: String) -> Self {
        self.listen_addr = addr;
        self
    }

    pub fn build(self) -> crate::error::Result<Server> {
        let factory = ProxyFactory::new(self.config);
        let proxy = factory.create_proxy()?;

        let mut server = Server::new(Some(Opt::default()))
            .map_err(|e| crate::error::ProxyError::internal(e.to_string()))?;
        server.bootstrap();

        let mut service = pingora_proxy::http_proxy_service(&server.configuration, proxy);
        service.add_tcp(&self.listen_addr);

        server.add_service(service);

        Ok(server)
    }
}

impl Default for ProxyServerBuilder {
    fn default() -> Self {
        let unified = UnifiedConfig::default();
        Self::new(&unified)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use proxy_config::unified::{
        ProxyConfig, ServerConfig, ServerEntry, UnifiedConfig, UpstreamConfig,
    };
    use std::sync::Arc;
    use tokio;

    fn create_test_config() -> ProxyConfig {
        ProxyConfig {
            server: ServerConfig {
                listen: "127.0.0.1:8080".to_string(),
                workers: Some(1),
                frontend_addr: Some("127.0.0.1:8080".to_string()),
                backend_addr: Some("127.0.0.1:8081".to_string()),
            },
            upstreams: vec![UpstreamConfig {
                name: "test-upstream".to_string(),
                servers: vec![
                    ServerEntry {
                        addr: "127.0.0.1:8081".to_string(),
                    },
                    ServerEntry {
                        addr: "127.0.0.1:8082".to_string(),
                    },
                ],
            }],
            routes: vec![],
            cache: None,
            security: None,
        }
    }

    #[test]
    fn test_proxy_service_creation() {
        let config = create_test_config();
        let result = ProxyService::new(config);

        assert!(result.is_ok());
        let proxy_service = result.unwrap();
        // 验证后端服务器已正确配置
        let backend = proxy_service.get_backend();
        assert!(backend.is_some());
    }

    #[test]
    fn test_proxy_service_with_empty_upstreams() {
        let mut config = create_test_config();
        config.upstreams.clear();

        let result = ProxyService::new(config);
        // 没有上游服务器时应该成功创建，但 get_backend() 返回 None
        assert!(result.is_ok());
        let proxy_service = result.unwrap();
        assert!(proxy_service.get_backend().is_none());
    }

    #[tokio::test]
    async fn test_backend_selection() {
        let config = create_test_config();
        let proxy_service = ProxyService::new(config).unwrap();
        // 测试后端服务器选择（现在只返回第一个服务器）
        let backend = proxy_service.get_backend();
        assert!(backend.is_some());
        // 验证选择的是配置中的第一个服务器
        let (ip, port) = backend.unwrap();
        let addr = format!("{}:{}", ip, port);
        assert_eq!(addr, "127.0.0.1:8081"); // 应该是第一个服务器
    }

    #[test]
    fn test_proxy_factory() {
        let config = create_test_config();
        let factory = ProxyFactory::new(config);

        let result = factory.create_proxy();
        assert!(result.is_ok());
    }

    #[test]
    fn test_reverse_proxy_creation() {
        let config = create_test_config();
        let proxy_service = ProxyService::new(config).unwrap();
        let reverse_proxy = ReverseProxy::new(proxy_service);

        // 测试上下文创建
        let _ctx = reverse_proxy.new_ctx();
    }

    #[test]
    fn test_proxy_server_builder() {
        let unified = UnifiedConfig::default();
        let builder = ProxyServerBuilder::new(&unified);

        // 测试构建器配置
        let _builder_with_addr = builder.with_listen_addr("127.0.0.1:9090".to_string());

        // 注意：这里不实际构建服务器，因为需要网络资源
        // 在实际测试中，可以使用mock或测试专用配置
    }

    #[test]
    fn test_proxy_server_builder_default() {
        let builder = ProxyServerBuilder::default();

        // 验证默认配置可以正常创建
        assert_eq!(builder.listen_addr, "0.0.0.0:8080");
    }

    // 测试错误处理
    #[test]
    fn test_proxy_error_classification() {
        // 客户端错误
        assert!(ProxyError::Authentication.is_client_error());
        assert!(ProxyError::Authorization.is_client_error());
        assert!(ProxyError::InvalidInput.is_client_error());
        assert!(ProxyError::RateLimited.is_client_error());
        // 服务器错误
        assert!(ProxyError::Internal.is_server_error());
        assert!(ProxyError::config("test").is_server_error());
        assert!(ProxyError::Network(Arc::new(std::io::Error::new(
            std::io::ErrorKind::ConnectionRefused,
            "test"
        )))
        .is_server_error());
    }

    // 测试错误链
    #[test]
    fn test_error_chain() {
        // 测试错误链
        let io_error = std::io::Error::new(std::io::ErrorKind::ConnectionRefused, "连接被拒绝");
        let proxy_error = ProxyError::Network(Arc::new(io_error));
        assert!(proxy_error.is_server_error());
        assert_eq!(proxy_error.error_code(), "NETWORK_ERROR");
    }

    // 性能测试
    #[tokio::test]
    async fn test_backend_selection_performance() {
        let config = create_test_config();
        let proxy_service = ProxyService::new(config).unwrap();

        let start = std::time::Instant::now();
        for _i in 0..1000 {
            let _backend = proxy_service.get_backend();
        }
        let duration = start.elapsed();

        // 1000次选择应该在10ms内完成（现在更快了）
        assert!(duration.as_millis() < 10);
    }

    // 并发测试
    #[tokio::test]
    async fn test_concurrent_backend_selection() {
        let config = create_test_config();
        let proxy_service = Arc::new(ProxyService::new(config).unwrap());

        let mut handles = vec![];
        for i in 0..10 {
            let service = Arc::clone(&proxy_service);
            let handle = tokio::spawn(async move {
                for _j in 0..100 {
                    let _backend = service.get_backend();
                }
            });
            handles.push(handle);
        }

        for handle in handles {
            assert!(handle.await.is_ok());
        }
    }

    // 后端服务器测试
    #[tokio::test]
    async fn test_backend_consistency() {
        let config = create_test_config();
        let proxy_service = ProxyService::new(config).unwrap();

        // 验证后端服务器选择的一致性
        let backend1 = proxy_service.get_backend();
        let backend2 = proxy_service.get_backend();

        assert_eq!(backend1, backend2); // 应该总是返回相同的后端

        if let Some((ip, port)) = backend1 {
            let addr = format!("{}:{}", ip, port);
            println!("Backend address: {}", addr);
            assert_eq!(addr, "127.0.0.1:8081"); // 应该是第一个服务器
        }
    }

    // 配置测试
    #[test]
    fn test_security_configuration() {
        let mut config = create_test_config();
        config.security = Some(Default::default());

        let proxy_service = ProxyService::new(config).unwrap();
        let backend = proxy_service.get_backend();

        // 验证后端配置正确
        assert!(backend.is_some());
    }

    #[test]
    fn test_no_security_configuration() {
        let mut config = create_test_config();
        config.security = None;

        let proxy_service = ProxyService::new(config).unwrap();
        let backend = proxy_service.get_backend();

        // 验证后端配置正确
        assert!(backend.is_some());
    }

    // 后端配置测试
    #[tokio::test]
    async fn test_backend_configuration() {
        let mut config = create_test_config();
        config.security = Some(Default::default());

        let proxy_service = ProxyService::new(config).unwrap();
        if let Some((_ip, _port)) = proxy_service.get_backend() {
            assert!(true);
        }
    }

    // 边界测试：极限配置
    #[test]
    fn test_extreme_configurations() {
        // 最小超时
        let mut config = create_test_config();
        config.security = Some(Default::default());
        let result = ProxyService::new(config);
        assert!(result.is_ok());

        // 大量上游服务器
        let mut config = create_test_config();
        config.upstreams.clear();
        for i in 0..1000 {
            config.upstreams.push(UpstreamConfig {
                name: format!("upstream-{}", i),
                servers: vec![ServerEntry {
                    addr: format!("127.0.0.1:{}", 8000 + i),
                }],
            });
        }
        let result = ProxyService::new(config);
        assert!(result.is_ok());
    }

    // 内存使用测试
    #[test]
    fn test_memory_usage() {
        let config = create_test_config();

        // 创建多个代理服务实例
        let mut services = vec![];
        for _ in 0..100 {
            if let Ok(service) = ProxyService::new(config.clone()) {
                services.push(service);
            }
        }

        // 验证所有服务都创建成功
        assert_eq!(services.len(), 100);

        // 清理以释放内存
        drop(services);
    }
}
