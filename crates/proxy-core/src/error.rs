//! 统一的代理错误处理模块
use serde_json;
use serde_yaml;
use std::borrow::Cow;
use std::fmt;
use std::sync::Arc;
use thiserror::Error;
use toml;

// 导入基础特征
use proxy_types::ProxyErrorTrait;

/// 统一的代理错误类型 - 内存优化版本
#[derive(Error, Debug, Clone)]
pub enum ProxyError {
    /// 配置相关错误 - 使用Cow减少字符串分配
    #[error("配置错误: {message}")]
    Config {
        message: Cow<'static, str>,
        #[source]
        source: Option<Arc<dyn std::error::Error + Send + Sync>>,
    },

    /// 认证/授权错误 - 使用静态字符串
    #[error("认证失败: {message}")]
    Authentication { message: Cow<'static, str> },

    #[error("权限不足: {message}")]
    Authorization { message: Cow<'static, str> },

    /// 缓存相关错误 - 优化字符串使用
    #[error("缓存操作失败: {operation}")]
    Cache {
        operation: Cow<'static, str>,
        #[source]
        source: Option<Arc<dyn std::error::Error + Send + Sync>>,
    },

    /// 网络/连接错误 - 包装Arc以减少克隆成本
    #[error("网络错误: {message}")]
    Network {
        message: Cow<'static, str>,
        #[source]
        source: Option<Arc<dyn std::error::Error + Send + Sync>>,
    },

    #[error("HTTP错误: {status} - {message}")]
    Http {
        status: u16,
        message: Cow<'static, str>,
    },

    /// 超时错误
    #[error("操作超时")]
    Timeout,

    /// 限流错误
    #[error("请求频率过高")]
    RateLimited,

    /// 监控相关错误
    #[error("监控系统错误: {message}")]
    Monitoring {
        message: Cow<'static, str>,
        #[source]
        source: Option<Arc<dyn std::error::Error + Send + Sync>>,
    },

    /// 内部系统错误 - 不暴露内部细节
    #[error("内部服务错误")]
    Internal { message: Cow<'static, str> },

    /// 资源不足错误 - 使用枚举减少字符串分配
    #[error("资源不足: {resource}")]
    ResourceExhausted { resource: ResourceType },

    /// 序列化/反序列化错误
    #[error("数据格式错误")]
    Serialization { message: Cow<'static, str> },

    /// 安全相关错误
    #[error("安全检查失败: {message}")]
    SecurityViolation { message: Cow<'static, str> },

    /// 输入验证错误
    #[error("输入验证失败: {message}")]
    InvalidInput { message: Cow<'static, str> },

    /// 数据库错误
    #[error("数据库错误: {message}")]
    Database {
        message: Cow<'static, str>,
        #[source]
        source: Option<Arc<dyn std::error::Error + Send + Sync>>,
    },

    /// 会话相关错误
    #[error("会话错误: {message}")]
    Session { message: Cow<'static, str> },

    /// 用户管理错误
    #[error("用户管理错误: {message}")]
    UserManagement { message: Cow<'static, str> },

    /// 文件系统错误
    #[error("文件系统错误: {message}")]
    FileSystem {
        message: Cow<'static, str>,
        #[source]
        source: Option<Arc<dyn std::error::Error + Send + Sync>>,
    },

    /// 服务不可用
    #[error("服务不可用: {service}")]
    ServiceUnavailable { service: Cow<'static, str> },
}

/// 资源类型枚举 - 避免字符串分配
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ResourceType {
    Memory,
    Connections,
    FileDescriptors,
    DiskSpace,
    CpuTime,
    NetworkBandwidth,
    Cache,
    Database,
}

impl fmt::Display for ResourceType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ResourceType::Memory => write!(f, "内存"),
            ResourceType::Connections => write!(f, "连接"),
            ResourceType::FileDescriptors => write!(f, "文件描述符"),
            ResourceType::DiskSpace => write!(f, "磁盘空间"),
            ResourceType::CpuTime => write!(f, "CPU时间"),
            ResourceType::NetworkBandwidth => write!(f, "网络带宽"),
            ResourceType::Cache => write!(f, "缓存"),
            ResourceType::Database => write!(f, "数据库"),
        }
    }
}

/// 预定义的错误消息 - 使用静态字符串减少分配
mod error_messages {
    pub const CONFIG_ERROR: &str = "配置错误，请检查配置文件";
    pub const AUTH_FAILED: &str = "认证失败，请检查凭据";
    pub const ACCESS_DENIED: &str = "访问被拒绝，权限不足";
    pub const CACHE_ERROR: &str = "缓存服务暂时不可用";
    pub const NETWORK_ERROR: &str = "网络连接失败";
    pub const HTTP_CLIENT_ERROR: &str = "请求格式错误";
    pub const HTTP_SERVER_ERROR: &str = "服务器内部错误";
    pub const HTTP_GENERAL_ERROR: &str = "HTTP请求处理失败";
    pub const TIMEOUT_ERROR: &str = "请求超时，请稍后重试";
    pub const RATE_LIMITED: &str = "请求过于频繁，请稍后重试";
    pub const MONITORING_ERROR: &str = "监控服务异常";
    pub const INTERNAL_ERROR: &str = "服务暂时不可用，请稍后重试";
    pub const RESOURCE_EXHAUSTED: &str = "系统资源不足";
    pub const SERIALIZATION_ERROR: &str = "数据格式错误";
    pub const SECURITY_VIOLATION: &str = "安全检查失败，请求被拒绝";
    pub const INVALID_INPUT: &str = "输入数据无效";
    pub const DATABASE_ERROR: &str = "数据服务暂时不可用";
    pub const SESSION_ERROR: &str = "会话已过期，请重新登录";
    pub const USER_MANAGEMENT_ERROR: &str = "用户操作失败";
    pub const FILE_SYSTEM_ERROR: &str = "文件操作失败";
    pub const SERVICE_UNAVAILABLE: &str = "服务暂时不可用";
}

/// 错误构造器模式 - 消除重复的构造函数
pub struct ErrorBuilder;

impl ErrorBuilder {
    // 手动实现所有构造函数以避免宏复杂性
    pub fn config(message: impl Into<String>) -> ProxyError {
        ProxyError::Config {
            message: Cow::Owned(message.into()),
            source: None,
        }
    }

    pub fn config_with_source(
        message: impl Into<String>,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> ProxyError {
        ProxyError::Config {
            message: Cow::Owned(message.into()),
            source: Some(Arc::new(source)),
        }
    }

    pub fn authentication(message: impl Into<String>) -> ProxyError {
        ProxyError::Authentication {
            message: Cow::Owned(message.into()),
        }
    }

    pub fn authorization(message: impl Into<String>) -> ProxyError {
        ProxyError::Authorization {
            message: Cow::Owned(message.into()),
        }
    }

    pub fn cache(operation: impl Into<String>) -> ProxyError {
        ProxyError::Cache {
            operation: Cow::Owned(operation.into()),
            source: None,
        }
    }

    pub fn cache_with_source(
        operation: impl Into<String>,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> ProxyError {
        ProxyError::Cache {
            operation: Cow::Owned(operation.into()),
            source: Some(Arc::new(source)),
        }
    }

    pub fn network(message: impl Into<String>) -> ProxyError {
        ProxyError::Network {
            message: Cow::Owned(message.into()),
            source: None,
        }
    }

    pub fn network_with_source(
        message: impl Into<String>,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> ProxyError {
        ProxyError::Network {
            message: Cow::Owned(message.into()),
            source: Some(Arc::new(source)),
        }
    }

    pub fn monitoring(message: impl Into<String>) -> ProxyError {
        ProxyError::Monitoring {
            message: Cow::Owned(message.into()),
            source: None,
        }
    }

    pub fn monitoring_with_source(
        message: impl Into<String>,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> ProxyError {
        ProxyError::Monitoring {
            message: Cow::Owned(message.into()),
            source: Some(Arc::new(source)),
        }
    }

    pub fn internal(message: impl Into<String>) -> ProxyError {
        ProxyError::Internal {
            message: Cow::Owned(message.into()),
        }
    }

    pub fn serialization(message: impl Into<String>) -> ProxyError {
        ProxyError::Serialization {
            message: Cow::Owned(message.into()),
        }
    }

    pub fn security_violation(message: impl Into<String>) -> ProxyError {
        ProxyError::SecurityViolation {
            message: Cow::Owned(message.into()),
        }
    }

    pub fn invalid_input(message: impl Into<String>) -> ProxyError {
        ProxyError::InvalidInput {
            message: Cow::Owned(message.into()),
        }
    }

    pub fn database(message: impl Into<String>) -> ProxyError {
        ProxyError::Database {
            message: Cow::Owned(message.into()),
            source: None,
        }
    }

    pub fn database_with_source(
        message: impl Into<String>,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> ProxyError {
        ProxyError::Database {
            message: Cow::Owned(message.into()),
            source: Some(Arc::new(source)),
        }
    }

    pub fn session(message: impl Into<String>) -> ProxyError {
        ProxyError::Session {
            message: Cow::Owned(message.into()),
        }
    }

    pub fn user_management(message: impl Into<String>) -> ProxyError {
        ProxyError::UserManagement {
            message: Cow::Owned(message.into()),
        }
    }

    pub fn file_system(message: impl Into<String>) -> ProxyError {
        ProxyError::FileSystem {
            message: Cow::Owned(message.into()),
            source: None,
        }
    }

    pub fn file_system_with_source(
        message: impl Into<String>,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> ProxyError {
        ProxyError::FileSystem {
            message: Cow::Owned(message.into()),
            source: Some(Arc::new(source)),
        }
    }

    pub fn service_unavailable(service: impl Into<String>) -> ProxyError {
        ProxyError::ServiceUnavailable {
            service: Cow::Owned(service.into()),
        }
    }

    pub fn http(status: u16, message: impl Into<String>) -> ProxyError {
        ProxyError::Http {
            status,
            message: Cow::Owned(message.into()),
        }
    }

    pub fn resource_exhausted(resource: ResourceType) -> ProxyError {
        ProxyError::ResourceExhausted { resource }
    }

    // 便利方法
    pub fn timeout() -> ProxyError {
        ProxyError::Timeout
    }

    pub fn rate_limited() -> ProxyError {
        ProxyError::RateLimited
    }

    // 兼容性方法
    pub fn not_found(message: impl Into<String>) -> ProxyError {
        Self::http(404, message)
    }

    pub fn invalid_operation(message: impl Into<String>) -> ProxyError {
        Self::invalid_input(message)
    }
}

impl ProxyError {
    // 重新导出构造器方法，保持API兼容性
    pub fn config(message: impl Into<String>) -> Self {
        ErrorBuilder::config(message)
    }

    pub fn config_with_source(
        message: impl Into<String>,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> Self {
        ErrorBuilder::config_with_source(message, source)
    }

    pub fn authentication(message: impl Into<String>) -> Self {
        ErrorBuilder::authentication(message)
    }

    pub fn authorization(message: impl Into<String>) -> Self {
        ErrorBuilder::authorization(message)
    }

    pub fn network(message: impl Into<String>) -> Self {
        ErrorBuilder::network(message)
    }

    pub fn network_with_source(
        message: impl Into<String>,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> Self {
        ErrorBuilder::network_with_source(message, source)
    }

    pub fn http(status: u16, message: impl Into<String>) -> Self {
        ErrorBuilder::http(status, message)
    }

    pub fn internal(message: impl Into<String>) -> Self {
        ErrorBuilder::internal(message)
    }

    pub fn security_violation(message: impl Into<String>) -> Self {
        ErrorBuilder::security_violation(message)
    }

    pub fn invalid_input(message: impl Into<String>) -> Self {
        ErrorBuilder::invalid_input(message)
    }

    pub fn database(message: impl Into<String>) -> Self {
        ErrorBuilder::database(message)
    }

    pub fn session(message: impl Into<String>) -> Self {
        ErrorBuilder::session(message)
    }

    pub fn user_management(message: impl Into<String>) -> Self {
        ErrorBuilder::user_management(message)
    }

    pub fn file_system(message: impl Into<String>) -> Self {
        ErrorBuilder::file_system(message)
    }

    pub fn file_system_with_source(
        message: impl Into<String>,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> Self {
        ErrorBuilder::file_system_with_source(message, source)
    }

    pub fn service_unavailable(service: impl Into<String>) -> Self {
        ErrorBuilder::service_unavailable(service)
    }

    pub fn monitoring(message: impl Into<String>) -> Self {
        ErrorBuilder::monitoring(message)
    }

    pub fn monitoring_with_source(
        message: impl Into<String>,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> Self {
        ErrorBuilder::monitoring_with_source(message, source)
    }

    pub fn serialization(message: impl Into<String>) -> Self {
        ErrorBuilder::serialization(message)
    }

    // 兼容性方法 - 保持向后兼容
    pub fn not_found(message: impl Into<String>) -> Self {
        Self::http(404, message)
    }

    pub fn invalid_operation(message: impl Into<String>) -> Self {
        Self::invalid_input(message)
    }

    /// 从安全错误创建ProxyError
    pub fn from_security_error(msg: &str) -> Self {
        tracing::debug!("Security error: {}", msg);
        Self::SecurityViolation {
            message: msg.to_string().into(),
        }
    }

    /// 创建安全错误 - 兼容性方法
    pub fn security(message: impl Into<String>) -> Self {
        Self::security_violation(message)
    }
}

/// 统一的错误特征实现 - 消除重复的impl块
impl ProxyErrorTrait for ProxyError {
    fn error_code(&self) -> &'static str {
        match self {
            Self::Config { .. } => "CONFIG_ERROR",
            Self::Authentication { .. } => "AUTH_FAILED",
            Self::Authorization { .. } => "ACCESS_DENIED",
            Self::Cache { .. } => "CACHE_ERROR",
            Self::Network { .. } => "NETWORK_ERROR",
            Self::Http { .. } => "HTTP_ERROR",
            Self::Timeout => "TIMEOUT",
            Self::RateLimited => "RATE_LIMITED",
            Self::Monitoring { .. } => "MONITORING_ERROR",
            Self::Internal { .. } => "INTERNAL_ERROR",
            Self::ResourceExhausted { .. } => "RESOURCE_EXHAUSTED",
            Self::Serialization { .. } => "SERIALIZATION_ERROR",
            Self::SecurityViolation { .. } => "SECURITY_VIOLATION",
            Self::InvalidInput { .. } => "INVALID_INPUT",
            Self::Database { .. } => "DATABASE_ERROR",
            Self::Session { .. } => "SESSION_ERROR",
            Self::UserManagement { .. } => "USER_MANAGEMENT_ERROR",
            Self::FileSystem { .. } => "FILE_SYSTEM_ERROR",
            Self::ServiceUnavailable { .. } => "SERVICE_UNAVAILABLE",
        }
    }

    fn user_message(&self) -> &'static str {
        use error_messages::*;

        match self {
            Self::Config { .. } => CONFIG_ERROR,
            Self::Authentication { .. } => AUTH_FAILED,
            Self::Authorization { .. } => ACCESS_DENIED,
            Self::Cache { .. } => CACHE_ERROR,
            Self::Network { .. } => NETWORK_ERROR,
            Self::Http { status, .. } => match *status {
                400..=499 => HTTP_CLIENT_ERROR,
                500..=599 => HTTP_SERVER_ERROR,
                _ => HTTP_GENERAL_ERROR,
            },
            Self::Timeout => TIMEOUT_ERROR,
            Self::RateLimited => RATE_LIMITED,
            Self::Monitoring { .. } => MONITORING_ERROR,
            Self::Internal { .. } => INTERNAL_ERROR,
            Self::ResourceExhausted { .. } => RESOURCE_EXHAUSTED,
            Self::Serialization { .. } => SERIALIZATION_ERROR,
            Self::SecurityViolation { .. } => SECURITY_VIOLATION,
            Self::InvalidInput { .. } => INVALID_INPUT,
            Self::Database { .. } => DATABASE_ERROR,
            Self::Session { .. } => SESSION_ERROR,
            Self::UserManagement { .. } => USER_MANAGEMENT_ERROR,
            Self::FileSystem { .. } => FILE_SYSTEM_ERROR,
            Self::ServiceUnavailable { .. } => SERVICE_UNAVAILABLE,
        }
    }

    fn is_client_error(&self) -> bool {
        match self {
            Self::Authentication { .. }
            | Self::Authorization { .. }
            | Self::InvalidInput { .. }
            | Self::RateLimited
            | Self::SecurityViolation { .. } => true,
            Self::Http { status, .. } => (400..500).contains(status),
            _ => false,
        }
    }

    fn is_server_error(&self) -> bool {
        match self {
            Self::Config { .. }
            | Self::Cache { .. }
            | Self::Network { .. }
            | Self::Timeout
            | Self::Monitoring { .. }
            | Self::Internal { .. }
            | Self::ResourceExhausted { .. }
            | Self::Serialization { .. }
            | Self::Database { .. }
            | Self::FileSystem { .. }
            | Self::ServiceUnavailable { .. } => true,
            Self::Http { status, .. } => (500..600).contains(status),
            _ => false,
        }
    }
}

/// 为ProxyError实现额外方法（非特征方法）
impl ProxyError {
    /// 获取用户友好的错误消息 - 直接调用特征方法
    pub fn user_message(&self) -> &'static str {
        <Self as ProxyErrorTrait>::user_message(self)
    }

    /// 获取错误代码 - 直接调用特征方法
    pub fn error_code(&self) -> &'static str {
        <Self as ProxyErrorTrait>::error_code(self)
    }

    /// 判断是否为客户端错误 - 直接调用特征方法
    pub fn is_client_error(&self) -> bool {
        <Self as ProxyErrorTrait>::is_client_error(self)
    }

    /// 判断是否为服务器错误 - 直接调用特征方法
    pub fn is_server_error(&self) -> bool {
        <Self as ProxyErrorTrait>::is_server_error(self)
    }

    /// 安全日志记录 - 记录详细信息但不暴露给用户
    pub fn log_security_event(&self, context: &str) {
        match self {
            Self::SecurityViolation { message } => {
                tracing::warn!(
                    target: "security",
                    context = context,
                    error = %message,
                    "安全违规事件"
                );
            }
            Self::Authentication { message } => {
                tracing::warn!(
                    target: "security",
                    context = context,
                    error = %message,
                    "认证失败事件"
                );
            }
            Self::Authorization { message } => {
                tracing::warn!(
                    target: "security",
                    context = context,
                    error = %message,
                    "授权失败事件"
                );
            }
            _ => {
                tracing::debug!(
                    target: "security",
                    context = context,
                    error = %self,
                    "安全相关错误"
                );
            }
        }
    }
}

/// 统一的结果类型
pub type Result<T> = std::result::Result<T, ProxyError>;

/// 认证错误类型 - 分离到独立模块
#[derive(Error, Debug)]
pub enum AuthError {
    #[error("认证失败: {0}")]
    AuthenticationFailed(String),
    #[error("授权失败")]
    AuthorizationFailed,
    #[error("会话无效")]
    SessionInvalid,
}

/// 统一错误转换模块 - 优化版本，消除重复的From实现
pub mod conversions {
    use super::*;

    /// 错误转换特征 - 统一转换接口
    pub trait IntoProxyError {
        fn into_proxy_error(self) -> ProxyError;
        fn into_proxy_error_with_context(self, context: &str) -> ProxyError;
    }

    // 手动实现错误转换以避免宏复杂性
    impl From<std::io::Error> for ProxyError {
        fn from(err: std::io::Error) -> Self {
            tracing::debug!("Converting std::io::Error to ProxyError with source");
            ErrorBuilder::network_with_source(err.to_string(), err)
        }
    }

    impl From<serde_json::Error> for ProxyError {
        fn from(err: serde_json::Error) -> Self {
            tracing::debug!("Converting serde_json::Error to ProxyError");
            ErrorBuilder::serialization(err.to_string())
        }
    }

    impl From<serde_yaml::Error> for ProxyError {
        fn from(err: serde_yaml::Error) -> Self {
            tracing::debug!("Converting serde_yaml::Error to ProxyError");
            ErrorBuilder::serialization(err.to_string())
        }
    }

    impl From<toml::de::Error> for ProxyError {
        fn from(err: toml::de::Error) -> Self {
            tracing::debug!("Converting toml::de::Error to ProxyError");
            ErrorBuilder::serialization(err.to_string())
        }
    }

    impl From<toml::ser::Error> for ProxyError {
        fn from(err: toml::ser::Error) -> Self {
            tracing::debug!("Converting toml::ser::Error to ProxyError");
            ErrorBuilder::serialization(err.to_string())
        }
    }

    // 认证错误的统一转换
    impl From<AuthError> for ProxyError {
        fn from(err: AuthError) -> Self {
            tracing::debug!("Authentication error: {}", err);
            match err {
                AuthError::AuthenticationFailed(msg) => {
                    ErrorBuilder::authentication(format!("认证失败: {}", msg))
                }
                AuthError::AuthorizationFailed => ErrorBuilder::authorization("授权失败"),
                AuthError::SessionInvalid => ErrorBuilder::session("会话无效"),
            }
        }
    }

    impl IntoProxyError for AuthError {
        fn into_proxy_error(self) -> ProxyError {
            self.into()
        }

        fn into_proxy_error_with_context(self, context: &str) -> ProxyError {
            match self {
                AuthError::AuthenticationFailed(msg) => {
                    ErrorBuilder::authentication(format!("{}: 认证失败: {}", context, msg))
                }
                AuthError::AuthorizationFailed => {
                    ErrorBuilder::authorization(format!("{}: 授权失败", context))
                }
                AuthError::SessionInvalid => {
                    ErrorBuilder::session(format!("{}: 会话无效", context))
                }
            }
        }
    }

    /// 批量错误转换工具
    pub struct ErrorBatch {
        errors: Vec<ProxyError>,
        context: Option<String>,
    }

    impl ErrorBatch {
        pub fn new() -> Self {
            Self {
                errors: Vec::new(),
                context: None,
            }
        }

        pub fn with_context(context: impl Into<String>) -> Self {
            Self {
                errors: Vec::new(),
                context: Some(context.into()),
            }
        }

        /// 添加错误到批次
        pub fn add<E: IntoProxyError>(&mut self, error: E) -> &mut Self {
            let proxy_error = if let Some(ref context) = self.context {
                error.into_proxy_error_with_context(context)
            } else {
                error.into_proxy_error()
            };
            self.errors.push(proxy_error);
            self
        }

        /// 获取第一个错误
        pub fn first(self) -> Option<ProxyError> {
            self.errors.into_iter().next()
        }

        /// 合并所有错误为一个错误
        pub fn combine(self) -> Option<ProxyError> {
            if self.errors.is_empty() {
                return None;
            }

            if self.errors.len() == 1 {
                return self.errors.into_iter().next();
            }

            // 合并多个错误
            let messages: Vec<String> = self.errors.iter().map(|e| e.to_string()).collect();

            Some(ErrorBuilder::internal(format!(
                "多个错误: {}",
                messages.join("; ")
            )))
        }

        /// 获取所有错误
        pub fn all(self) -> Vec<ProxyError> {
            self.errors
        }
    }

    impl Default for ErrorBatch {
        fn default() -> Self {
            Self::new()
        }
    }

    /// 结果转换工具
    pub trait ResultExt<T> {
        /// 为Result添加上下文
        fn with_context(self, context: &str) -> Result<T>;
        /// 映射错误类型
        fn map_err_to_proxy<E: IntoProxyError>(self) -> Result<T>;
    }

    impl<T, E: IntoProxyError> ResultExt<T> for std::result::Result<T, E> {
        fn with_context(self, context: &str) -> Result<T> {
            self.map_err(|e| e.into_proxy_error_with_context(context))
        }

        fn map_err_to_proxy<Er: IntoProxyError>(self) -> Result<T> {
            self.map_err(|e| e.into_proxy_error())
        }
    }
}

/// 优化的输入验证模块 - 保留核心功能，优化性能
pub mod input_validation {
    use super::*;
    use once_cell::sync::Lazy;
    use regex::Regex;
    use std::borrow::Cow;

    /// 验证配置
    #[derive(Debug, Clone)]
    pub struct ValidationConfig {
        pub max_string_length: usize,
        pub max_path_length: usize,
        pub max_url_length: usize,
        pub allow_unicode: bool,
        pub strict_mode: bool,
    }

    impl Default for ValidationConfig {
        fn default() -> Self {
            Self {
                max_string_length: 1000,
                max_path_length: 260,
                max_url_length: 2048,
                allow_unicode: true,
                strict_mode: false,
            }
        }
    }
    /// 预编译的验证模式 - 优化性能
    static VALIDATION_PATTERNS: Lazy<ValidationPatterns> = Lazy::new(ValidationPatterns::new);

    struct ValidationPatterns {
        // 合并相关模式以减少匹配次数
        security_threats: Regex,
        path_traversal: Regex,
        dangerous_protocols: Regex,
        hostname_format: Regex,
    }

    impl ValidationPatterns {
        fn new() -> Self {
            Self {
                // 合并所有安全威胁模式到一个正则表达式中
                security_threats: Regex::new(r"(?i)(\bunion\s+select\b|<script[^>]*>|javascript\s*:|[;&|`$]|\.\./|%2e%2e)").unwrap(),

                // 专门的路径遍历检测
                path_traversal: Regex::new(r"(?i)(\.\.[\\/]|[\\/]\.\.|%2e%2e|\\\.\\\.|\./\.|%252e%252e)").unwrap(),

                // 危险协议检测
                dangerous_protocols: Regex::new(r"(?i)(javascript|vbscript|data|file):").unwrap(),

                // 主机名格式验证
                hostname_format: Regex::new(r"^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$").unwrap(),
            }
        }
    }

    /// 验证字符串是否安全（防止注入攻击）
    pub fn validate_safe_string(input: &str) -> Result<()> {
        if input.is_empty() {
            return Err(ErrorBuilder::invalid_input("字符串不能为空"));
        }

        if input.len() > 1000 {
            return Err(ErrorBuilder::invalid_input("字符串长度超出限制"));
        }

        // 检查控制字符
        if input
            .chars()
            .any(|c| c.is_control() && c != '\n' && c != '\t' && c != '\r')
        {
            return Err(ErrorBuilder::security_violation("输入包含非法控制字符"));
        }

        // 使用合并的安全威胁检测
        if VALIDATION_PATTERNS.security_threats.is_match(input) {
            return Err(ErrorBuilder::security_violation("检测到潜在的安全威胁"));
        }

        Ok(())
    }

    /// 验证路径是否安全 - 使用完整的路径遍历检测
    pub fn validate_safe_path(path: &str) -> Result<()> {
        if path.is_empty() {
            return Err(ErrorBuilder::invalid_input("路径不能为空"));
        }

        if path.len() > 260 {
            return Err(ErrorBuilder::invalid_input("路径长度超出限制"));
        }

        // 使用完整的路径遍历检测，替代简单的字符串包含检查
        if VALIDATION_PATTERNS.path_traversal.is_match(path) {
            return Err(ErrorBuilder::security_violation("检测到路径遍历攻击"));
        }

        // 额外的路径遍历检测模式
        let dangerous_patterns = [
            "../",
            "..\\",
            "%2e%2e%2f",
            "%2e%2e%5c",
            "%252e%252e",
            "\\..\\",
            "/./",
            "\\.\\",
            "%c0%af",
            "%c1%9c",
        ];

        let path_lower = path.to_lowercase();
        for pattern in &dangerous_patterns {
            if path_lower.contains(pattern) {
                return Err(ErrorBuilder::security_violation("检测到编码的路径遍历攻击"));
            }
        }

        // 检查绝对路径 - 原有逻辑保持
        if path.starts_with('/') || path.starts_with('\\') || path.contains(':') {
            return Err(ErrorBuilder::security_violation("不允许绝对路径"));
        }

        // 检查非法字符
        if path
            .chars()
            .any(|c| matches!(c, '<' | '>' | ':' | '"' | '|' | '?' | '*'))
        {
            return Err(ErrorBuilder::invalid_input("路径包含非法字符"));
        }

        // 检查路径深度
        let depth = path.matches('/').count();
        if depth > 10 {
            return Err(ErrorBuilder::security_violation("路径深度过深"));
        }

        Ok(())
    }

    /// 验证URL是否安全
    pub fn validate_safe_url(url: &str) -> Result<()> {
        if url.is_empty() {
            return Err(ErrorBuilder::invalid_input("URL不能为空"));
        }

        if url.len() > 2048 {
            return Err(ErrorBuilder::invalid_input("URL长度超出限制"));
        }

        // 检查危险协议
        if VALIDATION_PATTERNS.dangerous_protocols.is_match(url) {
            return Err(ErrorBuilder::security_violation("检测到危险URL协议"));
        }

        // 检查URL中的路径遍历
        if let Some(path_start) = url.find("://").map(|i| i + 3) {
            if let Some(path_begin) = url[path_start..].find('/') {
                let path = &url[path_start + path_begin..];
                if !path.is_empty() && VALIDATION_PATTERNS.path_traversal.is_match(path) {
                    return Err(ErrorBuilder::security_violation("URL中检测到路径遍历"));
                }
            }
        }

        Ok(())
    }

    /// 验证主机名
    pub fn validate_safe_hostname(hostname: &str) -> Result<String> {
        if hostname.is_empty() {
            return Err(ErrorBuilder::invalid_input("主机名不能为空"));
        }

        if hostname.len() > 253 {
            return Err(ErrorBuilder::invalid_input("主机名过长"));
        }

        if !VALIDATION_PATTERNS.hostname_format.is_match(hostname) {
            return Err(ErrorBuilder::invalid_input("主机名格式无效"));
        }

        Ok(hostname.to_lowercase())
    }

    /// 安全地截断字符串（防止日志注入）- 优化内存使用
    pub fn safe_truncate(input: &str, max_len: usize) -> Cow<'_, str> {
        if input.len() <= max_len {
            // 检查是否需要清理控制字符
            if input
                .chars()
                .any(|c| c.is_control() && c != '\n' && c != '\t')
            {
                let clean: String = input
                    .chars()
                    .filter(|c| !c.is_control() || *c == '\n' || *c == '\t')
                    .collect();
                Cow::Owned(clean)
            } else {
                Cow::Borrowed(input) // 零分配
            }
        } else {
            let truncated: String = input
                .chars()
                .take(max_len)
                .filter(|c| !c.is_control() || *c == '\n' || *c == '\t')
                .collect();
            Cow::Owned(format!("{}...", truncated))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_builder_eliminates_duplication() {
        // 测试构造器是否正确工作
        let config_error = ErrorBuilder::config("测试配置错误");
        assert_eq!(config_error.error_code(), "CONFIG_ERROR");

        let network_error = ErrorBuilder::network("测试网络错误");
        assert_eq!(network_error.error_code(), "NETWORK_ERROR");
    }

    #[test]
    fn test_no_duplicate_trait_implementations() {
        // 测试特征方法只有一个实现
        let error = ErrorBuilder::authentication("测试");
        assert_eq!(error.error_code(), "AUTH_FAILED");
        assert_eq!(error.user_message(), "认证失败，请检查凭据");
        assert!(error.is_client_error());
        assert!(!error.is_server_error());
    }

    #[test]
    fn test_optimized_validation() {
        // 测试优化的验证逻辑
        assert!(input_validation::validate_safe_string("normal_string").is_ok());
        assert!(input_validation::validate_safe_string("<script>alert(1)</script>").is_err());
        assert!(input_validation::validate_safe_string("../../../etc/passwd").is_err());
    }

    #[test]
    fn test_error_conversion_consistency() {
        // 测试错误转换的一致性
        let io_error = std::io::Error::new(std::io::ErrorKind::NotFound, "文件未找到");
        let proxy_error: ProxyError = io_error.into();
        assert_eq!(proxy_error.error_code(), "NETWORK_ERROR");

        let json_error = serde_json::from_str::<serde_json::Value>("invalid json").unwrap_err();
        let proxy_error: ProxyError = json_error.into();
        assert_eq!(proxy_error.error_code(), "SERIALIZATION_ERROR");
    }
}
