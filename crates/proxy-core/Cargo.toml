[package]
name = "proxy-core"
version = "0.1.0"
edition = "2021"
description = "Core proxy service built on Pingora"

[dependencies]
# 基础依赖
proxy-types = { path = "../proxy-types" }
proxy-config = { path = "../proxy-config" }

# Pingora依赖 - 使用工作空间版本
pingora = { workspace = true, features = ["lb"], default-features = false }
pingora-core = { workspace = true, default-features = false }
pingora-proxy = { workspace = true, default-features = false }
pingora-http = { workspace = true, default-features = false }
pingora-load-balancing = { workspace = true, default-features = false }

# 标准依赖 - 使用工作空间版本
tokio = { workspace = true }
anyhow = { workspace = true }
tracing = { workspace = true }
serde = { workspace = true }
async-trait = { workspace = true }
thiserror = { workspace = true }
once_cell = { workspace = true }
regex = { workspace = true }
serde_json = { workspace = true }
serde_yaml = { workspace = true }

# 特定依赖
toml = "0.8"
paste = "1.0"  # 用于宏生成
zeroize = { version = "1.7", features = ["derive"] }

[features]
default = []