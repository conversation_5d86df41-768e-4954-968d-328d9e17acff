use crate::unified::{CacheConfig, ProxyConfig, Route, SecurityConfig};
use once_cell::sync::Lazy;
use regex::Regex;
use std::borrow::Cow;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};

// 全局正则表达式缓存 - 解决重复编译问题
#[allow(dead_code, clippy::type_complexity)]
static REGEX_CACHE: Lazy<Arc<RwLock<HashMap<String, Arc<Regex>>>>> =
    Lazy::new(|| Arc::new(RwLock::new(HashMap::new())));

// 预编译的常用正则表达式
static HOSTNAME_REGEX: Lazy<Arc<Regex>> = Lazy::new(|| {
    Arc::new(Regex::new(r"^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$").unwrap())
});

static IP_REGEX: Lazy<Arc<Regex>> = Lazy::new(|| {
    Arc::new(Regex::new(r"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$").unwrap())
});

static PATH_REGEX: Lazy<Arc<Regex>> =
    Lazy::new(|| Arc::new(Regex::new(r"^/[a-zA-Z0-9\-._~!$&'()*+,;=:@/]*$").unwrap()));

// 常用错误消息常量 - 减少字符串分配
const INVALID_FORMAT_MSG: &str = "格式无效";
const OUT_OF_RANGE_MSG: &str = "超出有效范围";
const REQUIRED_FIELD_MSG: &str = "必填字段缺失";

/// 优化的错误收集器，使用String容量预分配
#[derive(Debug, Default)]
pub struct ValidationErrors {
    errors: Vec<String>,
}

impl ValidationErrors {
    pub fn new() -> Self {
        Self {
            errors: Vec::with_capacity(16), // 预分配容量
        }
    }
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            errors: Vec::with_capacity(capacity),
        }
    }

    // 使用Cow避免不必要的字符串分配
    pub fn add_error_cow(&mut self, field_path: &str, message: Cow<'static, str>) {
        let mut error_msg = String::with_capacity(field_path.len() + message.len() + 10);
        error_msg.push_str("字段 ");
        error_msg.push_str(field_path);
        error_msg.push(' ');
        error_msg.push_str(&message);
        self.errors.push(error_msg);
    }

    pub fn add_error(&mut self, field_path: &str, message: &str) {
        self.add_error_cow(field_path, Cow::Owned(message.to_string()));
    }

    pub fn add_format_error(&mut self, field_path: &str, value: &str) {
        let mut error_msg = String::with_capacity(field_path.len() + value.len() + 30);
        error_msg.push_str("字段 ");
        error_msg.push_str(field_path);
        error_msg.push_str(" 的值 '");
        error_msg.push_str(value);
        error_msg.push_str("' ");
        error_msg.push_str(INVALID_FORMAT_MSG);
        self.errors.push(error_msg);
    }

    pub fn add_range_error<T: std::fmt::Display>(&mut self, field_path: &str, value: T) {
        let mut error_msg = String::with_capacity(field_path.len() + 50);
        error_msg.push_str("字段 ");
        error_msg.push_str(field_path);
        error_msg.push_str(" 的值 ");
        error_msg.push_str(&value.to_string());
        error_msg.push(' ');
        error_msg.push_str(OUT_OF_RANGE_MSG);
        self.errors.push(error_msg);
    }

    pub fn is_empty(&self) -> bool {
        self.errors.is_empty()
    }

    pub fn len(&self) -> usize {
        self.errors.len()
    }

    pub fn into_errors(self) -> Vec<String> {
        self.errors
    }
}

// 移除未使用的 get_cached_regex 函数 - 已注释掉
// /// 获取缓存的正则表达式
// fn get_cached_regex(pattern: &str) -> Result<Arc<Regex>, String> {
//     // 首先尝试读取缓存
//     {
//         let cache = REGEX_CACHE.read().map_err(|_| "正则表达式缓存锁定失败")?;
//         if let Some(regex) = cache.get(pattern) {
//             return Ok(Arc::clone(regex));
//         }
//     }
//
//     // 如果缓存中没有，编译新的正则表达式
//     let regex = Arc::new(Regex::new(pattern).map_err(|e| format!("正则表达式编译失败: {}", e))?);
//
//     // 写入缓存
//     {
//         let mut cache = REGEX_CACHE.write().map_err(|_| "正则表达式缓存锁定失败")?;
//         // 限制缓存大小，避免内存泄漏
//         if cache.len() >= 100 {
//             cache.clear(); // 简单的清理策略
//         }
//         cache.insert(pattern.to_string(), Arc::clone(&regex));
//     }
//
//     Ok(regex)
// }

/// 优化的配置验证器
pub struct ConfigValidator {
    strict_mode: bool,
    max_errors: usize,
}

impl Default for ConfigValidator {
    fn default() -> Self {
        Self {
            strict_mode: false,
            max_errors: 50, // 限制错误数量，避免内存问题
        }
    }
}

impl ConfigValidator {
    pub fn new(strict_mode: bool) -> Self {
        Self {
            strict_mode,
            max_errors: if strict_mode { 100 } else { 50 },
        }
    }

    /// 静态验证方法 - 供外部调用
    pub fn validate_config(config: &ProxyConfig) -> Result<(), Vec<String>> {
        let validator = Self::default();
        validator.validate(config)
    }

    /// 主要验证入口点 - 优化版本
    pub fn validate(&self, config: &ProxyConfig) -> Result<(), Vec<String>> {
        let mut errors = ValidationErrors::with_capacity(32);

        // 早期退出检查
        if self.validate_basic_structure(config, &mut errors).is_err() {
            return Err(errors.into_errors());
        }

        // 并行验证不同部分（如果错误数量在控制范围内）
        if errors.len() < self.max_errors / 4 {
            self.validate_servers(config, &mut errors);
        }

        if errors.len() < self.max_errors / 2 {
            self.validate_security_config(config, &mut errors);
        }

        if errors.len() < self.max_errors * 3 / 4 {
            self.validate_cache_config(config, &mut errors);
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors.into_errors())
        }
    }

    /// 验证基本结构
    fn validate_basic_structure(
        &self,
        config: &ProxyConfig,
        errors: &mut ValidationErrors,
    ) -> Result<(), ()> {
        // 由于我们的 ProxyConfig 没有 servers 字段，而是单个 server
        // 修改验证逻辑
        if config.server.listen.is_empty() {
            errors.add_error("server.listen", REQUIRED_FIELD_MSG);
            return Err(());
        }

        Ok(())
    }

    /// 优化的服务器验证
    fn validate_servers(&self, config: &ProxyConfig, errors: &mut ValidationErrors) {
        // 验证单个服务器配置
        self.validate_server(&config.server, "server", errors);

        // 验证路由
        for (i, route) in config.routes.iter().enumerate() {
            if errors.len() >= self.max_errors {
                break;
            }
            let route_path = format!("routes[{}]", i);
            self.validate_route(route, &route_path, errors);
        }
    }

    /// 验证单个服务器 - 使用预编译正则表达式
    fn validate_server(
        &self,
        server: &crate::unified::ServerConfig,
        path: &str,
        errors: &mut ValidationErrors,
    ) {
        // 验证监听地址
        let listen_path = format!("{}.listen", path);
        if !self.is_valid_address(&server.listen) {
            errors.add_format_error(&listen_path, &server.listen);
        }

        // 验证端口范围
        if let Some(port_str) = server.listen.split(':').next_back() {
            if let Ok(port) = port_str.parse::<u16>() {
                if port == 0 {
                    errors.add_range_error(&listen_path, port);
                }
            }
        }
    }

    /// 优化的地址验证 - 使用预编译正则表达式
    fn is_valid_address(&self, address: &str) -> bool {
        if let Some((host, port)) = address.rsplit_once(':') {
            // 验证端口
            if port.parse::<u16>().is_err() {
                return false;
            }

            // 验证主机名或IP
            if host == "localhost" || host.is_empty() {
                return true;
            }

            // 使用预编译的正则表达式
            IP_REGEX.is_match(host) || HOSTNAME_REGEX.is_match(host)
        } else {
            false
        }
    }

    /// 验证路由配置
    fn validate_route(&self, route: &Route, path: &str, errors: &mut ValidationErrors) {
        // 验证路径格式
        let path_field = format!("{}.path", path);
        if !PATH_REGEX.is_match(&route.path) {
            errors.add_format_error(&path_field, &route.path);
        }

        // 验证目标地址
        let target_field = format!("{}.target", path);
        if !self.is_valid_target(&route.target) {
            errors.add_format_error(&target_field, &route.target);
        }
    }

    /// 验证目标地址
    fn is_valid_target(&self, target: &str) -> bool {
        // 支持多种目标格式
        if target.starts_with("http://") || target.starts_with("https://") {
            return true;
        }

        // 简单的主机:端口格式
        self.is_valid_address(target)
    }

    /// 验证安全配置 - 加强TLS版本检查
    fn validate_security_config(&self, config: &ProxyConfig, errors: &mut ValidationErrors) {
        if let Some(security) = &config.security {
            self.validate_tls_config(security, errors);
            self.validate_auth_config(security, errors);
        }
    }

    /// 加强的TLS配置验证
    fn validate_tls_config(&self, security: &SecurityConfig, errors: &mut ValidationErrors) {
        // 禁止不安全的TLS版本
        if let Some(min_version) = &security.tls_min_version {
            match min_version.as_str() {
                "1.0" | "1.1" => {
                    errors.add_error(
                        "security.tls_min_version",
                        "不安全的TLS版本，最低要求TLS 1.2",
                    );
                }
                "1.2" | "1.3" => {
                    // 安全的版本
                }
                _ => {
                    errors.add_format_error("security.tls_min_version", min_version);
                }
            }
        } else if self.strict_mode {
            errors.add_error("security.tls_min_version", "严格模式下必须指定TLS最低版本");
        }

        // 验证密码套件
        if let Some(ciphers) = &security.tls_ciphers {
            self.validate_cipher_suites(ciphers, errors);
        }
    }

    /// 验证密码套件
    fn validate_cipher_suites(&self, ciphers: &[String], errors: &mut ValidationErrors) {
        // 禁止的不安全密码套件
        const INSECURE_CIPHERS: &[&str] = &["RC4", "MD5", "NULL", "EXPORT", "DES", "3DES"];

        for cipher in ciphers {
            for insecure in INSECURE_CIPHERS {
                if cipher.contains(insecure) {
                    errors.add_error(
                        "security.tls_ciphers",
                        &format!("不安全的密码套件: {}", cipher),
                    );
                    break;
                }
            }
        }
    }

    /// 验证认证配置
    fn validate_auth_config(&self, security: &SecurityConfig, errors: &mut ValidationErrors) {
        // 验证JWT配置
        if let Some(jwt_secret) = &security.jwt_secret {
            if jwt_secret.len() < 32 {
                errors.add_error("security.jwt_secret", "JWT密钥长度至少32个字符");
            }
        }

        // 验证速率限制
        if let Some(rate_limit) = security.rate_limit {
            if rate_limit == 0 {
                errors.add_range_error("security.rate_limit", rate_limit);
            }
        }
    }

    /// 验证缓存配置
    fn validate_cache_config(&self, config: &ProxyConfig, errors: &mut ValidationErrors) {
        if let Some(cache) = &config.cache {
            self.validate_cache_settings(cache, errors);
        }
    }

    /// 验证缓存设置
    fn validate_cache_settings(&self, cache: &CacheConfig, errors: &mut ValidationErrors) {
        // 验证缓存大小
        if let Some(max_size) = cache.max_size {
            if max_size == 0 {
                errors.add_range_error("cache.max_size", max_size);
            }
        }

        // 验证TTL
        if let Some(default_ttl) = cache.default_ttl {
            if default_ttl == 0 {
                errors.add_range_error("cache.default_ttl", default_ttl);
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    #[test]
    fn test_config_validator_basic() {
        let config = ProxyConfig::default();
        let result = ConfigValidator::validate_config(&config);
        assert!(result.is_ok());
    }
}

/// 字段访问器 - 用于动态设置配置字段
pub struct FieldAccessor;

impl FieldAccessor {
    /// 设置字段值
    pub fn set_field_value(
        config: &mut ProxyConfig,
        key: &str,
        value: &str,
    ) -> Result<(), crate::ConfigError> {
        match key {
            "server.listen" => config.server.listen = value.to_string(),
            "server.workers" => {
                config.server.workers = Some(value.parse().map_err(|_| {
                    crate::ConfigError::ParseError(format!("无效的 workers 值: {}", value))
                })?);
            }
            _ => {
                return Err(crate::ConfigError::ParseError(format!(
                    "未知的配置字段: {}",
                    key
                )));
            }
        }
        Ok(())
    }
}
