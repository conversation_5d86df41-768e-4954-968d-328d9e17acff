//! 代理配置管理 - 统一配置系统
//!
//! 提供完整的配置管理功能，包括加载、验证、转换和监控

// 重新导出核心模块
pub mod loader;
pub mod unified;
pub mod validation;
pub mod watcher;

// 统一导出 - 修复ConfigLoader重复导出问题
pub use loader::{ConfigFormat, ConfigLoader}; // 直接从loader导出ConfigLoader
pub use unified::{
    load_config,
    CacheConfig,
    ConfigConverter,
    ConfigManager,
    CorsConfig,
    HealthCheckConfig,
    ProxyConfig,
    RateLimitConfig,
    RouteConfig,
    SecurityConfig,
    ServerConfig,
    ServerEntry,
    UnifiedConfig,
    UpstreamConfig,
    // 移除ConfigLoader以避免冲突
};
pub use validation::{ConfigValidator, FieldAccessor, ValidationErrors};
#[cfg(feature = "watch")]
pub use watcher::{ConfigChangeEvent, ConfigWatcher};

/// 配置错误类型
#[derive(Debug, thiserror::Error)]
pub enum ConfigError {
    #[error("文件操作错误: {0}")]
    FileError(String),

    #[error("解析错误: {0}")]
    ParseError(String),

    #[error("验证错误: {0}")]
    ValidationError(String),

    #[error("转换错误: {0}")]
    ConversionError(String),

    #[error("不支持的格式: {0}")]
    UnsupportedFormat(String),

    #[error("配置监听器错误: {0}")]
    WatcherError(String),
}

/// 配置工具库的版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
