[package]
name = "proxy-config"
version = "0.1.0"
edition = "2021"
description = "Configuration management for proxy system"

[dependencies]
# 基础依赖
proxy-types = { path = "../proxy-types" }  # 只依赖基础类型

# 使用工作空间统一版本
serde = { workspace = true }
serde_json = { workspace = true }
serde_yaml = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true, features = ["fs"] }
tracing = { workspace = true }
once_cell = { workspace = true }
regex = { workspace = true }

# 特定依赖
toml = "0.8"
notify = { version = "6.1", optional = true }

[dev-dependencies]
tempfile = "3.8"

[features]
default = []
watch = ["dep:notify"]