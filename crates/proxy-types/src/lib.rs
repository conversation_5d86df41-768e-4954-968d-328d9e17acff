//! 共享类型和特征定义
//!
//! 这个crate提供所有其他crate都需要的基础类型和特征，
//! 避免循环依赖问题

#![allow(async_fn_in_trait)]

use serde::{Deserialize, Serialize};
use std::fmt;

/// 基础错误特征 - 所有错误类型都应该实现这个特征
pub trait ProxyErrorTrait: std::error::Error + Send + Sync + 'static {
    /// 获取错误代码
    fn error_code(&self) -> &'static str;

    /// 获取用户友好的错误消息
    fn user_message(&self) -> &'static str;

    /// 判断是否为客户端错误
    fn is_client_error(&self) -> bool;

    /// 判断是否为服务器错误
    fn is_server_error(&self) -> bool;
}

/// 配置特征 - 所有配置类型都应该实现这个特征
pub trait ConfigTrait: Clone + Send + Sync + 'static {
    /// 验证配置是否有效
    fn validate(&self) -> Result<(), Box<dyn ProxyErrorTrait>>;

    /// 合并另一个配置
    fn merge(&mut self, other: Self);
}

/// 服务特征 - 所有服务都应该实现这个特征
pub trait ServiceTrait: Send + Sync + 'static {
    type Config: ConfigTrait;
    type Error: ProxyErrorTrait;

    /// 启动服务
    async fn start(&self, config: &Self::Config) -> Result<(), Self::Error>;

    /// 停止服务
    async fn stop(&self) -> Result<(), Self::Error>;

    /// 检查服务健康状态
    async fn health_check(&self) -> Result<bool, Self::Error>;
}

/// 缓存特征
pub trait CacheTrait: Send + Sync + 'static {
    type Error: ProxyErrorTrait;

    async fn get(&self, key: &str) -> Result<Option<Vec<u8>>, Self::Error>;
    async fn set(&self, key: &str, value: Vec<u8>, ttl: Option<u64>) -> Result<(), Self::Error>;
    async fn delete(&self, key: &str) -> Result<(), Self::Error>;
    async fn clear(&self) -> Result<(), Self::Error>;
}

/// 认证特征
pub trait AuthTrait: Send + Sync + 'static {
    type Error: ProxyErrorTrait;
    type User: Clone + Send + Sync;
    type Claims: Clone + Send + Sync;

    async fn authenticate(&self, username: &str, password: &str)
        -> Result<Self::User, Self::Error>;
    async fn generate_token(&self, user: &Self::User) -> Result<String, Self::Error>;
    async fn verify_token(&self, token: &str) -> Result<Self::Claims, Self::Error>;
}

/// 监控特征
pub trait MonitoringTrait: Send + Sync + 'static {
    type Error: ProxyErrorTrait;
    type Metrics: Clone + Send + Sync;

    async fn record_metric(&self, name: &str, value: f64) -> Result<(), Self::Error>;
    async fn get_metrics(&self) -> Result<Self::Metrics, Self::Error>;
}

/// 通用结果类型
pub type Result<T, E = Box<dyn ProxyErrorTrait>> = std::result::Result<T, E>;

/// 基础配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BaseConfig {
    pub enabled: bool,
    pub debug: bool,
}

impl Default for BaseConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            debug: false,
        }
    }
}

/// 网络地址类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct NetworkAddress {
    pub host: String,
    pub port: u16,
}

impl fmt::Display for NetworkAddress {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}:{}", self.host, self.port)
    }
}

impl From<(String, u16)> for NetworkAddress {
    fn from((host, port): (String, u16)) -> Self {
        Self { host, port }
    }
}

impl From<(&str, u16)> for NetworkAddress {
    fn from((host, port): (&str, u16)) -> Self {
        Self {
            host: host.to_string(),
            port,
        }
    }
}

/// 服务状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ServiceStatus {
    Starting,
    Running,
    Stopping,
    Stopped,
    Failed,
}

impl fmt::Display for ServiceStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ServiceStatus::Starting => write!(f, "正在启动"),
            ServiceStatus::Running => write!(f, "运行中"),
            ServiceStatus::Stopping => write!(f, "正在停止"),
            ServiceStatus::Stopped => write!(f, "已停止"),
            ServiceStatus::Failed => write!(f, "失败"),
        }
    }
}

/// 健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthStatus {
    pub healthy: bool,
    pub message: Option<String>,
    pub details: std::collections::HashMap<String, String>,
}

impl HealthStatus {
    pub fn healthy() -> Self {
        Self {
            healthy: true,
            message: None,
            details: std::collections::HashMap::new(),
        }
    }

    pub fn unhealthy(message: impl Into<String>) -> Self {
        Self {
            healthy: false,
            message: Some(message.into()),
            details: std::collections::HashMap::new(),
        }
    }

    pub fn with_detail(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.details.insert(key.into(), value.into());
        self
    }
}
