//! 域名池管理统一模块
//!
//! 这个模块整合了域名池的所有功能：
//! - MongoDB数据模型
//! - 业务逻辑服务
//! - API接口
//! - 数据访问层
//! - 域名提取功能
//! - 内容替换功能

pub mod api;
pub mod extractor; // 新增：域名提取功能
pub mod models;
pub mod replacer;
pub mod repository;
pub mod service; // 新增：内容替换功能

// 统一导出
pub use extractor::DomainExtractor; // 导出域名提取器
pub use models::*;
pub use replacer::ContentReplacer;
pub use service::DomainPoolService; // 导出内容替换器
