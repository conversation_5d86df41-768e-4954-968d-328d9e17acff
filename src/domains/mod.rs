//! 域名池管理统一模块
//!
//! 这个模块整合了域名池的所有功能：
//! - MongoDB数据模型
//! - 业务逻辑服务
//! - API接口
//! - 数据访问层
//! - 域名提取功能
//! - 内容替换功能

pub mod api;
pub mod extractor; // 新增：域名提取功能
pub mod models;
pub mod replacer;
pub mod repository;
pub mod service; // 新增：内容替换功能
pub mod subdomain_generator; // 新增：智能子域名生成器
pub mod temp_url_storage; // 新增：临时URL存储管理器
pub mod temp_url_cleanup_task; // 新增：临时URL清理定时任务
pub mod quota_manager; // 新增：下游域名配额管理器
pub mod upstream_queue; // 新增：FIFO上游域名队列管理器
pub mod fifo_processor_task; // 新增：FIFO队列处理定时任务
pub mod batch_cache_updater; // 新增：批量缓存更新器

#[cfg(test)]
mod subdomain_generator_integration_test; // 集成测试

// 统一导出
pub use extractor::DomainExtractor; // 导出域名提取器
pub use models::*;
pub use replacer::ContentReplacer;
pub use service::DomainPoolService; // 导出内容替换器
pub use subdomain_generator::SubdomainGenerator; // 导出智能子域名生成器
pub use temp_url_storage::{TempUrlStorage, TempUrlStats}; // 导出临时URL存储管理器
pub use temp_url_cleanup_task::TempUrlCleanupTask; // 导出临时URL清理任务
pub use quota_manager::QuotaManager; // 导出配额管理器
pub use upstream_queue::UpstreamQueue; // 导出FIFO上游域名队列管理器
pub use fifo_processor_task::{FifoProcessorTask, FifoProcessingStats}; // 导出FIFO处理任务
pub use batch_cache_updater::{BatchCacheUpdater, BatchUpdateTask, BatchUpdateStats, ParentUpdateResult}; // 导出批量缓存更新器

// 兼容性别名 - 支持domain_pool和domain_mapping的引用
pub mod domain_pool {
    //! 域名池兼容性模块 - 重定向到统一的domains模块
    pub use super::*;
}

pub mod domain_mapping {
    //! 域名映射兼容性模块 - 重定向到统一的domains模块
    pub use super::*;
}
