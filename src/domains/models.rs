//! 域名池管理 - MongoDB数据模型

use chrono::{DateTime, Utc};
use mongodb::bson::oid::ObjectId;
use serde::{Deserialize, Serialize};

/// 下游域名池 (MongoDB Collection: downstream_pool)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownstreamPool {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub domain: String,
    pub added_at: DateTime<Utc>,
    pub status: PoolStatus,
    pub priority: i32, // FIFO排序
    pub created_by: Option<String>,
    pub notes: Option<String>,
}

/// 上游域名池 (MongoDB Collection: upstream_pool)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpstreamPool {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub domain: String,
    pub added_at: DateTime<Utc>,
    pub status: PoolStatus,
    pub priority: i32,     // FIFO排序
    pub tags: Vec<String>, // 标签数组
    pub created_by: Option<String>,
    pub notes: Option<String>,
}

/// 代理映射关系 (MongoDB Collection: proxy_mappings)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyMapping {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub downstream_domain: String,
    pub upstream_domain: String,
    pub created_at: DateTime<Utc>,
    pub last_used: Option<DateTime<Utc>>,
    pub status: MappingStatus,
    pub request_count: i64,
    pub success_count: i64,
    pub error_count: i64,
    pub average_response_time: Option<i32>,
    pub last_error: Option<String>,
    pub is_manual: bool,           // 区分手动创建和自动配对
    pub ssl_enabled: Option<bool>, // SSL启用状态
    pub created_by: Option<String>,
}

/// 递归链路记录 (MongoDB Collection: recursive_chains)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveChain {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub mapping_id: ObjectId,
    pub original_upstream: String,
    pub extracted_upstream: String,
    pub extraction_rule: String,
    pub extracted_at: DateTime<Utc>,
    pub chain_depth: i32,
    pub is_active: bool,
    pub response_data: Option<String>, // 响应内容摘要
}

/// 配对配置 (MongoDB Collection: pairing_configs)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PairingConfig {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub max_batch_size: u32,
    pub auto_pairing_enabled: bool,
    pub health_check_enabled: bool,
    pub health_check_interval: u32, // 秒
    pub max_retry_attempts: u32,
    pub timeout_seconds: u32,
    pub updated_at: DateTime<Utc>,
    pub updated_by: Option<String>,
}

/// 域名池状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum PoolStatus {
    Available, // 可用
    Used,      // 已使用
    Reserved,  // 预留
    Disabled,  // 禁用
}

/// 映射状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum MappingStatus {
    Active,   // 活跃
    Inactive, // 非活跃
    Failed,   // 失败
    Paused,   // 暂停
}

/// API请求结构体
/// 批量添加请求
#[derive(Debug, Deserialize)]
pub struct BatchAddRequest {
    pub domains: Vec<String>,
    pub domain_type: DomainType,
    pub tags: Option<Vec<String>>, // 仅上游域名使用
}

/// 域名类型
#[derive(Debug, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum DomainType {
    Downstream,
    Upstream,
}

/// 手动创建映射请求
#[derive(Debug, Deserialize)]
pub struct ManualMappingRequest {
    pub downstream_domain: String,
    pub upstream_domain: String,
}

/// 触发配对请求
#[derive(Debug, Deserialize)]
pub struct TriggerPairingRequest {
    pub max_batch_size: Option<u32>,
}

/// 域名池统计信息
#[derive(Debug, Serialize)]
pub struct PoolStats {
    pub downstream_available: i64,
    pub downstream_used: i64,
    pub upstream_available: i64,
    pub upstream_used: i64,
    pub active_mappings: i64,
    pub failed_mappings: i64,
    pub total_requests: i64,
    pub success_rate: f64,
}

/// 配对结果
#[derive(Debug, Serialize)]
pub struct PairingResult {
    pub success_count: u32,
    pub failed_count: u32,
    pub paired_mappings: Vec<ProxyMapping>,
    pub errors: Vec<String>,
}

// 实现方法

impl DownstreamPool {
    pub fn new(domain: String, priority: i32) -> Self {
        Self {
            id: None,
            domain,
            added_at: Utc::now(),
            status: PoolStatus::Available,
            priority,
            created_by: None,
            notes: None,
        }
    }
}

impl UpstreamPool {
    pub fn new(domain: String, priority: i32) -> Self {
        Self {
            id: None,
            domain,
            added_at: Utc::now(),
            status: PoolStatus::Available,
            priority,
            tags: Vec::new(),
            created_by: None,
            notes: None,
        }
    }

    pub fn with_tags(mut self, tags: Vec<String>) -> Self {
        self.tags = tags;
        self
    }
}

impl ProxyMapping {
    pub fn new(downstream: String, upstream: String, is_manual: bool) -> Self {
        Self {
            id: None,
            downstream_domain: downstream,
            upstream_domain: upstream,
            created_at: Utc::now(),
            last_used: None,
            status: MappingStatus::Active,
            request_count: 0,
            success_count: 0,
            error_count: 0,
            average_response_time: None,
            last_error: None,
            is_manual,
            ssl_enabled: Some(false), // 默认不启用SSL
            created_by: None,
        }
    }

    pub fn update_stats(&mut self, success: bool, response_time: u32, error: Option<String>) {
        self.request_count += 1;
        self.last_used = Some(Utc::now());

        if success {
            self.success_count += 1;
        } else {
            self.error_count += 1;
            self.last_error = error;
        }

        // 更新平均响应时间
        if let Some(avg) = self.average_response_time {
            self.average_response_time = Some((avg + response_time as i32) / 2);
        } else {
            self.average_response_time = Some(response_time as i32);
        }
    }

    pub fn success_rate(&self) -> f64 {
        if self.request_count == 0 {
            0.0
        } else {
            self.success_count as f64 / self.request_count as f64
        }
    }
}

impl Default for PairingConfig {
    fn default() -> Self {
        Self {
            id: None,
            max_batch_size: 100,
            auto_pairing_enabled: true,
            health_check_enabled: true,
            health_check_interval: 300, // 5分钟
            max_retry_attempts: 3,
            timeout_seconds: 30,
            updated_at: Utc::now(),
            updated_by: None,
        }
    }
}

/// 临时URL存储 (MongoDB Collection: temp_discovered_urls)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TempDiscoveredUrl {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub url: String,
    pub discovered_from: Option<String>, // 从哪个页面发现的
    pub created_at: DateTime<Utc>,
    pub status: TempUrlStatus,
    pub processing_attempts: i32, // 处理尝试次数
    pub last_attempt_at: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
    pub expires_at: DateTime<Utc>, // 过期时间
}

/// 临时URL状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum TempUrlStatus {
    Pending,    // 待处理
    Processing, // 处理中
    Completed,  // 已完成
    Failed,     // 处理失败
    Expired,    // 已过期
}

impl TempDiscoveredUrl {
    /// 创建新的临时URL记录
    pub fn new(url: String, discovered_from: Option<String>) -> Self {
        let now = Utc::now();
        Self {
            id: None,
            url,
            discovered_from,
            created_at: now,
            status: TempUrlStatus::Pending,
            processing_attempts: 0,
            last_attempt_at: None,
            error_message: None,
            expires_at: now + chrono::Duration::hours(24), // 24小时后过期
        }
    }

    /// 标记为处理中
    pub fn mark_processing(&mut self) {
        self.status = TempUrlStatus::Processing;
        self.processing_attempts += 1;
        self.last_attempt_at = Some(Utc::now());
    }

    /// 标记为已完成
    pub fn mark_completed(&mut self) {
        self.status = TempUrlStatus::Completed;
    }

    /// 标记为失败
    pub fn mark_failed(&mut self, error: String) {
        self.status = TempUrlStatus::Failed;
        self.error_message = Some(error);
    }

    /// 检查是否过期
    pub fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at
    }

    /// 检查是否可以重试
    pub fn can_retry(&self) -> bool {
        self.processing_attempts < 3 && !self.is_expired()
    }
}

/// 下游域名配额管理 (MongoDB Collection: downstream_quotas)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownstreamQuota {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    /// 下游域名
    pub domain: String,
    /// 已使用的子域名数量
    pub used_count: u32,
    /// 最大配额（默认100）
    pub max_quota: u32,
    /// 已使用的子域名列表
    pub used_subdomains: Vec<String>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后更新时间
    pub updated_at: DateTime<Utc>,
    /// 是否启用
    pub enabled: bool,
    /// 配额使用率
    pub usage_rate: f64,
}

impl DownstreamQuota {
    /// 创建新的配额记录
    pub fn new(domain: String, max_quota: Option<u32>) -> Self {
        let now = Utc::now();
        let quota = max_quota.unwrap_or(100); // 默认100个子域名

        Self {
            id: None,
            domain,
            used_count: 0,
            max_quota: quota,
            used_subdomains: Vec::new(),
            created_at: now,
            updated_at: now,
            enabled: true,
            usage_rate: 0.0,
        }
    }

    /// 添加子域名
    pub fn add_subdomain(&mut self, subdomain: String) -> bool {
        if self.used_count >= self.max_quota {
            return false;
        }

        if !self.used_subdomains.contains(&subdomain) {
            self.used_subdomains.push(subdomain);
            self.used_count = self.used_subdomains.len() as u32;
            self.usage_rate = self.used_count as f64 / self.max_quota as f64;
            self.updated_at = Utc::now();
            true
        } else {
            false
        }
    }

    /// 移除子域名
    pub fn remove_subdomain(&mut self, subdomain: &str) -> bool {
        if let Some(pos) = self.used_subdomains.iter().position(|x| x == subdomain) {
            self.used_subdomains.remove(pos);
            self.used_count = self.used_subdomains.len() as u32;
            self.usage_rate = self.used_count as f64 / self.max_quota as f64;
            self.updated_at = Utc::now();
            true
        } else {
            false
        }
    }

    /// 检查是否有可用配额
    pub fn has_available_quota(&self) -> bool {
        self.enabled && self.used_count < self.max_quota
    }

    /// 获取剩余配额
    pub fn remaining_quota(&self) -> u32 {
        if self.used_count >= self.max_quota {
            0
        } else {
            self.max_quota - self.used_count
        }
    }

    /// 检查子域名是否已使用
    pub fn is_subdomain_used(&self, subdomain: &str) -> bool {
        self.used_subdomains.contains(&subdomain.to_string())
    }

    /// 更新配额限制
    pub fn update_quota(&mut self, new_quota: u32) {
        self.max_quota = new_quota;
        self.usage_rate = self.used_count as f64 / self.max_quota as f64;
        self.updated_at = Utc::now();
    }

    /// 获取配额统计信息
    pub fn get_stats(&self) -> QuotaStats {
        QuotaStats {
            domain: self.domain.clone(),
            used_count: self.used_count,
            max_quota: self.max_quota,
            remaining_quota: self.remaining_quota(),
            usage_rate: self.usage_rate,
            enabled: self.enabled,
            last_updated: self.updated_at,
        }
    }
}

/// 配额统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuotaStats {
    pub domain: String,
    pub used_count: u32,
    pub max_quota: u32,
    pub remaining_quota: u32,
    pub usage_rate: f64,
    pub enabled: bool,
    pub last_updated: DateTime<Utc>,
}

/// 全局配额配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalQuotaConfig {
    /// 默认配额数量
    pub default_quota: u32,
    /// 是否启用配额管理
    pub enabled: bool,
    /// 配额警告阈值（百分比）
    pub warning_threshold: f64,
    /// 自动切换是否启用
    pub auto_switch_enabled: bool,
    /// 最后更新时间
    pub updated_at: DateTime<Utc>,
}

impl Default for GlobalQuotaConfig {
    fn default() -> Self {
        Self {
            default_quota: 100,
            enabled: true,
            warning_threshold: 0.8, // 80%时警告
            auto_switch_enabled: true,
            updated_at: Utc::now(),
        }
    }
}

/// 上游域名队列项 (MongoDB Collection: upstream_queue)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpstreamQueueItem {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    /// 上游域名
    pub domain: String,
    /// 队列位置（用于FIFO排序）
    pub queue_position: u64,
    /// 处理状态
    pub status: UpstreamProcessStatus,
    /// 优先级（数字越小优先级越高）
    pub priority: u32,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后处理时间
    pub last_processed_at: Option<DateTime<Utc>>,
    /// 处理尝试次数
    pub process_attempts: u32,
    /// 最大重试次数
    pub max_retries: u32,
    /// 错误信息
    pub error_message: Option<String>,
    /// 处理结果
    pub process_result: Option<String>,
    /// 是否启用
    pub enabled: bool,
}

/// 上游域名处理状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum UpstreamProcessStatus {
    Pending,    // 待处理
    Processing, // 处理中
    Completed,  // 已完成
    Failed,     // 处理失败
    Skipped,    // 已跳过
    Retrying,   // 重试中
}

impl UpstreamQueueItem {
    /// 创建新的队列项
    pub fn new(domain: String, priority: Option<u32>) -> Self {
        Self {
            id: None,
            domain,
            queue_position: 0, // 将在入队时设置
            status: UpstreamProcessStatus::Pending,
            priority: priority.unwrap_or(100), // 默认优先级100
            created_at: Utc::now(),
            last_processed_at: None,
            process_attempts: 0,
            max_retries: 3,
            error_message: None,
            process_result: None,
            enabled: true,
        }
    }

    /// 标记为处理中
    pub fn mark_processing(&mut self) {
        self.status = UpstreamProcessStatus::Processing;
        self.process_attempts += 1;
        self.last_processed_at = Some(Utc::now());
    }

    /// 标记为已完成
    pub fn mark_completed(&mut self, result: Option<String>) {
        self.status = UpstreamProcessStatus::Completed;
        self.process_result = result;
        self.last_processed_at = Some(Utc::now());
    }

    /// 标记为失败
    pub fn mark_failed(&mut self, error: String) {
        self.status = UpstreamProcessStatus::Failed;
        self.error_message = Some(error);
        self.last_processed_at = Some(Utc::now());
    }

    /// 标记为重试
    pub fn mark_retrying(&mut self) {
        self.status = UpstreamProcessStatus::Retrying;
        self.last_processed_at = Some(Utc::now());
    }

    /// 检查是否可以重试
    pub fn can_retry(&self) -> bool {
        self.enabled &&
        self.process_attempts < self.max_retries &&
        matches!(self.status, UpstreamProcessStatus::Failed | UpstreamProcessStatus::Retrying)
    }

    /// 检查是否需要处理
    pub fn needs_processing(&self) -> bool {
        self.enabled &&
        matches!(self.status, UpstreamProcessStatus::Pending | UpstreamProcessStatus::Retrying)
    }

    /// 获取处理统计信息
    pub fn get_stats(&self) -> UpstreamQueueStats {
        UpstreamQueueStats {
            domain: self.domain.clone(),
            status: self.status.clone(),
            priority: self.priority,
            queue_position: self.queue_position,
            process_attempts: self.process_attempts,
            max_retries: self.max_retries,
            created_at: self.created_at,
            last_processed_at: self.last_processed_at,
            enabled: self.enabled,
        }
    }
}

/// 上游队列统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpstreamQueueStats {
    pub domain: String,
    pub status: UpstreamProcessStatus,
    pub priority: u32,
    pub queue_position: u64,
    pub process_attempts: u32,
    pub max_retries: u32,
    pub created_at: DateTime<Utc>,
    pub last_processed_at: Option<DateTime<Utc>>,
    pub enabled: bool,
}

/// FIFO队列配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FifoQueueConfig {
    /// 是否启用FIFO处理
    pub enabled: bool,
    /// 批处理大小
    pub batch_size: u32,
    /// 处理间隔（秒）
    pub process_interval_seconds: u64,
    /// 最大队列长度
    pub max_queue_length: u32,
    /// 默认重试次数
    pub default_max_retries: u32,
    /// 处理超时时间（秒）
    pub process_timeout_seconds: u64,
    /// 最后更新时间
    pub updated_at: DateTime<Utc>,
}

impl Default for FifoQueueConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            batch_size: 5,
            process_interval_seconds: 60, // 1分钟
            max_queue_length: 1000,
            default_max_retries: 3,
            process_timeout_seconds: 300, // 5分钟
            updated_at: Utc::now(),
        }
    }
}
