use anyhow::Result;
use regex::{Captures, Regex};
/// 内容替换器 - 将响应内容中的上游域名替换为下游域名
use std::collections::HashMap;

/// 内容替换器
pub struct ContentReplacer {
    /// 域名映射缓存：上游域名 -> 下游域名
    domain_mappings: HashMap<String, String>,
    /// URL替换正则表达式
    url_regex: Regex,
    /// 域名替换正则表达式
    domain_regex: Regex,
    /// Base64 URL替换正则表达式
    base64_regex: Regex,
}

impl ContentReplacer {
    /// 创建新的内容替换器
    pub fn new(domain_mappings: HashMap<String, String>) -> Result<Self> {
        // 创建匹配所有映射域名的正则表达式
        let domains: Vec<String> = domain_mappings.keys().map(|d| regex::escape(d)).collect();

        let domain_pattern = domains.join("|");

        // URL替换正则（匹配完整URL）
        let url_pattern = format!(r#"(https?://)({})(/[^"\s]*)?(["\s>]|$)"#, domain_pattern);
        let url_regex = Regex::new(&url_pattern)?;

        // 域名替换正则（匹配独立域名）
        let domain_pattern = format!(r#"\b({})\b"#, domain_pattern);
        let domain_regex = Regex::new(&domain_pattern)?;

        // Base64 URL替换正则
        let base64_regex = Regex::new(r#"[A-Za-z0-9+/]{20,}={0,2}"#)?;

        Ok(Self {
            domain_mappings,
            url_regex,
            domain_regex,
            base64_regex,
        })
    }

    /// 更新域名映射
    pub fn update_mappings(&mut self, new_mappings: HashMap<String, String>) -> Result<()> {
        self.domain_mappings = new_mappings;

        // 重新构建正则表达式
        let domains: Vec<String> = self
            .domain_mappings
            .keys()
            .map(|d| regex::escape(d))
            .collect();

        if !domains.is_empty() {
            let domain_pattern = domains.join("|");

            let url_pattern = format!(r#"(https?://)({})(/[^"\s]*)?(["\s>]|$)"#, domain_pattern);
            self.url_regex = Regex::new(&url_pattern)?;

            let domain_pattern = format!(r#"\b({})\b"#, domain_pattern);
            self.domain_regex = Regex::new(&domain_pattern)?;
        }

        Ok(())
    }

    /// 替换HTML内容中的域名
    pub fn replace_html(&self, html: &str) -> Result<String> {
        if self.domain_mappings.is_empty() {
            return Ok(html.to_string());
        }

        let mut result = html.to_string();

        // 替换完整URL
        result = self
            .url_regex
            .replace_all(&result, |caps: &Captures| {
                let protocol = caps.get(1).map_or("", |m| m.as_str());
                let domain = caps.get(2).map_or("", |m| m.as_str());
                let path = caps.get(3).map_or("", |m| m.as_str());
                let suffix = caps.get(4).map_or("", |m| m.as_str());

                if let Some(downstream) = self.domain_mappings.get(domain) {
                    format!("{}{}{}{}", protocol, downstream, path, suffix)
                } else {
                    caps.get(0).unwrap().as_str().to_string()
                }
            })
            .to_string();

        // 处理Base64编码的URL
        result = self.replace_base64_urls(&result)?;

        // 替换独立域名
        result = self
            .domain_regex
            .replace_all(&result, |caps: &Captures| {
                let domain = caps.get(1).map_or("", |m| m.as_str());
                self.domain_mappings
                    .get(domain)
                    .cloned()
                    .unwrap_or_else(|| domain.to_string())
            })
            .to_string();

        Ok(result)
    }

    /// 替换CSS内容中的域名
    pub fn replace_css(&self, css: &str) -> Result<String> {
        if self.domain_mappings.is_empty() {
            return Ok(css.to_string());
        }

        let mut result = css.to_string();

        // CSS特殊处理：url()函数
        let css_url_regex = Regex::new(r#"url\s*\(\s*(['""]?)([^'")]+)\1\s*\)"#)?;
        result = css_url_regex
            .replace_all(&result, |caps: &Captures| {
                let quote = caps.get(1).map_or("", |m| m.as_str());
                let url = caps.get(2).map_or("", |m| m.as_str());

                let replaced_url = self.replace_url_string(url);
                format!("url({}{}{})", quote, replaced_url, quote)
            })
            .to_string();

        // 替换其他域名引用
        result = self.replace_text(&result)?;

        Ok(result)
    }

    /// 替换JavaScript内容中的域名
    pub fn replace_javascript(&self, js: &str) -> Result<String> {
        if self.domain_mappings.is_empty() {
            return Ok(js.to_string());
        }

        let mut result = js.to_string();

        // JavaScript字符串中的URL
        let js_string_regex = Regex::new(r#"(['"`])([^'"`]*(?:https?://[^'"`]*)?)\1"#)?;
        result = js_string_regex
            .replace_all(&result, |caps: &Captures| {
                let quote = caps.get(1).map_or("", |m| m.as_str());
                let content = caps.get(2).map_or("", |m| m.as_str());

                let replaced_content = self.replace_url_string(content);
                format!("{}{}{}", quote, replaced_content, quote)
            })
            .to_string();

        // 替换其他域名引用
        result = self.replace_text(&result)?;

        Ok(result)
    }

    /// 替换JSON内容中的域名
    pub fn replace_json(&self, json: &str) -> Result<String> {
        if self.domain_mappings.is_empty() {
            return Ok(json.to_string());
        }

        // 尝试解析和重构JSON
        if let Ok(mut value) = serde_json::from_str::<serde_json::Value>(json) {
            self.replace_json_value(&mut value);
            Ok(serde_json::to_string(&value)?)
        } else {
            // 如果JSON解析失败，使用正则表达式替换
            self.replace_text(json)
        }
    }

    /// 替换普通文本中的域名
    pub fn replace_text(&self, text: &str) -> Result<String> {
        if self.domain_mappings.is_empty() {
            return Ok(text.to_string());
        }

        let mut result = text.to_string();

        // 替换完整URL
        result = self
            .url_regex
            .replace_all(&result, |caps: &Captures| {
                let protocol = caps.get(1).map_or("", |m| m.as_str());
                let domain = caps.get(2).map_or("", |m| m.as_str());
                let path = caps.get(3).map_or("", |m| m.as_str());
                let suffix = caps.get(4).map_or("", |m| m.as_str());

                if let Some(downstream) = self.domain_mappings.get(domain) {
                    format!("{}{}{}{}", protocol, downstream, path, suffix)
                } else {
                    caps.get(0).unwrap().as_str().to_string()
                }
            })
            .to_string();

        // 处理Base64编码的URL
        result = self.replace_base64_urls(&result)?;

        // 替换独立域名
        result = self
            .domain_regex
            .replace_all(&result, |caps: &Captures| {
                let domain = caps.get(1).map_or("", |m| m.as_str());
                self.domain_mappings
                    .get(domain)
                    .cloned()
                    .unwrap_or_else(|| domain.to_string())
            })
            .to_string();

        Ok(result)
    }

    /// 根据内容类型选择替换方法
    pub fn replace_by_content_type(&self, content: &str, content_type: &str) -> Result<String> {
        let content_type = content_type.to_lowercase();

        if content_type.contains("text/html") {
            self.replace_html(content)
        } else if content_type.contains("text/css") {
            self.replace_css(content)
        } else if content_type.contains("application/javascript")
            || content_type.contains("text/javascript")
        {
            self.replace_javascript(content)
        } else if content_type.contains("application/json") {
            self.replace_json(content)
        } else if content_type.starts_with("text/") {
            self.replace_text(content)
        } else {
            // 二进制内容不处理
            Ok(content.to_string())
        }
    }

    /// 获取当前映射数量
    pub fn mapping_count(&self) -> usize {
        self.domain_mappings.len()
    }

    /// 替换Base64编码的URL
    fn replace_base64_urls(&self, text: &str) -> Result<String> {
        let mut result = text.to_string();

        result = self
            .base64_regex
            .replace_all(&result, |caps: &Captures| {
                let base64_str = caps.get(0).unwrap().as_str();

                // 尝试解码Base64
                if let Ok(decoded) = base64::decode(base64_str) {
                    if let Ok(decoded_str) = String::from_utf8(decoded) {
                        // 替换解码后字符串中的域名
                        if let Ok(replaced) = self.replace_text(&decoded_str) {
                            // 重新编码
                            if let Ok(reencoded) = base64::encode(&replaced).parse::<String>() {
                                return reencoded;
                            }
                        }
                    }
                }

                // 如果处理失败，返回原始字符串
                base64_str.to_string()
            })
            .to_string();

        Ok(result)
    }

    /// 替换URL字符串中的域名
    fn replace_url_string(&self, url: &str) -> String {
        for (upstream, downstream) in &self.domain_mappings {
            if url.contains(upstream) {
                return url.replace(upstream, downstream);
            }
        }
        url.to_string()
    }

    /// 递归替换JSON值中的域名
    fn replace_json_value(&self, value: &mut serde_json::Value) {
        match value {
            serde_json::Value::String(s) => {
                if let Ok(replaced) = self.replace_text(s) {
                    *s = replaced;
                }
            }
            serde_json::Value::Array(arr) => {
                for item in arr {
                    self.replace_json_value(item);
                }
            }
            serde_json::Value::Object(obj) => {
                for (_, val) in obj {
                    self.replace_json_value(val);
                }
            }
            _ => {}
        }
    }

    /// 获取域名映射的副本
    pub fn get_mappings(&self) -> HashMap<String, String> {
        self.domain_mappings.clone()
    }

    /// 检查是否包含特定映射
    pub fn has_mapping(&self, upstream: &str) -> bool {
        self.domain_mappings.contains_key(upstream)
    }

    /// 获取特定上游域名的下游映射
    pub fn get_downstream(&self, upstream: &str) -> Option<&String> {
        self.domain_mappings.get(upstream)
    }
}
