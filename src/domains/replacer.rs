use anyhow::Result;
use regex::{Captures, Regex};
/// 内容替换器 - 将响应内容中的上游域名替换为下游域名
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;
use tracing::{debug, warn};

/// 内容替换器
pub struct ContentReplacer {
    /// 域名映射缓存：上游域名 -> 下游域名
    domain_mappings: HashMap<String, String>,
    /// URL替换正则表达式
    url_regex: Regex,
    /// 域名替换正则表达式
    domain_regex: Regex,
    /// Base64 URL替换正则表达式
    base64_regex: Regex,
    /// 相对路径处理正则表达式
    relative_path_regex: Regex,
    /// CSS @import 正则表达式
    css_import_regex: Regex,
    /// JavaScript模块导入正则表达式
    js_import_regex: Regex,
    /// HTML属性URL正则表达式
    html_attr_regex: Regex,
}

impl ContentReplacer {
    /// 创建新的内容替换器
    pub fn new(domain_mappings: HashMap<String, String>) -> Result<Self> {
        // 创建匹配所有映射域名的正则表达式
        let domains: Vec<String> = domain_mappings.keys().map(|d| regex::escape(d)).collect();

        let domain_pattern = domains.join("|");

        // URL替换正则（匹配完整URL）
        let url_pattern = format!(r#"(https?://)({})(/[^"\s]*)?(["\s>]|$)"#, domain_pattern);
        let url_regex = Regex::new(&url_pattern)?;

        // 域名替换正则（匹配独立域名）
        let domain_pattern = format!(r#"\b({})\b"#, domain_pattern);
        let domain_regex = Regex::new(&domain_pattern)?;

        // Base64 URL替换正则
        let base64_regex = Regex::new(r#"[A-Za-z0-9+/]{20,}={0,2}"#)?;

        // 相对路径处理正则（匹配 ./path 或 ../path 或 /path）
        let relative_path_regex = Regex::new(r#"(\.{0,2}/[^"\s']*)"#)?;

        // CSS @import 正则
        let css_import_regex = Regex::new(r#"@import\s+(?:url\s*\(\s*)?(['""]?)([^'")]+)\1(?:\s*\))?"#)?;

        // JavaScript模块导入正则
        let js_import_regex = Regex::new(r#"(?:import|from)\s+(['""])([^'"]+)\1"#)?;

        // HTML属性URL正则（src, href, action等）
        let html_attr_regex = Regex::new(r#"(?i)(src|href|action|data-[^=]*|style)\s*=\s*(['""])([^'"]*)\2"#)?;

        Ok(Self {
            domain_mappings,
            url_regex,
            domain_regex,
            base64_regex,
            relative_path_regex,
            css_import_regex,
            js_import_regex,
            html_attr_regex,
        })
    }

    /// 更新域名映射
    pub fn update_mappings(&mut self, new_mappings: HashMap<String, String>) -> Result<()> {
        self.domain_mappings = new_mappings;

        // 重新构建正则表达式
        let domains: Vec<String> = self
            .domain_mappings
            .keys()
            .map(|d| regex::escape(d))
            .collect();

        if !domains.is_empty() {
            let domain_pattern = domains.join("|");

            let url_pattern = format!(r#"(https?://)({})(/[^"\s]*)?(["\s>]|$)"#, domain_pattern);
            self.url_regex = Regex::new(&url_pattern)?;

            let domain_pattern = format!(r#"\b({})\b"#, domain_pattern);
            self.domain_regex = Regex::new(&domain_pattern)?;
        }

        // 重新构建其他正则表达式（这些不依赖于域名）
        self.base64_regex = Regex::new(r#"[A-Za-z0-9+/]{20,}={0,2}"#)?;
        self.relative_path_regex = Regex::new(r#"(\.{0,2}/[^"\s']*)"#)?;
        self.css_import_regex = Regex::new(r#"@import\s+(?:url\s*\(\s*)?(['""]?)([^'")]+)\1(?:\s*\))?"#)?;
        self.js_import_regex = Regex::new(r#"(?:import|from)\s+(['""])([^'"]+)\1"#)?;
        self.html_attr_regex = Regex::new(r#"(?i)(src|href|action|data-[^=]*|style)\s*=\s*(['""])([^'"]*)\2"#)?;

        Ok(())
    }

    /// 替换HTML内容中的域名（增强版）
    pub fn replace_html(&self, html: &str) -> Result<String> {
        if self.domain_mappings.is_empty() {
            return Ok(html.to_string());
        }

        let mut result = html.to_string();

        // 1. 替换HTML属性中的URL（src, href, action等）
        result = self
            .html_attr_regex
            .replace_all(&result, |caps: &Captures| {
                let attr_name = caps.get(1).map_or("", |m| m.as_str());
                let quote = caps.get(2).map_or("", |m| m.as_str());
                let url = caps.get(3).map_or("", |m| m.as_str());

                let replaced_url = self.replace_url_comprehensive(url);
                format!("{}={}{}{}", attr_name, quote, replaced_url, quote)
            })
            .to_string();

        // 2. 替换完整URL
        result = self
            .url_regex
            .replace_all(&result, |caps: &Captures| {
                let protocol = caps.get(1).map_or("", |m| m.as_str());
                let domain = caps.get(2).map_or("", |m| m.as_str());
                let path = caps.get(3).map_or("", |m| m.as_str());
                let suffix = caps.get(4).map_or("", |m| m.as_str());

                if let Some(downstream) = self.domain_mappings.get(domain) {
                    format!("{}{}{}{}", protocol, downstream, path, suffix)
                } else {
                    caps.get(0).unwrap().as_str().to_string()
                }
            })
            .to_string();

        // 3. 处理内联CSS中的URL
        result = self.replace_inline_css_urls(&result)?;

        // 4. 处理内联JavaScript中的URL
        result = self.replace_inline_js_urls(&result)?;

        // 5. 处理Base64编码的URL
        result = self.replace_base64_urls(&result)?;

        // 6. 替换独立域名
        result = self
            .domain_regex
            .replace_all(&result, |caps: &Captures| {
                let domain = caps.get(1).map_or("", |m| m.as_str());
                self.domain_mappings
                    .get(domain)
                    .cloned()
                    .unwrap_or_else(|| domain.to_string())
            })
            .to_string();

        Ok(result)
    }

    /// 替换CSS内容中的域名（增强版）
    pub fn replace_css(&self, css: &str) -> Result<String> {
        if self.domain_mappings.is_empty() {
            return Ok(css.to_string());
        }

        let mut result = css.to_string();

        // 1. 处理@import语句
        result = self
            .css_import_regex
            .replace_all(&result, |caps: &Captures| {
                let quote = caps.get(1).map_or("", |m| m.as_str());
                let url = caps.get(2).map_or("", |m| m.as_str());

                let replaced_url = self.replace_url_comprehensive(url);
                if quote.is_empty() {
                    format!("@import {}", replaced_url)
                } else {
                    format!("@import {}{}{}", quote, replaced_url, quote)
                }
            })
            .to_string();

        // 2. 处理url()函数
        let css_url_regex = Regex::new(r#"url\s*\(\s*(['""]?)([^'")]+)\1\s*\)"#)?;
        result = css_url_regex
            .replace_all(&result, |caps: &Captures| {
                let quote = caps.get(1).map_or("", |m| m.as_str());
                let url = caps.get(2).map_or("", |m| m.as_str());

                let replaced_url = self.replace_url_comprehensive(url);
                format!("url({}{}{})", quote, replaced_url, quote)
            })
            .to_string();

        // 3. 处理CSS变量中的URL
        let css_var_regex = Regex::new(r#"--[^:]+:\s*(['""]?)([^'";]+)\1"#)?;
        result = css_var_regex
            .replace_all(&result, |caps: &Captures| {
                let quote = caps.get(1).map_or("", |m| m.as_str());
                let value = caps.get(2).map_or("", |m| m.as_str());

                let replaced_value = self.replace_url_comprehensive(value);
                format!("{}:{}{}{}",
                    caps.get(0).unwrap().as_str().split(':').next().unwrap_or(""),
                    quote, replaced_value, quote)
            })
            .to_string();

        // 4. 替换其他域名引用
        result = self.replace_text(&result)?;

        Ok(result)
    }

    /// 替换JavaScript内容中的域名（增强版）
    pub fn replace_javascript(&self, js: &str) -> Result<String> {
        if self.domain_mappings.is_empty() {
            return Ok(js.to_string());
        }

        let mut result = js.to_string();

        // 1. 处理ES6模块导入
        result = self
            .js_import_regex
            .replace_all(&result, |caps: &Captures| {
                let quote = caps.get(1).map_or("", |m| m.as_str());
                let module_path = caps.get(2).map_or("", |m| m.as_str());

                let replaced_path = self.replace_url_comprehensive(module_path);
                format!("{}{}{}", quote, replaced_path, quote)
            })
            .to_string();

        // 2. 处理动态导入
        let dynamic_import_regex = Regex::new(r#"import\s*\(\s*(['""])([^'"]+)\1\s*\)"#)?;
        result = dynamic_import_regex
            .replace_all(&result, |caps: &Captures| {
                let quote = caps.get(1).map_or("", |m| m.as_str());
                let module_path = caps.get(2).map_or("", |m| m.as_str());

                let replaced_path = self.replace_url_comprehensive(module_path);
                format!("import({}{}{})", quote, replaced_path, quote)
            })
            .to_string();

        // 3. 处理fetch、XMLHttpRequest等API调用
        let api_call_regex = Regex::new(r#"(?:fetch|XMLHttpRequest|axios\.(?:get|post|put|delete))\s*\(\s*(['""])([^'"]+)\1"#)?;
        result = api_call_regex
            .replace_all(&result, |caps: &Captures| {
                let quote = caps.get(1).map_or("", |m| m.as_str());
                let url = caps.get(2).map_or("", |m| m.as_str());

                let replaced_url = self.replace_url_comprehensive(url);
                format!("{}({}{}{})",
                    caps.get(0).unwrap().as_str().split('(').next().unwrap_or(""),
                    quote, replaced_url, quote)
            })
            .to_string();

        // 4. 处理字符串中的URL
        let js_string_regex = Regex::new(r#"(['"`])([^'"`]*(?:https?://[^'"`]*)?)\1"#)?;
        result = js_string_regex
            .replace_all(&result, |caps: &Captures| {
                let quote = caps.get(1).map_or("", |m| m.as_str());
                let content = caps.get(2).map_or("", |m| m.as_str());

                let replaced_content = self.replace_url_comprehensive(content);
                format!("{}{}{}", quote, replaced_content, quote)
            })
            .to_string();

        // 5. 替换其他域名引用
        result = self.replace_text(&result)?;

        Ok(result)
    }

    /// 替换JSON内容中的域名
    pub fn replace_json(&self, json: &str) -> Result<String> {
        if self.domain_mappings.is_empty() {
            return Ok(json.to_string());
        }

        // 尝试解析和重构JSON
        if let Ok(mut value) = serde_json::from_str::<serde_json::Value>(json) {
            self.replace_json_value(&mut value);
            Ok(serde_json::to_string(&value)?)
        } else {
            // 如果JSON解析失败，使用正则表达式替换
            self.replace_text(json)
        }
    }

    /// 替换普通文本中的域名
    pub fn replace_text(&self, text: &str) -> Result<String> {
        if self.domain_mappings.is_empty() {
            return Ok(text.to_string());
        }

        let mut result = text.to_string();

        // 替换完整URL
        result = self
            .url_regex
            .replace_all(&result, |caps: &Captures| {
                let protocol = caps.get(1).map_or("", |m| m.as_str());
                let domain = caps.get(2).map_or("", |m| m.as_str());
                let path = caps.get(3).map_or("", |m| m.as_str());
                let suffix = caps.get(4).map_or("", |m| m.as_str());

                if let Some(downstream) = self.domain_mappings.get(domain) {
                    format!("{}{}{}{}", protocol, downstream, path, suffix)
                } else {
                    caps.get(0).unwrap().as_str().to_string()
                }
            })
            .to_string();

        // 处理Base64编码的URL
        result = self.replace_base64_urls(&result)?;

        // 替换独立域名
        result = self
            .domain_regex
            .replace_all(&result, |caps: &Captures| {
                let domain = caps.get(1).map_or("", |m| m.as_str());
                self.domain_mappings
                    .get(domain)
                    .cloned()
                    .unwrap_or_else(|| domain.to_string())
            })
            .to_string();

        Ok(result)
    }

    /// 根据内容类型选择替换方法
    pub fn replace_by_content_type(&self, content: &str, content_type: &str) -> Result<String> {
        let content_type = content_type.to_lowercase();

        if content_type.contains("text/html") {
            self.replace_html(content)
        } else if content_type.contains("text/css") {
            self.replace_css(content)
        } else if content_type.contains("application/javascript")
            || content_type.contains("text/javascript")
        {
            self.replace_javascript(content)
        } else if content_type.contains("application/json") {
            self.replace_json(content)
        } else if content_type.starts_with("text/") {
            self.replace_text(content)
        } else {
            // 二进制内容不处理
            Ok(content.to_string())
        }
    }

    /// 获取当前映射数量
    pub fn mapping_count(&self) -> usize {
        self.domain_mappings.len()
    }

    /// 替换Base64编码的URL
    fn replace_base64_urls(&self, text: &str) -> Result<String> {
        let mut result = text.to_string();

        result = self
            .base64_regex
            .replace_all(&result, |caps: &Captures| {
                let base64_str = caps.get(0).unwrap().as_str();

                // 尝试解码Base64
                if let Ok(decoded) = base64::decode(base64_str) {
                    if let Ok(decoded_str) = String::from_utf8(decoded) {
                        // 替换解码后字符串中的域名
                        if let Ok(replaced) = self.replace_text(&decoded_str) {
                            // 重新编码
                            if let Ok(reencoded) = base64::encode(&replaced).parse::<String>() {
                                return reencoded;
                            }
                        }
                    }
                }

                // 如果处理失败，返回原始字符串
                base64_str.to_string()
            })
            .to_string();

        Ok(result)
    }

    /// 替换URL字符串中的域名
    fn replace_url_string(&self, url: &str) -> String {
        for (upstream, downstream) in &self.domain_mappings {
            if url.contains(upstream) {
                return url.replace(upstream, downstream);
            }
        }
        url.to_string()
    }

    /// 递归替换JSON值中的域名
    fn replace_json_value(&self, value: &mut serde_json::Value) {
        match value {
            serde_json::Value::String(s) => {
                if let Ok(replaced) = self.replace_text(s) {
                    *s = replaced;
                }
            }
            serde_json::Value::Array(arr) => {
                for item in arr {
                    self.replace_json_value(item);
                }
            }
            serde_json::Value::Object(obj) => {
                for (_, val) in obj {
                    self.replace_json_value(val);
                }
            }
            _ => {}
        }
    }

    /// 获取域名映射的副本
    pub fn get_mappings(&self) -> HashMap<String, String> {
        self.domain_mappings.clone()
    }

    /// 检查是否包含特定映射
    pub fn has_mapping(&self, upstream: &str) -> bool {
        self.domain_mappings.contains_key(upstream)
    }

    /// 获取特定上游域名的下游映射
    pub fn get_downstream(&self, upstream: &str) -> Option<&String> {
        self.domain_mappings.get(upstream)
    }

    // ==================== 增强的URL替换方法 ====================

    /// 全面的URL替换（支持相对路径和绝对路径）
    fn replace_url_comprehensive(&self, url: &str) -> String {
        // 如果是相对路径，直接返回
        if url.starts_with("./") || url.starts_with("../") || url.starts_with("/") {
            return url.to_string();
        }

        // 如果是数据URL或其他协议，直接返回
        if url.starts_with("data:") || url.starts_with("blob:") || url.starts_with("javascript:") {
            return url.to_string();
        }

        // 处理绝对URL
        for (upstream, downstream) in &self.domain_mappings {
            if url.contains(upstream) {
                return url.replace(upstream, downstream);
            }
        }

        url.to_string()
    }

    /// 处理内联CSS中的URL
    fn replace_inline_css_urls(&self, html: &str) -> Result<String> {
        let style_regex = Regex::new(r#"(?i)<style[^>]*>(.*?)</style>"#)?;
        let result = style_regex
            .replace_all(html, |caps: &Captures| {
                let style_content = caps.get(1).map_or("", |m| m.as_str());
                match self.replace_css(style_content) {
                    Ok(replaced) => caps.get(0).unwrap().as_str().replace(style_content, &replaced),
                    Err(_) => caps.get(0).unwrap().as_str().to_string(),
                }
            })
            .to_string();
        Ok(result)
    }

    /// 处理内联JavaScript中的URL
    fn replace_inline_js_urls(&self, html: &str) -> Result<String> {
        let script_regex = Regex::new(r#"(?i)<script[^>]*>(.*?)</script>"#)?;
        let result = script_regex
            .replace_all(html, |caps: &Captures| {
                let script_content = caps.get(1).map_or("", |m| m.as_str());
                match self.replace_javascript(script_content) {
                    Ok(replaced) => caps.get(0).unwrap().as_str().replace(script_content, &replaced),
                    Err(_) => caps.get(0).unwrap().as_str().to_string(),
                }
            })
            .to_string();
        Ok(result)
    }

    /// 更新已缓存的内容
    pub async fn update_cached_content(
        &self,
        cache_file_path: &str,
        old_url: &str,
        new_url: &str,
    ) -> Result<bool> {
        let path = Path::new(cache_file_path);

        if !path.exists() {
            debug!("缓存文件不存在: {}", cache_file_path);
            return Ok(false);
        }

        // 读取文件内容
        let content = fs::read_to_string(path).await?;

        // 替换URL
        let updated_content = content.replace(old_url, new_url);

        // 如果内容有变化，写回文件
        if updated_content != content {
            fs::write(path, updated_content).await?;
            debug!("更新缓存文件: {} (替换: {} -> {})", cache_file_path, old_url, new_url);
            return Ok(true);
        }

        Ok(false)
    }

    /// 批量更新缓存目录中的内容
    pub async fn update_cached_directory(
        &self,
        cache_dir_path: &str,
        old_url: &str,
        new_url: &str,
    ) -> Result<u32> {
        let dir_path = Path::new(cache_dir_path);

        if !dir_path.exists() || !dir_path.is_dir() {
            debug!("缓存目录不存在: {}", cache_dir_path);
            return Ok(0);
        }

        let mut updated_count = 0;
        let mut entries = fs::read_dir(dir_path).await?;

        while let Some(entry) = entries.next_entry().await? {
            let file_path = entry.path();

            if file_path.is_file() {
                let file_path_str = file_path.to_string_lossy();

                // 只处理文本文件
                if self.is_text_file(&file_path_str) {
                    if self.update_cached_content(&file_path_str, old_url, new_url).await? {
                        updated_count += 1;
                    }
                }
            }
        }

        debug!("批量更新缓存目录: {} (更新了 {} 个文件)", cache_dir_path, updated_count);
        Ok(updated_count)
    }

    /// 检查是否为文本文件
    fn is_text_file(&self, file_path: &str) -> bool {
        let path = Path::new(file_path);

        if let Some(extension) = path.extension() {
            let ext = extension.to_string_lossy().to_lowercase();
            matches!(ext.as_str(), "html" | "htm" | "css" | "js" | "json" | "xml" | "txt" | "svg")
        } else {
            false
        }
    }

    /// 验证替换结果
    pub fn validate_replacement(&self, original: &str, replaced: &str) -> bool {
        // 基本验证：确保替换后的内容不为空且有变化
        if replaced.is_empty() || replaced == original {
            return false;
        }

        // 验证HTML结构完整性（简单检查）
        if original.contains("<html") && !replaced.contains("<html") {
            warn!("HTML结构可能被破坏");
            return false;
        }

        // 验证CSS语法完整性（简单检查）
        if original.contains("{") && !replaced.contains("{") {
            warn!("CSS语法可能被破坏");
            return false;
        }

        // 验证JavaScript语法完整性（简单检查）
        if original.contains("function") && !replaced.contains("function") {
            warn!("JavaScript语法可能被破坏");
            return false;
        }

        true
    }

    /// 回滚替换（如果验证失败）
    pub fn rollback_replacement(&self, original: &str) -> String {
        warn!("替换验证失败，回滚到原始内容");
        original.to_string()
    }

    /// 获取替换统计信息
    pub fn get_replacement_stats(&self, original: &str, replaced: &str) -> ReplacementStats {
        let original_len = original.len();
        let replaced_len = replaced.len();
        let size_diff = replaced_len as i64 - original_len as i64;

        // 计算替换次数（简单估算）
        let mut replacement_count = 0;
        for upstream in self.domain_mappings.keys() {
            replacement_count += original.matches(upstream).count();
        }

        ReplacementStats {
            original_size: original_len,
            replaced_size: replaced_len,
            size_difference: size_diff,
            replacement_count,
            mappings_used: self.domain_mappings.len(),
        }
    }
}

/// 替换统计信息
#[derive(Debug, Clone)]
pub struct ReplacementStats {
    pub original_size: usize,
    pub replaced_size: usize,
    pub size_difference: i64,
    pub replacement_count: usize,
    pub mappings_used: usize,
}
