//! 临时URL清理定时任务
//!
//! 定期清理过期的临时URL，防止数据库积累过多无用数据

use crate::domains::DomainPoolService;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::interval;
use tracing::{error, info, warn};

/// 临时URL清理任务管理器
pub struct TempUrlCleanupTask {
    domain_service: Arc<DomainPoolService>,
    cleanup_interval: Duration,
    is_running: Arc<tokio::sync::RwLock<bool>>,
}

impl TempUrlCleanupTask {
    /// 创建新的清理任务
    pub fn new(domain_service: Arc<DomainPoolService>) -> Self {
        Self {
            domain_service,
            cleanup_interval: Duration::from_secs(3600), // 默认1小时清理一次
            is_running: Arc::new(tokio::sync::RwLock::new(false)),
        }
    }

    /// 设置清理间隔
    pub fn with_interval(mut self, interval: Duration) -> Self {
        self.cleanup_interval = interval;
        self
    }

    /// 启动清理任务
    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 检查是否已经在运行
        {
            let running = self.is_running.read().await;
            if *running {
                warn!("临时URL清理任务已在运行中");
                return Ok(());
            }
        }

        // 标记为运行中
        {
            let mut running = self.is_running.write().await;
            *running = true;
        }

        let domain_service = self.domain_service.clone();
        let cleanup_interval = self.cleanup_interval;
        let is_running = self.is_running.clone();

        // 启动后台任务
        tokio::spawn(async move {
            info!("临时URL清理任务启动，清理间隔: {:?}", cleanup_interval);
            
            let mut interval_timer = interval(cleanup_interval);
            
            loop {
                interval_timer.tick().await;
                
                // 检查是否应该停止
                {
                    let running = is_running.read().await;
                    if !*running {
                        info!("临时URL清理任务已停止");
                        break;
                    }
                }

                // 执行清理
                match domain_service.cleanup_expired_temp_urls().await {
                    Ok(cleaned_count) => {
                        if cleaned_count > 0 {
                            info!("清理了 {} 个过期临时URL", cleaned_count);
                        }
                    }
                    Err(e) => {
                        error!("清理过期临时URL失败: {}", e);
                    }
                }

                // 获取统计信息
                match domain_service.get_temp_url_stats().await {
                    Ok(stats) => {
                        info!(
                            "临时URL统计 - 总数: {}, 待处理: {}, 处理中: {}, 已完成: {}, 失败: {}",
                            stats.total_count,
                            stats.pending_count,
                            stats.processing_count,
                            stats.completed_count,
                            stats.failed_count
                        );
                    }
                    Err(e) => {
                        warn!("获取临时URL统计失败: {}", e);
                    }
                }
            }
        });

        info!("临时URL清理任务已启动");
        Ok(())
    }

    /// 停止清理任务
    pub async fn stop(&self) {
        let mut running = self.is_running.write().await;
        *running = false;
        info!("临时URL清理任务已标记为停止");
    }

    /// 检查任务是否在运行
    pub async fn is_running(&self) -> bool {
        let running = self.is_running.read().await;
        *running
    }

    /// 手动执行一次清理
    pub async fn cleanup_once(&self) -> Result<u64, Box<dyn std::error::Error + Send + Sync>> {
        info!("手动执行临时URL清理");
        
        let cleaned_count = self.domain_service.cleanup_expired_temp_urls().await?;
        
        info!("手动清理完成，清理了 {} 个过期临时URL", cleaned_count);
        Ok(cleaned_count)
    }
}

impl Drop for TempUrlCleanupTask {
    fn drop(&mut self) {
        // 在析构时尝试停止任务
        let is_running = self.is_running.clone();
        tokio::spawn(async move {
            let mut running = is_running.write().await;
            *running = false;
        });
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[tokio::test]
    async fn test_cleanup_task_lifecycle() {
        // 这里需要一个模拟的DomainPoolService
        // 由于依赖复杂，这个测试在实际环境中运行
        
        // 测试任务的基本生命周期
        // let task = TempUrlCleanupTask::new(mock_service)
        //     .with_interval(Duration::from_secs(1));
        
        // assert!(!task.is_running().await);
        // task.start().await.unwrap();
        // assert!(task.is_running().await);
        // task.stop().await;
        // 等待一段时间确保任务停止
        // tokio::time::sleep(Duration::from_millis(100)).await;
        // assert!(!task.is_running().await);
    }
}
