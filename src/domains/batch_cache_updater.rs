//! 批量缓存更新器 - 精确的父页面缓存批量更新

use crate::domains::models::{ProxyMapping, DiscoveryContext};
use crate::types::{ProxyError, ProxyResult};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{debug, info, warn};

/// 批量更新任务状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BatchUpdateStatus {
    Pending,    // 等待执行
    Running,    // 正在执行
    Completed,  // 已完成
    Failed,     // 失败
    Cancelled,  // 已取消
}

/// 批量更新任务
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchUpdateTask {
    pub task_id: String,
    pub upstream_domain: String,
    pub affected_parent_urls: Vec<String>,
    pub status: BatchUpdateStatus,
    pub created_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub progress: f64, // 0.0 - 1.0
    pub total_parents: u32,
    pub updated_parents: u32,
    pub failed_parents: u32,
    pub error_message: Option<String>,
}

/// 父页面更新结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParentUpdateResult {
    pub parent_url: String,
    pub success: bool,
    pub error_message: Option<String>,
    pub updated_at: DateTime<Utc>,
    pub content_size_before: Option<usize>,
    pub content_size_after: Option<usize>,
    pub replacement_count: u32,
}

/// 批量更新统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchUpdateStats {
    pub total_tasks: u32,
    pub completed_tasks: u32,
    pub failed_tasks: u32,
    pub total_parents_updated: u32,
    pub total_replacements_made: u32,
    pub average_update_time_ms: f64,
}

/// 批量缓存更新器
pub struct BatchCacheUpdater {
    /// 活跃的更新任务
    active_tasks: tokio::sync::RwLock<HashMap<String, BatchUpdateTask>>,
    /// 更新历史（最近100个任务）
    update_history: tokio::sync::RwLock<Vec<BatchUpdateTask>>,
    /// 最大并发更新数
    max_concurrent_updates: usize,
    /// 当前运行的任务数
    running_tasks: tokio::sync::RwLock<usize>,
}

impl BatchCacheUpdater {
    /// 创建新的批量缓存更新器
    pub fn new(max_concurrent_updates: usize) -> Self {
        Self {
            active_tasks: tokio::sync::RwLock::new(HashMap::new()),
            update_history: tokio::sync::RwLock::new(Vec::new()),
            max_concurrent_updates,
            running_tasks: tokio::sync::RwLock::new(0),
        }
    }

    /// 获取受影响的父页面URL列表
    pub async fn get_affected_parent_urls(&self, upstream_domain: &str, mapping: &ProxyMapping) -> Vec<String> {
        let mut parent_urls = mapping.get_all_parent_urls();
        
        // 去重并排序
        parent_urls.sort();
        parent_urls.dedup();
        
        debug!("域名 {} 影响的父页面数量: {}", upstream_domain, parent_urls.len());
        parent_urls
    }

    /// 创建批量更新任务
    pub async fn create_update_task(
        &self,
        upstream_domain: String,
        mapping: &ProxyMapping,
    ) -> ProxyResult<String> {
        let task_id = uuid::Uuid::new_v4().to_string();
        let affected_parent_urls = self.get_affected_parent_urls(&upstream_domain, mapping).await;
        
        let task = BatchUpdateTask {
            task_id: task_id.clone(),
            upstream_domain: upstream_domain.clone(),
            affected_parent_urls: affected_parent_urls.clone(),
            status: BatchUpdateStatus::Pending,
            created_at: Utc::now(),
            started_at: None,
            completed_at: None,
            progress: 0.0,
            total_parents: affected_parent_urls.len() as u32,
            updated_parents: 0,
            failed_parents: 0,
            error_message: None,
        };

        // 添加到活跃任务列表
        let mut active_tasks = self.active_tasks.write().await;
        active_tasks.insert(task_id.clone(), task);

        info!("创建批量更新任务: {} (域名: {}, 影响父页面: {})", 
            task_id, upstream_domain, affected_parent_urls.len());

        Ok(task_id)
    }

    /// 执行批量更新任务
    pub async fn execute_update_task(
        &self,
        task_id: &str,
        cache_manager: &dyn CacheManager,
        content_replacer: &dyn ContentReplacer,
    ) -> ProxyResult<BatchUpdateStats> {
        // 检查并发限制
        {
            let running_count = *self.running_tasks.read().await;
            if running_count >= self.max_concurrent_updates {
                return Err(ProxyError::invalid_operation("达到最大并发更新限制"));
            }
        }

        // 获取任务
        let mut task = {
            let mut active_tasks = self.active_tasks.write().await;
            match active_tasks.get_mut(task_id) {
                Some(task) => {
                    if task.status != BatchUpdateStatus::Pending {
                        return Err(ProxyError::invalid_operation("任务状态不允许执行"));
                    }
                    task.status = BatchUpdateStatus::Running;
                    task.started_at = Some(Utc::now());
                    task.clone()
                }
                None => return Err(ProxyError::invalid_input("任务不存在")),
            }
        };

        // 增加运行任务计数
        {
            let mut running_count = self.running_tasks.write().await;
            *running_count += 1;
        }

        info!("开始执行批量更新任务: {} (域名: {})", task_id, task.upstream_domain);

        let start_time = std::time::Instant::now();
        let mut update_results = Vec::new();

        // 批量更新父页面缓存
        for (index, parent_url) in task.affected_parent_urls.iter().enumerate() {
            let result = self.update_single_parent_cache(
                parent_url,
                &task.upstream_domain,
                cache_manager,
                content_replacer,
            ).await;

            match result {
                Ok(update_result) => {
                    task.updated_parents += 1;
                    update_results.push(update_result);
                }
                Err(e) => {
                    task.failed_parents += 1;
                    let error_result = ParentUpdateResult {
                        parent_url: parent_url.clone(),
                        success: false,
                        error_message: Some(e.to_string()),
                        updated_at: Utc::now(),
                        content_size_before: None,
                        content_size_after: None,
                        replacement_count: 0,
                    };
                    update_results.push(error_result);
                    warn!("更新父页面缓存失败: {} - {}", parent_url, e);
                }
            }

            // 更新进度
            task.progress = (index + 1) as f64 / task.total_parents as f64;
            
            // 每10个更新一次任务状态
            if (index + 1) % 10 == 0 {
                self.update_task_progress(task_id, &task).await?;
            }
        }

        // 完成任务
        task.status = BatchUpdateStatus::Completed;
        task.completed_at = Some(Utc::now());
        task.progress = 1.0;

        // 更新任务状态
        self.update_task_progress(task_id, &task).await?;

        // 减少运行任务计数
        {
            let mut running_count = self.running_tasks.write().await;
            *running_count -= 1;
        }

        // 移动到历史记录
        self.move_task_to_history(task_id).await?;

        let elapsed_time = start_time.elapsed();
        let total_replacements: u32 = update_results.iter().map(|r| r.replacement_count).sum();

        let stats = BatchUpdateStats {
            total_tasks: 1,
            completed_tasks: 1,
            failed_tasks: 0,
            total_parents_updated: task.updated_parents,
            total_replacements_made: total_replacements,
            average_update_time_ms: elapsed_time.as_millis() as f64,
        };

        info!("批量更新任务完成: {} (耗时: {:?}, 更新: {}, 失败: {}, 替换: {})", 
            task_id, elapsed_time, task.updated_parents, task.failed_parents, total_replacements);

        Ok(stats)
    }
}

/// 缓存管理器接口
#[async_trait::async_trait]
pub trait CacheManager {
    async fn get_cached_content(&self, url: &str) -> ProxyResult<Option<Vec<u8>>>;
    async fn update_cached_content(&self, url: &str, content: Vec<u8>) -> ProxyResult<()>;
    async fn invalidate_cache(&self, url: &str) -> ProxyResult<()>;
}

/// 内容替换器接口
#[async_trait::async_trait]
pub trait ContentReplacer {
    async fn replace_domain_in_content(
        &self,
        content: &[u8],
        old_domain: &str,
        new_domain: &str,
        content_type: &str,
    ) -> ProxyResult<(Vec<u8>, u32)>; // (新内容, 替换次数)
}

impl BatchCacheUpdater {
    /// 更新单个父页面缓存
    async fn update_single_parent_cache(
        &self,
        parent_url: &str,
        upstream_domain: &str,
        cache_manager: &dyn CacheManager,
        content_replacer: &dyn ContentReplacer,
    ) -> ProxyResult<ParentUpdateResult> {
        let start_time = std::time::Instant::now();

        // 获取缓存内容
        let cached_content = cache_manager.get_cached_content(parent_url).await?;

        let content = match cached_content {
            Some(content) => content,
            None => {
                return Ok(ParentUpdateResult {
                    parent_url: parent_url.to_string(),
                    success: false,
                    error_message: Some("缓存内容不存在".to_string()),
                    updated_at: Utc::now(),
                    content_size_before: None,
                    content_size_after: None,
                    replacement_count: 0,
                });
            }
        };

        let content_size_before = content.len();

        // 确定内容类型
        let content_type = self.detect_content_type(parent_url, &content);

        // 获取映射关系（这里需要从外部传入或查询）
        // 为了简化，假设我们有一个方法来获取下游域名
        let downstream_domain = self.get_downstream_domain_for_upstream(upstream_domain).await?;

        // 执行内容替换
        let (new_content, replacement_count) = content_replacer.replace_domain_in_content(
            &content,
            upstream_domain,
            &downstream_domain,
            &content_type,
        ).await?;

        let content_size_after = new_content.len();

        // 更新缓存
        if replacement_count > 0 {
            cache_manager.update_cached_content(parent_url, new_content).await?;
            debug!("更新父页面缓存: {} (替换: {} 次)", parent_url, replacement_count);
        }

        Ok(ParentUpdateResult {
            parent_url: parent_url.to_string(),
            success: true,
            error_message: None,
            updated_at: Utc::now(),
            content_size_before: Some(content_size_before),
            content_size_after: Some(content_size_after),
            replacement_count,
        })
    }

    /// 检测内容类型
    fn detect_content_type(&self, url: &str, content: &[u8]) -> String {
        // 基于URL扩展名检测
        if url.ends_with(".html") || url.ends_with(".htm") {
            return "text/html".to_string();
        }
        if url.ends_with(".css") {
            return "text/css".to_string();
        }
        if url.ends_with(".js") {
            return "application/javascript".to_string();
        }
        if url.ends_with(".json") {
            return "application/json".to_string();
        }

        // 基于内容检测
        if let Ok(content_str) = std::str::from_utf8(content) {
            let content_lower = content_str.to_lowercase();
            if content_lower.contains("<!doctype html") || content_lower.contains("<html") {
                return "text/html".to_string();
            }
            if content_lower.contains("@import") || content_lower.contains("background:") {
                return "text/css".to_string();
            }
            if content_lower.contains("function") || content_lower.contains("var ") {
                return "application/javascript".to_string();
            }
        }

        "text/plain".to_string()
    }

    /// 获取上游域名对应的下游域名（需要外部依赖）
    async fn get_downstream_domain_for_upstream(&self, upstream_domain: &str) -> ProxyResult<String> {
        // 这里应该调用repository来获取映射关系
        // 为了编译通过，先返回一个占位符
        // 实际实现中需要注入repository依赖
        Ok(format!("mapped-{}", upstream_domain))
    }

    /// 更新任务进度
    async fn update_task_progress(&self, task_id: &str, task: &BatchUpdateTask) -> ProxyResult<()> {
        let mut active_tasks = self.active_tasks.write().await;
        if let Some(existing_task) = active_tasks.get_mut(task_id) {
            *existing_task = task.clone();
        }
        Ok(())
    }

    /// 将任务移动到历史记录
    async fn move_task_to_history(&self, task_id: &str) -> ProxyResult<()> {
        let task = {
            let mut active_tasks = self.active_tasks.write().await;
            active_tasks.remove(task_id)
        };

        if let Some(task) = task {
            let mut history = self.update_history.write().await;
            history.push(task);

            // 保持历史记录不超过100个
            if history.len() > 100 {
                history.remove(0);
            }
        }

        Ok(())
    }

    /// 获取任务状态
    pub async fn get_task_status(&self, task_id: &str) -> Option<BatchUpdateTask> {
        let active_tasks = self.active_tasks.read().await;
        active_tasks.get(task_id).cloned()
    }

    /// 获取所有活跃任务
    pub async fn get_active_tasks(&self) -> Vec<BatchUpdateTask> {
        let active_tasks = self.active_tasks.read().await;
        active_tasks.values().cloned().collect()
    }

    /// 获取更新历史
    pub async fn get_update_history(&self, limit: Option<usize>) -> Vec<BatchUpdateTask> {
        let history = self.update_history.read().await;
        let limit = limit.unwrap_or(history.len());
        history.iter().rev().take(limit).cloned().collect()
    }

    /// 取消任务
    pub async fn cancel_task(&self, task_id: &str) -> ProxyResult<()> {
        let mut active_tasks = self.active_tasks.write().await;
        if let Some(task) = active_tasks.get_mut(task_id) {
            if task.status == BatchUpdateStatus::Pending {
                task.status = BatchUpdateStatus::Cancelled;
                task.completed_at = Some(Utc::now());
                info!("取消批量更新任务: {}", task_id);
                Ok(())
            } else {
                Err(ProxyError::invalid_operation("只能取消等待中的任务"))
            }
        } else {
            Err(ProxyError::invalid_input("任务不存在"))
        }
    }

    /// 获取批量更新统计
    pub async fn get_batch_update_stats(&self) -> BatchUpdateStats {
        let active_tasks = self.active_tasks.read().await;
        let history = self.update_history.read().await;

        let total_tasks = active_tasks.len() + history.len();
        let completed_tasks = history.iter().filter(|t| t.status == BatchUpdateStatus::Completed).count();
        let failed_tasks = history.iter().filter(|t| t.status == BatchUpdateStatus::Failed).count();

        let total_parents_updated: u32 = history.iter().map(|t| t.updated_parents).sum();
        let total_replacements_made: u32 = 0; // 需要从详细结果中计算

        let total_time_ms: u64 = history.iter()
            .filter_map(|t| {
                if let (Some(start), Some(end)) = (t.started_at, t.completed_at) {
                    Some((end - start).num_milliseconds() as u64)
                } else {
                    None
                }
            })
            .sum();

        let average_update_time_ms = if completed_tasks > 0 {
            total_time_ms as f64 / completed_tasks as f64
        } else {
            0.0
        };

        BatchUpdateStats {
            total_tasks: total_tasks as u32,
            completed_tasks: completed_tasks as u32,
            failed_tasks: failed_tasks as u32,
            total_parents_updated,
            total_replacements_made,
            average_update_time_ms,
        }
    }
}
