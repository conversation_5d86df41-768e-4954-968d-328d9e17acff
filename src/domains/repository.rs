//! 域名池管理 - MongoDB数据访问层

use crate::domains::models::*;
use crate::types::{ProxyError, ProxyResult};
use chrono::Utc;
use futures::TryStreamExt;
use mongodb::bson::{self, doc, oid::ObjectId};
use tracing::{info, warn};

/// 域名池MongoDB仓库
pub struct DomainPoolRepository {
    db: mongodb::Database,
}

impl DomainPoolRepository {
    pub fn new(db: mongodb::Database) -> Self {
        Self { db }
    }

    /// 初始化数据库索引
    pub async fn init_indexes(&self) -> ProxyResult<()> {
        use mongodb::options::IndexOptions;
        use mongodb::IndexModel;

        // 为上游域名创建唯一索引
        let upstream_index = IndexModel::builder()
            .keys(doc! { "domain": 1 })
            .options(IndexOptions::builder().unique(true).build())
            .build();

        // 为下游域名创建唯一索引
        let downstream_index = IndexModel::builder()
            .keys(doc! { "domain": 1 })
            .options(IndexOptions::builder().unique(true).build())
            .build();

        // 创建索引
        self.upstream_collection()
            .create_index(upstream_index, None)
            .await
            .map_err(|e| ProxyError::database(&format!("创建上游域名唯一索引失败: {}", e)))?;

        self.downstream_collection()
            .create_index(downstream_index, None)
            .await
            .map_err(|e| ProxyError::database(&format!("创建下游域名唯一索引失败: {}", e)))?;

        info!("数据库唯一索引创建成功");
        Ok(())
    }

    /// 获取下游域名池集合 - 简化实现
    fn downstream_collection(&self) -> mongodb::Collection<DownstreamPool> {
        self.db.collection::<DownstreamPool>("downstream_pool")
    }

    /// 获取上游域名池集合 - 简化实现
    fn upstream_collection(&self) -> mongodb::Collection<UpstreamPool> {
        self.db.collection::<UpstreamPool>("upstream_pool")
    }

    /// 获取代理映射集合 - 简化实现
    fn mapping_collection(&self) -> mongodb::Collection<ProxyMapping> {
        self.db.collection::<ProxyMapping>("proxy_mappings")
    }

    /// 获取递归链路集合 - 简化实现
    fn chain_collection(&self) -> mongodb::Collection<RecursiveChain> {
        self.db.collection::<RecursiveChain>("recursive_chains")
    }

    /// 获取配对配置集合 - 简化实现
    fn config_collection(&self) -> mongodb::Collection<PairingConfig> {
        self.db.collection::<PairingConfig>("pairing_configs")
    }

    // === 下游域名池操作 ===

    /// 批量添加下游域名
    pub async fn batch_add_downstream(&self, domains: Vec<String>) -> ProxyResult<u32> {
        let collection = self.downstream_collection();

        // 获取当前最大优先级
        let max_priority = self.get_max_downstream_priority().await?;

        let mut docs = Vec::new();
        for (i, domain) in domains.iter().enumerate() {
            // 检查域名是否已存在于下游域名池
            if self.downstream_exists(domain).await? {
                warn!("下游域名已存在于下游池，跳过: {}", domain);
                continue;
            }

            // 检查域名是否已存在于映射表的下游字段
            if self.mapping_downstream_exists(domain).await? {
                warn!("下游域名已存在于映射表，跳过: {}", domain);
                continue;
            }

            let pool_item = DownstreamPool::new(domain.clone(), max_priority + i as i32 + 1);
            docs.push(pool_item);
        }

        if docs.is_empty() {
            return Ok(0);
        }

        // 使用ordered=false允许部分插入成功
        let options = mongodb::options::InsertManyOptions::builder()
            .ordered(false)
            .build();

        match collection.insert_many(docs, options).await {
            Ok(result) => Ok(result.inserted_ids.len() as u32),
            Err(e) => {
                // 检查是否是重复键错误
                if let mongodb::error::ErrorKind::BulkWrite(_) = e.kind.as_ref() {
                    // 如果是批量写入错误，可能包含重复键错误
                    // 由于我们已经在应用层做了重复检测，这里只是额外保障
                    warn!("批量添加下游域名时遇到数据库约束错误，可能有并发重复插入");
                    return Ok(0); // 返回0表示没有新增
                }
                Err(ProxyError::database(&format!(
                    "批量添加下游域名失败: {}",
                    e
                )))
            }
        }
    }

    /// 获取可用下游域名（FIFO顺序）
    pub async fn get_available_downstream(&self, limit: u32) -> ProxyResult<Vec<DownstreamPool>> {
        let collection = self.downstream_collection();

        let filter = doc! { "status": "available" };
        let options = mongodb::options::FindOptions::builder()
            .sort(doc! { "priority": 1 }) // FIFO排序
            .limit(limit as i64)
            .build();

        let cursor = collection
            .find(filter, options)
            .await
            .map_err(|e| ProxyError::database(&format!("查询可用下游域名失败: {}", e)))?;

        let domains: Vec<DownstreamPool> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集下游域名失败: {}", e)))?;

        Ok(domains)
    }

    /// 标记下游域名为已使用
    pub async fn mark_downstream_used(&self, domain: &str) -> ProxyResult<()> {
        let collection = self.downstream_collection();

        let filter = doc! { "domain": domain, "status": "available" };
        let update = doc! { "$set": { "status": "used" } };

        collection
            .update_one(filter, update, None)
            .await
            .map_err(|e| ProxyError::database(&format!("标记下游域名已使用失败: {}", e)))?;

        Ok(())
    }

    // === 上游域名池操作 ===

    /// 批量添加上游域名
    pub async fn batch_add_upstream(
        &self,
        domains: Vec<String>,
        tags: Option<Vec<String>>,
    ) -> ProxyResult<u32> {
        let collection = self.upstream_collection();

        // 获取当前最大优先级
        let max_priority = self.get_max_upstream_priority().await?;

        let mut docs = Vec::new();
        for (i, domain) in domains.iter().enumerate() {
            // 检查域名是否已存在于上游域名池
            if self.upstream_exists(domain).await? {
                warn!("上游域名已存在于上游池，跳过: {}", domain);
                continue;
            }

            // 检查域名是否已存在于映射表的上游字段
            if self.mapping_upstream_exists(domain).await? {
                warn!("上游域名已存在于映射表，跳过: {}", domain);
                continue;
            }

            let mut pool_item = UpstreamPool::new(domain.clone(), max_priority + i as i32 + 1);
            if let Some(ref tag_list) = tags {
                pool_item.tags = tag_list.clone();
            }
            docs.push(pool_item);
        }

        if docs.is_empty() {
            return Ok(0);
        }

        // 使用ordered=false允许部分插入成功
        let options = mongodb::options::InsertManyOptions::builder()
            .ordered(false)
            .build();

        match collection.insert_many(docs, options).await {
            Ok(result) => Ok(result.inserted_ids.len() as u32),
            Err(e) => {
                // 检查是否是重复键错误
                if let mongodb::error::ErrorKind::BulkWrite(_) = e.kind.as_ref() {
                    // 如果是批量写入错误，可能包含重复键错误
                    // 由于我们已经在应用层做了重复检测，这里只是额外保障
                    warn!("批量添加上游域名时遇到数据库约束错误，可能有并发重复插入");
                    return Ok(0); // 返回0表示没有新增
                }
                Err(ProxyError::database(&format!(
                    "批量添加上游域名失败: {}",
                    e
                )))
            }
        }
    }

    /// 获取可用上游域名（FIFO顺序）
    pub async fn get_available_upstream(&self, limit: u32) -> ProxyResult<Vec<UpstreamPool>> {
        let collection = self.upstream_collection();

        let filter = doc! {
            "status": "available",
            "health_status": { "$ne": "unhealthy" }
        };
        let options = mongodb::options::FindOptions::builder()
            .sort(doc! { "priority": 1 }) // FIFO排序
            .limit(limit as i64)
            .build();

        let cursor = collection
            .find(filter, options)
            .await
            .map_err(|e| ProxyError::database(&format!("查询可用上游域名失败: {}", e)))?;

        let domains: Vec<UpstreamPool> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集上游域名失败: {}", e)))?;

        Ok(domains)
    }

    /// 获取所有上游域名
    pub async fn get_all_upstream(&self) -> ProxyResult<Vec<UpstreamPool>> {
        let collection = self.upstream_collection();

        let options = mongodb::options::FindOptions::builder()
            .sort(doc! { "priority": 1 })
            .build();

        let cursor = collection
            .find(doc! {}, options)
            .await
            .map_err(|e| ProxyError::database(&format!("查询所有上游域名失败: {}", e)))?;

        let domains: Vec<UpstreamPool> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集上游域名失败: {}", e)))?;

        Ok(domains)
    }

    /// 获取所有下游域名
    pub async fn get_all_downstream(&self) -> ProxyResult<Vec<DownstreamPool>> {
        let collection = self.downstream_collection();

        let options = mongodb::options::FindOptions::builder()
            .sort(doc! { "priority": 1 })
            .build();

        let cursor = collection
            .find(doc! {}, options)
            .await
            .map_err(|e| ProxyError::database(&format!("查询所有下游域名失败: {}", e)))?;

        let domains: Vec<DownstreamPool> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集下游域名失败: {}", e)))?;

        Ok(domains)
    }

    /// 获取所有已占用的下游域名字符串（用于子域名生成器缓存）
    pub async fn get_all_downstream_domains(&self) -> ProxyResult<Vec<String>> {
        let mappings_collection = self.mapping_collection();

        // 从映射表中获取所有下游域名
        let cursor = mappings_collection
            .find(doc! {}, None)
            .await
            .map_err(|e| ProxyError::database(&format!("查询映射表下游域名失败: {}", e)))?;

        let mappings: Vec<ProxyMapping> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集映射表数据失败: {}", e)))?;

        // 提取所有下游域名
        let downstream_domains: Vec<String> = mappings
            .into_iter()
            .map(|mapping| mapping.downstream_domain)
            .collect();

        Ok(downstream_domains)
    }

    /// 删除上游域名
    pub async fn delete_upstream_domain(&self, domain_id: &str) -> ProxyResult<()> {
        let collection = self.upstream_collection();

        // 尝试按ObjectId删除
        if let Ok(object_id) = mongodb::bson::oid::ObjectId::parse_str(domain_id) {
            let result = collection
                .delete_one(doc! { "_id": object_id }, None)
                .await
                .map_err(|e| ProxyError::database(&format!("删除上游域名失败: {}", e)))?;

            if result.deleted_count > 0 {
                return Ok(());
            }
        }

        // 尝试按域名删除
        let result = collection
            .delete_one(doc! { "domain": domain_id }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("删除上游域名失败: {}", e)))?;

        if result.deleted_count > 0 {
            Ok(())
        } else {
            Err(ProxyError::invalid_input("上游域名不存在"))
        }
    }

    /// 删除下游域名
    pub async fn delete_downstream_domain(&self, domain_id: &str) -> ProxyResult<()> {
        let collection = self.downstream_collection();

        // 尝试按ObjectId删除
        if let Ok(object_id) = mongodb::bson::oid::ObjectId::parse_str(domain_id) {
            let result = collection
                .delete_one(doc! { "_id": object_id }, None)
                .await
                .map_err(|e| ProxyError::database(&format!("删除下游域名失败: {}", e)))?;

            if result.deleted_count > 0 {
                return Ok(());
            }
        }

        // 尝试按域名删除
        let result = collection
            .delete_one(doc! { "domain": domain_id }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("删除下游域名失败: {}", e)))?;

        if result.deleted_count > 0 {
            Ok(())
        } else {
            Err(ProxyError::invalid_input("下游域名不存在"))
        }
    }

    /// 清空上游域名
    pub async fn clear_upstream_domains(&self) -> ProxyResult<u32> {
        let collection = self.upstream_collection();
        let result = collection
            .delete_many(doc! {}, None)
            .await
            .map_err(|e| ProxyError::database(&format!("清空上游域名失败: {}", e)))?;
        Ok(result.deleted_count as u32)
    }

    /// 清空下游域名
    pub async fn clear_downstream_domains(&self) -> ProxyResult<u32> {
        let collection = self.downstream_collection();
        let result = collection
            .delete_many(doc! {}, None)
            .await
            .map_err(|e| ProxyError::database(&format!("清空下游域名失败: {}", e)))?;
        Ok(result.deleted_count as u32)
    }

    // === 代理映射操作 ===

    /// 创建代理映射
    pub async fn create_mapping(
        &self,
        downstream: &str,
        upstream: &str,
        is_manual: bool,
    ) -> ProxyResult<ObjectId> {
        let collection = self.mapping_collection();

        // 按照指定顺序检测重复，发现第一个重复立即返回错误

        // 1. 检测下游域名是否在映射表中重复
        if self.mapping_downstream_exists(downstream).await? {
            return Err(ProxyError::invalid_input(&format!(
                "域名 {} 已在映射表中使用，无法创建映射",
                downstream
            )));
        }

        // 2. 检测下游域名是否在下游域名池中重复
        if self.downstream_exists(downstream).await? {
            return Err(ProxyError::invalid_input(&format!(
                "域名 {} 已在下游池中使用，无法创建映射",
                downstream
            )));
        }

        // 3. 检测上游域名是否在映射表中重复
        if self.mapping_upstream_exists(upstream).await? {
            return Err(ProxyError::invalid_input(&format!(
                "域名 {} 已在映射表中使用，无法创建映射",
                upstream
            )));
        }

        // 4. 检测上游域名是否在上游域名池中重复
        if self.upstream_exists(upstream).await? {
            return Err(ProxyError::invalid_input(&format!(
                "域名 {} 已在上游池中使用，无法创建映射",
                upstream
            )));
        }

        let mapping = ProxyMapping::new(downstream.to_string(), upstream.to_string(), is_manual);

        let result = collection
            .insert_one(&mapping, None)
            .await
            .map_err(|e| ProxyError::database(&format!("创建代理映射失败: {}", e)))?;

        let mapping_id = result
            .inserted_id
            .as_object_id()
            .ok_or_else(|| ProxyError::database("无效的映射ID"))?;

        // 如果是自动配对，标记域名为已使用
        if !is_manual {
            self.mark_downstream_used(downstream).await?;
            self.mark_upstream_used(upstream).await?;
        }

        info!(
            "创建代理映射: {} -> {} ({})",
            downstream,
            upstream,
            if is_manual { "手动" } else { "自动" }
        );

        Ok(mapping_id)
    }

    /// 根据ID获取映射
    pub async fn get_mapping_by_id(&self, mapping_id: &str) -> ProxyResult<ProxyMapping> {
        let collection = self.mapping_collection();

        let object_id = mongodb::bson::oid::ObjectId::parse_str(mapping_id)
            .map_err(|_| ProxyError::invalid_input("无效的映射ID格式"))?;

        let mapping = collection
            .find_one(doc! { "_id": object_id }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("查询映射失败: {}", e)))?
            .ok_or_else(|| ProxyError::invalid_input("映射不存在"))?;

        Ok(mapping)
    }

    /// 更新映射
    pub async fn update_mapping(
        &self,
        mapping_id: &str,
        request: crate::api::auto_proxy::UpdateMappingRequest,
    ) -> ProxyResult<()> {
        let collection = self.mapping_collection();

        let object_id = mongodb::bson::oid::ObjectId::parse_str(mapping_id)
            .map_err(|_| ProxyError::invalid_input("无效的映射ID格式"))?;

        // 构建更新文档
        let mut update_doc = doc! {};

        if let Some(downstream) = request.downstream_domain {
            // 检查新的下游域名是否与其他映射冲突
            if self.mapping_downstream_exists(&downstream).await? {
                let existing = collection
                    .find_one(doc! { "downstream_domain": &downstream }, None)
                    .await
                    .map_err(|e| ProxyError::database(&format!("检查重复映射失败: {}", e)))?;

                if let Some(existing_mapping) = existing {
                    if existing_mapping.id != Some(object_id) {
                        return Err(ProxyError::invalid_input(&format!(
                            "域名 {} 已在映射表中使用，无法更新映射",
                            downstream
                        )));
                    }
                }
            }

            // 检查下游域名是否在下游域名池中
            if self.downstream_exists(&downstream).await? {
                return Err(ProxyError::invalid_input(&format!(
                    "域名 {} 已在下游池中使用，无法更新映射",
                    downstream
                )));
            }

            update_doc.insert("downstream_domain", downstream);
        }

        if let Some(upstream) = request.upstream_domain {
            // 检查新的上游域名是否与其他映射冲突
            if self.mapping_upstream_exists(&upstream).await? {
                let existing = collection
                    .find_one(doc! { "upstream_domain": &upstream }, None)
                    .await
                    .map_err(|e| ProxyError::database(&format!("检查重复映射失败: {}", e)))?;

                if let Some(existing_mapping) = existing {
                    if existing_mapping.id != Some(object_id) {
                        return Err(ProxyError::invalid_input(&format!(
                            "域名 {} 已在映射表中使用，无法更新映射",
                            upstream
                        )));
                    }
                }
            }

            // 检查上游域名是否在上游域名池中
            if self.upstream_exists(&upstream).await? {
                return Err(ProxyError::invalid_input(&format!(
                    "域名 {} 已在上游池中使用，无法更新映射",
                    upstream
                )));
            }

            update_doc.insert("upstream_domain", upstream);
        }

        if let Some(ssl_enabled) = request.ssl_enabled {
            update_doc.insert("ssl_enabled", ssl_enabled);
        }

        if update_doc.is_empty() {
            return Err(ProxyError::invalid_input("没有提供要更新的字段"));
        }

        // 添加更新时间
        update_doc.insert("updated_at", chrono::Utc::now());

        let result = collection
            .update_one(doc! { "_id": object_id }, doc! { "$set": update_doc }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("更新映射失败: {}", e)))?;

        if result.matched_count == 0 {
            return Err(ProxyError::invalid_input("映射不存在"));
        }

        info!("更新代理映射: ID={}", mapping_id);
        Ok(())
    }

    /// 删除映射
    pub async fn delete_mapping(&self, mapping_id: &str) -> ProxyResult<()> {
        let collection = self.mapping_collection();

        let object_id = mongodb::bson::oid::ObjectId::parse_str(mapping_id)
            .map_err(|_| ProxyError::invalid_input("无效的映射ID格式"))?;

        let result = collection
            .delete_one(doc! { "_id": object_id }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("删除映射失败: {}", e)))?;

        if result.deleted_count == 0 {
            return Err(ProxyError::invalid_input("映射不存在"));
        }

        info!("删除代理映射: ID={}", mapping_id);
        Ok(())
    }

    /// 获取活跃代理映射
    pub async fn get_active_mappings(&self) -> ProxyResult<Vec<ProxyMapping>> {
        let collection = self.mapping_collection();

        let filter = doc! { "status": "active" };
        let cursor = collection
            .find(filter, None)
            .await
            .map_err(|e| ProxyError::database(&format!("查询活跃映射失败: {}", e)))?;

        let mappings: Vec<ProxyMapping> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集映射数据失败: {}", e)))?;

        Ok(mappings)
    }

    /// 更新映射统计
    pub async fn update_mapping_stats(
        &self,
        downstream: &str,
        success: bool,
        response_time: u32,
        error: Option<String>,
    ) -> ProxyResult<()> {
        let collection = self.mapping_collection();

        let filter = doc! { "downstream_domain": downstream };

        // 构建更新操作
        let mut update_doc = doc! {
            "$inc": { "request_count": 1 },
            "$set": { "last_used": Utc::now() }
        };

        if success {
            update_doc
                .get_document_mut("$inc")
                .unwrap()
                .insert("success_count", 1);
        } else {
            update_doc
                .get_document_mut("$inc")
                .unwrap()
                .insert("error_count", 1);
            if let Some(err_msg) = error {
                update_doc
                    .get_document_mut("$set")
                    .unwrap()
                    .insert("last_error", err_msg);
            }
        }

        // 更新平均响应时间（简化版本）
        update_doc
            .get_document_mut("$set")
            .unwrap()
            .insert("average_response_time", response_time as i32);

        collection
            .update_one(filter, update_doc, None)
            .await
            .map_err(|e| ProxyError::database(&format!("更新映射统计失败: {}", e)))?;

        Ok(())
    }

    // === 统计信息 ===

    /// 获取域名池统计信息
    pub async fn get_pool_stats(&self) -> ProxyResult<PoolStats> {
        // 下游域名统计
        let downstream_available = self.count_downstream_by_status("available").await?;
        let downstream_used = self.count_downstream_by_status("used").await?;

        // 上游域名统计
        let upstream_available = self.count_upstream_by_status("available").await?;
        let upstream_used = self.count_upstream_by_status("used").await?;

        // 映射统计
        let active_mappings = self.count_mappings_by_status("active").await?;
        let failed_mappings = self.count_mappings_by_status("failed").await?;

        // 请求统计
        let (total_requests, total_success) = self.get_request_stats().await?;
        let success_rate = if total_requests > 0 {
            total_success as f64 / total_requests as f64
        } else {
            0.0
        };

        Ok(PoolStats {
            downstream_available,
            downstream_used,
            upstream_available,
            upstream_used,
            active_mappings,
            failed_mappings,
            total_requests,
            success_rate,
        })
    }

    // === 辅助方法 ===

    async fn downstream_exists(&self, domain: &str) -> ProxyResult<bool> {
        let collection = self.downstream_collection();
        let count = collection
            .count_documents(doc! { "domain": domain }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("检查下游域名存在性失败: {}", e)))?;
        Ok(count > 0)
    }

    async fn upstream_exists(&self, domain: &str) -> ProxyResult<bool> {
        let collection = self.upstream_collection();
        let count = collection
            .count_documents(doc! { "domain": domain }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("检查上游域名存在性失败: {}", e)))?;
        Ok(count > 0)
    }

    async fn mapping_exists(&self, downstream: &str) -> ProxyResult<bool> {
        let collection = self.mapping_collection();
        let count = collection
            .count_documents(doc! { "downstream_domain": downstream }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("检查映射存在性失败: {}", e)))?;
        Ok(count > 0)
    }

    /// 检查映射表中是否存在指定的上游域名
    async fn mapping_upstream_exists(&self, upstream: &str) -> ProxyResult<bool> {
        let collection = self.mapping_collection();
        let count = collection
            .count_documents(doc! { "upstream_domain": upstream }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("检查映射表上游域名存在性失败: {}", e)))?;
        Ok(count > 0)
    }

    /// 检查映射表中是否存在指定的下游域名
    async fn mapping_downstream_exists(&self, downstream: &str) -> ProxyResult<bool> {
        let collection = self.mapping_collection();
        let count = collection
            .count_documents(doc! { "downstream_domain": downstream }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("检查映射表下游域名存在性失败: {}", e)))?;
        Ok(count > 0)
    }

    async fn get_max_downstream_priority(&self) -> ProxyResult<i32> {
        let collection = self.downstream_collection();
        let options = mongodb::options::FindOptions::builder()
            .sort(doc! { "priority": -1 })
            .limit(1)
            .build();

        let mut cursor = collection
            .find(doc! {}, options)
            .await
            .map_err(|e| ProxyError::database(&format!("查询最大优先级失败: {}", e)))?;

        if let Some(doc) = cursor
            .try_next()
            .await
            .map_err(|e| ProxyError::database(&format!("获取最大优先级失败: {}", e)))?
        {
            Ok(doc.priority)
        } else {
            Ok(0)
        }
    }

    async fn get_max_upstream_priority(&self) -> ProxyResult<i32> {
        let collection = self.upstream_collection();
        let options = mongodb::options::FindOptions::builder()
            .sort(doc! { "priority": -1 })
            .limit(1)
            .build();

        let mut cursor = collection
            .find(doc! {}, options)
            .await
            .map_err(|e| ProxyError::database(&format!("查询最大优先级失败: {}", e)))?;

        if let Some(doc) = cursor
            .try_next()
            .await
            .map_err(|e| ProxyError::database(&format!("获取最大优先级失败: {}", e)))?
        {
            Ok(doc.priority)
        } else {
            Ok(0)
        }
    }

    async fn mark_upstream_used(&self, domain: &str) -> ProxyResult<()> {
        let collection = self.upstream_collection();

        let filter = doc! { "domain": domain, "status": "available" };
        let update = doc! { "$set": { "status": "used" } };

        collection
            .update_one(filter, update, None)
            .await
            .map_err(|e| ProxyError::database(&format!("标记上游域名已使用失败: {}", e)))?;

        Ok(())
    }

    // 统计方法
    async fn count_downstream_by_status(&self, status: &str) -> ProxyResult<i64> {
        let collection = self.downstream_collection();
        let count = collection
            .count_documents(doc! { "status": status }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("统计下游域名失败: {}", e)))?;
        Ok(count as i64)
    }

    async fn count_upstream_by_status(&self, status: &str) -> ProxyResult<i64> {
        let collection = self.upstream_collection();
        let count = collection
            .count_documents(doc! { "status": status }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("统计上游域名失败: {}", e)))?;
        Ok(count as i64)
    }

    async fn count_mappings_by_status(&self, status: &str) -> ProxyResult<i64> {
        let collection = self.mapping_collection();
        let count = collection
            .count_documents(doc! { "status": status }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("统计映射状态失败: {}", e)))?;
        Ok(count as i64)
    }

    async fn get_request_stats(&self) -> ProxyResult<(i64, i64)> {
        let collection = self.mapping_collection();

        let pipeline = vec![doc! {
            "$group": {
                "_id": null,
                "total_requests": { "$sum": "$request_count" },
                "total_success": { "$sum": "$success_count" }
            }
        }];

        let mut cursor = collection
            .aggregate(pipeline, None)
            .await
            .map_err(|e| ProxyError::database(&format!("聚合请求统计失败: {}", e)))?;

        if let Some(doc) = cursor
            .try_next()
            .await
            .map_err(|e| ProxyError::database(&format!("获取请求统计失败: {}", e)))?
        {
            let total_requests = doc.get_i64("total_requests").unwrap_or(0);
            let total_success = doc.get_i64("total_success").unwrap_or(0);
            Ok((total_requests, total_success))
        } else {
            Ok((0, 0))
        }
    }

    /// 创建索引
    pub async fn create_indexes(&self) -> ProxyResult<()> {
        use mongodb::options::IndexOptions;
        use mongodb::IndexModel;

        // 下游域名池索引
        let downstream_coll = self.downstream_collection();
        let indexes = vec![
            IndexModel::builder()
                .keys(doc! { "domain": 1 })
                .options(IndexOptions::builder().unique(true).build())
                .build(),
            IndexModel::builder()
                .keys(doc! { "status": 1, "priority": 1 })
                .build(),
        ];
        downstream_coll
            .create_indexes(indexes, None)
            .await
            .map_err(|e| ProxyError::database(&format!("创建下游域名索引失败: {}", e)))?;

        // 上游域名池索引
        let upstream_coll = self.upstream_collection();
        let indexes = vec![
            IndexModel::builder()
                .keys(doc! { "domain": 1 })
                .options(IndexOptions::builder().unique(true).build())
                .build(),
            IndexModel::builder()
                .keys(doc! { "status": 1, "health_status": 1, "priority": 1 })
                .build(),
        ];
        upstream_coll
            .create_indexes(indexes, None)
            .await
            .map_err(|e| ProxyError::database(&format!("创建上游域名索引失败: {}", e)))?;

        // 代理映射索引
        let mapping_coll = self.mapping_collection();
        let indexes = vec![
            IndexModel::builder()
                .keys(doc! { "downstream_domain": 1 })
                .options(IndexOptions::builder().unique(true).build())
                .build(),
            IndexModel::builder().keys(doc! { "status": 1 }).build(),
        ];
        mapping_coll
            .create_indexes(indexes, None)
            .await
            .map_err(|e| ProxyError::database(&format!("创建映射索引失败: {}", e)))?;

        info!("域名池MongoDB索引创建完成");
        Ok(())
    }

    /// 根据上游域名获取所有映射关系
    pub async fn get_mappings_by_upstream(&self, upstream_domain: &str) -> ProxyResult<Vec<ProxyMapping>> {
        let collection = self.mapping_collection();

        let filter = doc! { "upstream_domain": upstream_domain };
        let cursor = collection
            .find(filter, None)
            .await
            .map_err(|e| ProxyError::database(&format!("查询上游域名映射失败: {}", e)))?;

        let mappings: Vec<ProxyMapping> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集映射数据失败: {}", e)))?;

        Ok(mappings)
    }

    /// 获取临时URL集合
    pub fn temp_urls_collection(&self) -> mongodb::Collection<crate::domains::models::TempDiscoveredUrl> {
        self.db.collection("temp_discovered_urls")
    }
}
