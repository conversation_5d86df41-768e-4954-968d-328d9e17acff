//! 临时URL存储管理器
//!
//! 用于管理发现但尚未处理的URL，防止重复处理并提供状态跟踪

use crate::domains::models::{TempDiscoveredUrl, TempUrlStatus};
use crate::types::{ProxyError, ProxyResult};
use chrono::Utc;
use futures::TryStreamExt;
use mongodb::bson::{doc, oid::ObjectId};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

/// 临时URL存储管理器
pub struct TempUrlStorage {
    db: mongodb::Database,
    /// 内存缓存，用于快速查重
    url_cache: Arc<RwLock<std::collections::HashSet<String>>>,
}

impl TempUrlStorage {
    /// 创建新的临时URL存储管理器
    pub fn new(db: mongodb::Database) -> Self {
        Self {
            db,
            url_cache: Arc::new(RwLock::new(std::collections::HashSet::new())),
        }
    }

    /// 初始化存储（创建索引和加载缓存）
    pub async fn init(&self) -> ProxyResult<()> {
        // 创建数据库索引
        self.create_indexes().await?;
        
        // 加载现有URL到缓存
        self.load_cache().await?;
        
        info!("临时URL存储管理器初始化完成");
        Ok(())
    }

    /// 添加临时URL
    pub async fn add_temp_url(
        &self,
        url: String,
        discovered_from: Option<String>,
    ) -> ProxyResult<bool> {
        // 检查URL是否已存在
        if self.url_exists(&url).await? {
            debug!("URL已存在于临时存储中: {}", url);
            return Ok(false);
        }

        // 创建临时URL记录
        let temp_url = TempDiscoveredUrl::new(url.clone(), discovered_from);
        
        // 插入数据库
        let collection = self.temp_urls_collection();
        let result = collection
            .insert_one(&temp_url, None)
            .await
            .map_err(|e| ProxyError::database(&format!("插入临时URL失败: {}", e)))?;

        // 更新内存缓存
        {
            let mut cache = self.url_cache.write().await;
            cache.insert(url.clone());
        }

        debug!("成功添加临时URL: {} (ID: {:?})", url, result.inserted_id);
        Ok(true)
    }

    /// 移除临时URL
    pub async fn remove_temp_url(&self, url: &str) -> ProxyResult<bool> {
        let collection = self.temp_urls_collection();
        
        let filter = doc! { "url": url };
        let result = collection
            .delete_one(filter, None)
            .await
            .map_err(|e| ProxyError::database(&format!("删除临时URL失败: {}", e)))?;

        if result.deleted_count > 0 {
            // 从内存缓存中移除
            {
                let mut cache = self.url_cache.write().await;
                cache.remove(url);
            }
            
            debug!("成功删除临时URL: {}", url);
            Ok(true)
        } else {
            debug!("临时URL不存在: {}", url);
            Ok(false)
        }
    }

    /// 获取待处理的URL列表
    pub async fn get_pending_urls(&self, limit: u32) -> ProxyResult<Vec<TempDiscoveredUrl>> {
        let collection = self.temp_urls_collection();
        
        let filter = doc! { 
            "status": "pending",
            "expires_at": { "$gt": Utc::now() }
        };
        
        let options = mongodb::options::FindOptions::builder()
            .sort(doc! { "created_at": 1 }) // 按创建时间排序，FIFO
            .limit(limit as i64)
            .build();

        let cursor = collection
            .find(filter, options)
            .await
            .map_err(|e| ProxyError::database(&format!("查询待处理URL失败: {}", e)))?;

        let urls: Vec<TempDiscoveredUrl> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集待处理URL失败: {}", e)))?;

        debug!("获取到 {} 个待处理URL", urls.len());
        Ok(urls)
    }

    /// 更新URL状态
    pub async fn update_url_status(
        &self,
        url: &str,
        status: TempUrlStatus,
        error_message: Option<String>,
    ) -> ProxyResult<bool> {
        let collection = self.temp_urls_collection();
        
        let filter = doc! { "url": url };
        let mut update_doc = doc! {
            "$set": {
                "status": bson::to_bson(&status).unwrap(),
                "last_attempt_at": Utc::now()
            },
            "$inc": {
                "processing_attempts": 1
            }
        };

        if let Some(error) = error_message {
            update_doc.get_document_mut("$set").unwrap().insert("error_message", error);
        }

        let result = collection
            .update_one(filter, update_doc, None)
            .await
            .map_err(|e| ProxyError::database(&format!("更新URL状态失败: {}", e)))?;

        if result.modified_count > 0 {
            debug!("成功更新URL状态: {} -> {:?}", url, status);
            Ok(true)
        } else {
            warn!("URL状态更新失败，URL不存在: {}", url);
            Ok(false)
        }
    }

    /// 清理过期的临时URL
    pub async fn cleanup_expired_urls(&self) -> ProxyResult<u64> {
        let collection = self.temp_urls_collection();
        
        let filter = doc! {
            "$or": [
                { "expires_at": { "$lt": Utc::now() } },
                { "status": "completed" },
                { 
                    "status": "failed",
                    "processing_attempts": { "$gte": 3 }
                }
            ]
        };

        // 获取要删除的URL列表，用于更新缓存
        let cursor = collection
            .find(filter.clone(), None)
            .await
            .map_err(|e| ProxyError::database(&format!("查询过期URL失败: {}", e)))?;

        let expired_urls: Vec<TempDiscoveredUrl> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集过期URL失败: {}", e)))?;

        // 删除过期URL
        let result = collection
            .delete_many(filter, None)
            .await
            .map_err(|e| ProxyError::database(&format!("删除过期URL失败: {}", e)))?;

        // 从内存缓存中移除
        if !expired_urls.is_empty() {
            let mut cache = self.url_cache.write().await;
            for url in expired_urls {
                cache.remove(&url.url);
            }
        }

        info!("清理了 {} 个过期临时URL", result.deleted_count);
        Ok(result.deleted_count)
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> ProxyResult<TempUrlStats> {
        let collection = self.temp_urls_collection();
        
        let total_count = collection
            .count_documents(doc! {}, None)
            .await
            .map_err(|e| ProxyError::database(&format!("统计总数失败: {}", e)))?;

        let pending_count = collection
            .count_documents(doc! { "status": "pending" }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("统计待处理数失败: {}", e)))?;

        let processing_count = collection
            .count_documents(doc! { "status": "processing" }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("统计处理中数失败: {}", e)))?;

        let completed_count = collection
            .count_documents(doc! { "status": "completed" }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("统计已完成数失败: {}", e)))?;

        let failed_count = collection
            .count_documents(doc! { "status": "failed" }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("统计失败数失败: {}", e)))?;

        Ok(TempUrlStats {
            total_count,
            pending_count,
            processing_count,
            completed_count,
            failed_count,
        })
    }

    /// 检查URL是否存在
    async fn url_exists(&self, url: &str) -> ProxyResult<bool> {
        // 先检查内存缓存
        {
            let cache = self.url_cache.read().await;
            if cache.contains(url) {
                return Ok(true);
            }
        }

        // 检查数据库
        let collection = self.temp_urls_collection();
        let count = collection
            .count_documents(doc! { "url": url }, None)
            .await
            .map_err(|e| ProxyError::database(&format!("检查URL存在性失败: {}", e)))?;

        Ok(count > 0)
    }

    /// 加载现有URL到缓存
    async fn load_cache(&self) -> ProxyResult<()> {
        let collection = self.temp_urls_collection();
        
        let cursor = collection
            .find(doc! {}, None)
            .await
            .map_err(|e| ProxyError::database(&format!("加载缓存失败: {}", e)))?;

        let urls: Vec<TempDiscoveredUrl> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集URL数据失败: {}", e)))?;

        let mut cache = self.url_cache.write().await;
        for url in urls {
            cache.insert(url.url);
        }

        debug!("加载了 {} 个URL到缓存", cache.len());
        Ok(())
    }

    /// 创建数据库索引
    async fn create_indexes(&self) -> ProxyResult<()> {
        use mongodb::options::IndexOptions;
        use mongodb::IndexModel;

        let collection = self.temp_urls_collection();

        let indexes = vec![
            // URL唯一索引
            IndexModel::builder()
                .keys(doc! { "url": 1 })
                .options(IndexOptions::builder().unique(true).build())
                .build(),
            // 状态和过期时间复合索引
            IndexModel::builder()
                .keys(doc! { "status": 1, "expires_at": 1 })
                .build(),
            // 创建时间索引（用于FIFO排序）
            IndexModel::builder()
                .keys(doc! { "created_at": 1 })
                .build(),
        ];

        collection
            .create_indexes(indexes, None)
            .await
            .map_err(|e| ProxyError::database(&format!("创建临时URL索引失败: {}", e)))?;

        debug!("临时URL存储索引创建完成");
        Ok(())
    }

    /// 获取临时URL集合
    fn temp_urls_collection(&self) -> mongodb::Collection<TempDiscoveredUrl> {
        self.db.collection("temp_discovered_urls")
    }
}

/// 临时URL统计信息
#[derive(Debug, Clone)]
pub struct TempUrlStats {
    pub total_count: u64,
    pub pending_count: u64,
    pub processing_count: u64,
    pub completed_count: u64,
    pub failed_count: u64,
}
