//! FIFO队列处理定时任务
//!
//! 定期处理FIFO队列中的上游域名，实现自动化的域名处理机制

use crate::domains::DomainPoolService;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::interval;
use tracing::{error, info, warn};

/// FIFO队列处理任务管理器
pub struct FifoProcessorTask {
    domain_service: Arc<DomainPoolService>,
    is_running: Arc<tokio::sync::RwLock<bool>>,
}

impl FifoProcessorTask {
    /// 创建新的FIFO处理任务
    pub fn new(domain_service: Arc<DomainPoolService>) -> Self {
        Self {
            domain_service,
            is_running: Arc::new(tokio::sync::RwLock::new(false)),
        }
    }

    /// 启动FIFO处理任务
    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 检查是否已经在运行
        {
            let running = self.is_running.read().await;
            if *running {
                warn!("FIFO处理任务已在运行中");
                return Ok(());
            }
        }

        // 标记为运行中
        {
            let mut running = self.is_running.write().await;
            *running = true;
        }

        let domain_service = self.domain_service.clone();
        let is_running = self.is_running.clone();

        // 启动后台任务
        tokio::spawn(async move {
            info!("FIFO队列处理任务启动");
            
            loop {
                // 检查是否应该停止
                {
                    let running = is_running.read().await;
                    if !*running {
                        info!("FIFO队列处理任务已停止");
                        break;
                    }
                }

                // 获取配置
                let config = domain_service.get_fifo_config().await;
                
                if !config.enabled {
                    // 如果FIFO处理被禁用，等待一段时间后再检查
                    tokio::time::sleep(Duration::from_secs(60)).await;
                    continue;
                }

                let process_interval = Duration::from_secs(config.process_interval_seconds);
                let mut interval_timer = interval(process_interval);
                
                // 等待下一个处理间隔
                interval_timer.tick().await;

                // 执行FIFO处理
                match domain_service.process_next_upstream_domain().await {
                    Ok(true) => {
                        info!("FIFO队列处理成功");
                        
                        // 获取队列状态
                        if let Ok(stats) = domain_service.get_upstream_queue_stats().await {
                            let pending_count = stats.iter()
                                .filter(|s| s.status == crate::domains::models::UpstreamProcessStatus::Pending)
                                .count();
                            
                            if pending_count > 0 {
                                info!("FIFO队列还有 {} 个待处理域名", pending_count);
                            }
                        }
                    }
                    Ok(false) => {
                        // 队列为空，等待更长时间
                        tokio::time::sleep(Duration::from_secs(300)).await; // 5分钟
                    }
                    Err(e) => {
                        error!("FIFO队列处理失败: {}", e);
                        // 处理失败时等待一段时间再重试
                        tokio::time::sleep(Duration::from_secs(60)).await;
                    }
                }
            }
        });

        info!("FIFO队列处理任务已启动");
        Ok(())
    }

    /// 停止FIFO处理任务
    pub async fn stop(&self) {
        let mut running = self.is_running.write().await;
        *running = false;
        info!("FIFO队列处理任务已标记为停止");
    }

    /// 检查任务是否在运行
    pub async fn is_running(&self) -> bool {
        let running = self.is_running.read().await;
        *running
    }

    /// 手动触发一次FIFO处理
    pub async fn trigger_once(&self) -> Result<u32, Box<dyn std::error::Error + Send + Sync>> {
        info!("手动触发FIFO队列处理");
        
        let config = self.domain_service.get_fifo_config().await;
        if !config.enabled {
            return Err("FIFO处理已禁用".into());
        }

        let mut processed_count = 0;
        let batch_size = config.batch_size;
        
        // 批量处理
        for _ in 0..batch_size {
            match self.domain_service.process_next_upstream_domain().await {
                Ok(true) => {
                    processed_count += 1;
                }
                Ok(false) => {
                    break; // 队列为空
                }
                Err(e) => {
                    warn!("处理域名失败: {}", e);
                    break;
                }
            }
        }
        
        info!("手动FIFO处理完成，处理了 {} 个域名", processed_count);
        Ok(processed_count)
    }

    /// 获取处理统计信息
    pub async fn get_processing_stats(&self) -> Result<FifoProcessingStats, Box<dyn std::error::Error + Send + Sync>> {
        let stats = self.domain_service.get_upstream_queue_stats().await?;
        let config = self.domain_service.get_fifo_config().await;
        
        let total_count = stats.len();
        let pending_count = stats.iter()
            .filter(|s| s.status == crate::domains::models::UpstreamProcessStatus::Pending)
            .count();
        let processing_count = stats.iter()
            .filter(|s| s.status == crate::domains::models::UpstreamProcessStatus::Processing)
            .count();
        let completed_count = stats.iter()
            .filter(|s| s.status == crate::domains::models::UpstreamProcessStatus::Completed)
            .count();
        let failed_count = stats.iter()
            .filter(|s| s.status == crate::domains::models::UpstreamProcessStatus::Failed)
            .count();
        let retrying_count = stats.iter()
            .filter(|s| s.status == crate::domains::models::UpstreamProcessStatus::Retrying)
            .count();

        Ok(FifoProcessingStats {
            enabled: config.enabled,
            total_count,
            pending_count,
            processing_count,
            completed_count,
            failed_count,
            retrying_count,
            batch_size: config.batch_size,
            process_interval_seconds: config.process_interval_seconds,
            is_task_running: self.is_running().await,
        })
    }
}

impl Drop for FifoProcessorTask {
    fn drop(&mut self) {
        // 在析构时尝试停止任务
        let is_running = self.is_running.clone();
        tokio::spawn(async move {
            let mut running = is_running.write().await;
            *running = false;
        });
    }
}

/// FIFO处理统计信息
#[derive(Debug, Clone)]
pub struct FifoProcessingStats {
    pub enabled: bool,
    pub total_count: usize,
    pub pending_count: usize,
    pub processing_count: usize,
    pub completed_count: usize,
    pub failed_count: usize,
    pub retrying_count: usize,
    pub batch_size: u32,
    pub process_interval_seconds: u64,
    pub is_task_running: bool,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_fifo_processor_lifecycle() {
        // 这里需要一个模拟的DomainPoolService
        // 由于依赖复杂，这个测试在实际环境中运行
        
        // 测试任务的基本生命周期
        // let task = FifoProcessorTask::new(mock_service);
        
        // assert!(!task.is_running().await);
        // task.start().await.unwrap();
        // assert!(task.is_running().await);
        // task.stop().await;
        // 等待一段时间确保任务停止
        // tokio::time::sleep(Duration::from_millis(100)).await;
        // assert!(!task.is_running().await);
    }
}
