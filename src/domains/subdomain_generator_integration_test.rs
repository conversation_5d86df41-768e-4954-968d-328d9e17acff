//! 子域名生成器集成测试
//! 
//! 测试子域名生成器与域名服务的集成

#[cfg(test)]
mod integration_tests {
    use super::super::SubdomainGenerator;

    #[tokio::test]
    async fn test_subdomain_generation_workflow() {
        let mut generator = SubdomainGenerator::new();
        
        // 测试场景1: 正常生成子域名
        let result1 = generator
            .generate_downstream_subdomain("mail.google.com", "a.com")
            .await
            .unwrap();
        assert_eq!(result1, "mail.a.com");
        
        // 测试场景2: 冲突时生成随机后缀
        let result2 = generator
            .generate_downstream_subdomain("mail.google.com", "a.com")
            .await
            .unwrap();
        assert!(result2.starts_with("mail-"));
        assert!(result2.ends_with(".a.com"));
        assert_ne!(result1, result2);
        
        // 测试场景3: 不同前缀的域名
        let result3 = generator
            .generate_downstream_subdomain("www.google.com", "a.com")
            .await
            .unwrap();
        assert_eq!(result3, "www.a.com");
        
        // 测试场景4: 单级域名
        let result4 = generator
            .generate_downstream_subdomain("google.com", "a.com")
            .await
            .unwrap();
        assert_eq!(result4, "google.a.com");
        
        // 验证缓存状态
        assert_eq!(generator.get_occupied_count(), 4);
    }

    #[test]
    fn test_domain_prefix_extraction_edge_cases() {
        let generator = SubdomainGenerator::new();
        
        // 测试各种域名格式
        assert_eq!(generator.extract_domain_prefix("api.v2.example.com"), "api");
        assert_eq!(generator.extract_domain_prefix("cdn-static.example.org"), "cdn-static");
        assert_eq!(generator.extract_domain_prefix("m.facebook.com"), "m");
        assert_eq!(generator.extract_domain_prefix("secure.login.yahoo.com"), "secure");
        assert_eq!(generator.extract_domain_prefix("blog.example.co.uk"), "blog");
    }

    #[test]
    fn test_random_suffix_uniqueness() {
        let generator = SubdomainGenerator::new();
        let mut suffixes = std::collections::HashSet::new();
        
        // 生成100个随机后缀，检查唯一性
        for _ in 0..100 {
            let suffix = generator.generate_random_suffix(5, 8);
            suffixes.insert(suffix);
        }
        
        // 应该有很高的唯一性（至少95%）
        assert!(suffixes.len() >= 95);
    }

    #[test]
    fn test_domain_validation_comprehensive() {
        let generator = SubdomainGenerator::new();
        
        // 有效域名
        let valid_domains = vec![
            "example.com",
            "sub.example.com",
            "api-v2.example.org",
            "cdn.static.example.net",
            "mail.google.com",
            "www.github.com",
            "secure-login.bank.com",
        ];
        
        for domain in valid_domains {
            assert!(generator.validate_domain_format(domain), "域名应该有效: {}", domain);
        }
        
        // 无效域名
        let invalid_domains = vec![
            "",                    // 空字符串
            "example",             // 没有点号
            ".example.com",        // 以点号开始
            "example.com.",        // 以点号结束
            "example..com",        // 连续点号
            "example.com/path",    // 包含路径
            "example.com:8080",    // 包含端口
            "example com",         // 包含空格
            "example@com",         // 包含特殊字符
        ];
        
        for domain in invalid_domains {
            assert!(!generator.validate_domain_format(domain), "域名应该无效: {}", domain);
        }
    }

    #[tokio::test]
    async fn test_cache_management() {
        let mut generator = SubdomainGenerator::new();
        
        // 初始状态
        assert_eq!(generator.get_occupied_count(), 0);
        
        // 手动标记一些域名为已占用
        generator.mark_subdomain_occupied("test1.a.com".to_string());
        generator.mark_subdomain_occupied("test2.a.com".to_string());
        assert_eq!(generator.get_occupied_count(), 2);
        
        // 批量更新缓存
        let new_occupied = vec![
            "mail.a.com".to_string(),
            "www.a.com".to_string(),
            "api.a.com".to_string(),
        ];
        generator.update_occupied_cache(new_occupied);
        assert_eq!(generator.get_occupied_count(), 3);
        
        // 清空缓存
        generator.clear_occupied_cache();
        assert_eq!(generator.get_occupied_count(), 0);
    }

    #[tokio::test]
    async fn test_conflict_detection() {
        let mut generator = SubdomainGenerator::new();
        
        // 预先占用一个子域名
        generator.mark_subdomain_occupied("mail.a.com".to_string());
        
        // 检查冲突检测
        let has_conflict = generator.check_subdomain_conflict("mail.a.com").await.unwrap();
        assert!(has_conflict);
        
        let no_conflict = generator.check_subdomain_conflict("www.a.com").await.unwrap();
        assert!(!no_conflict);
    }

    #[test]
    fn test_performance() {
        let generator = SubdomainGenerator::new();
        let start = std::time::Instant::now();
        
        // 测试大量前缀提取的性能
        for i in 0..10000 {
            let domain = format!("subdomain{}.example.com", i);
            let prefix = generator.extract_domain_prefix(&domain);
            assert_eq!(prefix, format!("subdomain{}", i));
        }
        
        let duration = start.elapsed();
        println!("10000次前缀提取耗时: {:?}", duration);
        
        // 应该在合理时间内完成（比如100ms）
        assert!(duration.as_millis() < 100);
    }
}
