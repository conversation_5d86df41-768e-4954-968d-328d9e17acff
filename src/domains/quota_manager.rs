//! 下游域名配额管理器
//!
//! 管理每个下游域名的子域名数量限制，支持配额检查、使用统计、自动切换等功能

use crate::domains::models::{DownstreamQuota, GlobalQuotaConfig, QuotaStats};
use crate::types::{ProxyError, ProxyResult};
use chrono::Utc;
use futures::TryStreamExt;
use mongodb::bson::{doc, oid::ObjectId};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

/// 配额管理器
pub struct QuotaManager {
    db: mongodb::Database,
    /// 内存缓存的配额信息
    quota_cache: Arc<RwLock<HashMap<String, DownstreamQuota>>>,
    /// 全局配额配置
    global_config: Arc<RwLock<GlobalQuotaConfig>>,
    /// 下游域名列表（按优先级排序）
    downstream_domains: Arc<RwLock<Vec<String>>>,
    /// 当前使用的下游域名索引
    current_domain_index: Arc<RwLock<usize>>,
}

impl QuotaManager {
    /// 创建新的配额管理器
    pub fn new(db: mongodb::Database) -> Self {
        Self {
            db,
            quota_cache: Arc::new(RwLock::new(HashMap::new())),
            global_config: Arc::new(RwLock::new(GlobalQuotaConfig::default())),
            downstream_domains: Arc::new(RwLock::new(Vec::new())),
            current_domain_index: Arc::new(RwLock::new(0)),
        }
    }

    /// 初始化配额管理器
    pub async fn init(&self) -> ProxyResult<()> {
        // 创建数据库索引
        self.create_indexes().await?;
        
        // 加载现有配额数据
        self.load_quota_cache().await?;
        
        // 加载全局配置
        self.load_global_config().await?;
        
        info!("配额管理器初始化完成");
        Ok(())
    }

    /// 检查域名是否有可用配额
    pub async fn check_quota_available(&self, domain: &str) -> ProxyResult<bool> {
        let quota_cache = self.quota_cache.read().await;
        
        if let Some(quota) = quota_cache.get(domain) {
            Ok(quota.has_available_quota())
        } else {
            // 如果没有配额记录，创建一个新的
            drop(quota_cache);
            self.create_quota_record(domain).await?;
            Ok(true)
        }
    }

    /// 获取子域名使用数量
    pub async fn get_subdomain_count(&self, domain: &str) -> ProxyResult<u32> {
        let quota_cache = self.quota_cache.read().await;
        
        if let Some(quota) = quota_cache.get(domain) {
            Ok(quota.used_count)
        } else {
            Ok(0)
        }
    }

    /// 添加子域名使用
    pub async fn add_subdomain_usage(
        &self,
        domain: &str,
        subdomain: &str,
    ) -> ProxyResult<bool> {
        // 检查并更新内存缓存
        {
            let mut quota_cache = self.quota_cache.write().await;
            
            if let Some(quota) = quota_cache.get_mut(domain) {
                if quota.add_subdomain(subdomain.to_string()) {
                    // 更新数据库
                    self.update_quota_in_db(quota).await?;
                    
                    debug!("添加子域名使用: {} -> {} (使用量: {}/{})", 
                        domain, subdomain, quota.used_count, quota.max_quota);
                    
                    return Ok(true);
                } else {
                    return Ok(false);
                }
            }
        }
        
        // 如果没有配额记录，创建一个新的
        self.create_quota_record(domain).await?;

        // 重新尝试添加（避免递归）
        {
            let mut quota_cache = self.quota_cache.write().await;
            if let Some(quota) = quota_cache.get_mut(domain) {
                if quota.add_subdomain(subdomain.to_string()) {
                    self.update_quota_in_db(quota).await?;
                    return Ok(true);
                }
            }
        }

        Ok(false)
    }

    /// 移除子域名使用
    pub async fn remove_subdomain_usage(
        &self,
        domain: &str,
        subdomain: &str,
    ) -> ProxyResult<bool> {
        let mut quota_cache = self.quota_cache.write().await;
        
        if let Some(quota) = quota_cache.get_mut(domain) {
            if quota.remove_subdomain(subdomain) {
                // 更新数据库
                self.update_quota_in_db(quota).await?;
                
                debug!("移除子域名使用: {} -> {} (使用量: {}/{})", 
                    domain, subdomain, quota.used_count, quota.max_quota);
                
                Ok(true)
            } else {
                Ok(false)
            }
        } else {
            Ok(false)
        }
    }

    /// 获取下一个可用的下游域名
    pub async fn get_next_available_downstream(&self) -> ProxyResult<Option<String>> {
        let downstream_domains = self.downstream_domains.read().await;
        let mut current_index = self.current_domain_index.write().await;
        
        if downstream_domains.is_empty() {
            return Ok(None);
        }
        
        let start_index = *current_index;
        
        // 循环查找可用的下游域名
        loop {
            let domain = &downstream_domains[*current_index];
            
            if self.check_quota_available(domain).await? {
                debug!("找到可用下游域名: {} (索引: {})", domain, *current_index);
                return Ok(Some(domain.clone()));
            }
            
            // 移动到下一个域名
            *current_index = (*current_index + 1) % downstream_domains.len();
            
            // 如果回到起始位置，说明所有域名都已满
            if *current_index == start_index {
                warn!("所有下游域名配额已满");
                return Ok(None);
            }
        }
    }

    /// 切换到下一个下游域名
    pub async fn switch_to_next_downstream(&self) -> ProxyResult<Option<String>> {
        let mut current_index = self.current_domain_index.write().await;
        let downstream_domains = self.downstream_domains.read().await;
        
        if downstream_domains.is_empty() {
            return Ok(None);
        }
        
        // 移动到下一个域名
        *current_index = (*current_index + 1) % downstream_domains.len();
        let next_domain = downstream_domains[*current_index].clone();
        
        info!("切换到下一个下游域名: {} (索引: {})", next_domain, *current_index);
        Ok(Some(next_domain))
    }

    /// 设置下游域名列表
    pub async fn set_downstream_domains(&self, domains: Vec<String>) -> ProxyResult<()> {
        let mut downstream_domains = self.downstream_domains.write().await;
        *downstream_domains = domains;
        
        // 重置当前索引
        let mut current_index = self.current_domain_index.write().await;
        *current_index = 0;
        
        info!("更新下游域名列表，共 {} 个域名", downstream_domains.len());
        Ok(())
    }

    /// 获取配额统计信息
    pub async fn get_quota_stats(&self, domain: &str) -> ProxyResult<Option<QuotaStats>> {
        let quota_cache = self.quota_cache.read().await;
        
        if let Some(quota) = quota_cache.get(domain) {
            Ok(Some(quota.get_stats()))
        } else {
            Ok(None)
        }
    }

    /// 获取所有配额统计信息
    pub async fn get_all_quota_stats(&self) -> ProxyResult<Vec<QuotaStats>> {
        let quota_cache = self.quota_cache.read().await;
        
        let stats: Vec<QuotaStats> = quota_cache
            .values()
            .map(|quota| quota.get_stats())
            .collect();
        
        Ok(stats)
    }

    /// 更新域名配额限制
    pub async fn update_domain_quota(&self, domain: &str, new_quota: u32) -> ProxyResult<()> {
        let mut quota_cache = self.quota_cache.write().await;
        
        if let Some(quota) = quota_cache.get_mut(domain) {
            quota.update_quota(new_quota);
            self.update_quota_in_db(quota).await?;
            
            info!("更新域名配额: {} -> {}", domain, new_quota);
            Ok(())
        } else {
            Err(ProxyError::invalid_input(&format!("域名配额记录不存在: {}", domain)))
        }
    }

    /// 更新全局配额配置
    pub async fn update_global_config(&self, config: GlobalQuotaConfig) -> ProxyResult<()> {
        // 更新内存配置
        {
            let mut global_config = self.global_config.write().await;
            *global_config = config.clone();
        }
        
        // 更新数据库
        let collection = self.global_config_collection();
        let filter = doc! {};
        let update = doc! {
            "$set": mongodb::bson::to_document(&config)
                .map_err(|e| ProxyError::internal(&format!("序列化配置失败: {}", e)))?
        };
        
        collection
            .update_one(filter, update, None)
            .await
            .map_err(|e| ProxyError::database(&format!("更新全局配置失败: {}", e)))?;
        
        info!("更新全局配额配置");
        Ok(())
    }

    /// 获取全局配额配置
    pub async fn get_global_config(&self) -> GlobalQuotaConfig {
        let global_config = self.global_config.read().await;
        global_config.clone()
    }

    /// 创建配额记录
    async fn create_quota_record(&self, domain: &str) -> ProxyResult<()> {
        let global_config = self.global_config.read().await;
        let quota = DownstreamQuota::new(domain.to_string(), Some(global_config.default_quota));
        drop(global_config);
        
        // 插入数据库
        let collection = self.quota_collection();
        collection
            .insert_one(&quota, None)
            .await
            .map_err(|e| ProxyError::database(&format!("创建配额记录失败: {}", e)))?;
        
        // 更新内存缓存
        {
            let mut quota_cache = self.quota_cache.write().await;
            quota_cache.insert(domain.to_string(), quota);
        }
        
        debug!("创建配额记录: {}", domain);
        Ok(())
    }

    /// 更新数据库中的配额记录
    async fn update_quota_in_db(&self, quota: &DownstreamQuota) -> ProxyResult<()> {
        let collection = self.quota_collection();
        let filter = doc! { "domain": &quota.domain };
        let update = doc! {
            "$set": mongodb::bson::to_document(quota)
                .map_err(|e| ProxyError::internal(&format!("序列化配额记录失败: {}", e)))?
        };
        
        collection
            .update_one(filter, update, None)
            .await
            .map_err(|e| ProxyError::database(&format!("更新配额记录失败: {}", e)))?;
        
        Ok(())
    }

    /// 加载配额缓存
    async fn load_quota_cache(&self) -> ProxyResult<()> {
        let collection = self.quota_collection();
        
        let cursor = collection
            .find(doc! {}, None)
            .await
            .map_err(|e| ProxyError::database(&format!("查询配额记录失败: {}", e)))?;

        let quotas: Vec<DownstreamQuota> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集配额记录失败: {}", e)))?;

        let mut quota_cache = self.quota_cache.write().await;
        for quota in quotas {
            quota_cache.insert(quota.domain.clone(), quota);
        }

        debug!("加载了 {} 个配额记录到缓存", quota_cache.len());
        Ok(())
    }

    /// 加载全局配置
    async fn load_global_config(&self) -> ProxyResult<()> {
        let collection = self.global_config_collection();
        
        if let Some(config) = collection
            .find_one(doc! {}, None)
            .await
            .map_err(|e| ProxyError::database(&format!("查询全局配置失败: {}", e)))?
        {
            let mut global_config = self.global_config.write().await;
            *global_config = config;
        } else {
            // 如果没有配置，创建默认配置
            let default_config = GlobalQuotaConfig::default();
            collection
                .insert_one(&default_config, None)
                .await
                .map_err(|e| ProxyError::database(&format!("创建默认配置失败: {}", e)))?;
        }
        
        Ok(())
    }

    /// 创建数据库索引
    async fn create_indexes(&self) -> ProxyResult<()> {
        use mongodb::options::IndexOptions;
        use mongodb::IndexModel;

        let quota_collection = self.quota_collection();

        let indexes = vec![
            // 域名唯一索引
            IndexModel::builder()
                .keys(doc! { "domain": 1 })
                .options(IndexOptions::builder().unique(true).build())
                .build(),
            // 启用状态和使用率复合索引
            IndexModel::builder()
                .keys(doc! { "enabled": 1, "usage_rate": 1 })
                .build(),
        ];

        quota_collection
            .create_indexes(indexes, None)
            .await
            .map_err(|e| ProxyError::database(&format!("创建配额索引失败: {}", e)))?;

        debug!("配额管理器索引创建完成");
        Ok(())
    }

    /// 获取配额集合
    fn quota_collection(&self) -> mongodb::Collection<DownstreamQuota> {
        self.db.collection("downstream_quotas")
    }

    /// 获取全局配置集合
    fn global_config_collection(&self) -> mongodb::Collection<GlobalQuotaConfig> {
        self.db.collection("global_quota_config")
    }
}
