//! 智能子域名生成器
//!
//! 基于上游域名前缀生成下游子域名，支持冲突检测和随机后缀生成

use anyhow::Result;
use rand::{thread_rng, Rng};
use rand::distributions::Alphanumeric;
use std::collections::HashSet;
use tracing::{debug, warn};

/// 智能子域名生成器
pub struct SubdomainGenerator {
    /// 已占用的子域名缓存
    occupied_subdomains: HashSet<String>,
}

impl SubdomainGenerator {
    /// 创建新的子域名生成器
    pub fn new() -> Self {
        Self {
            occupied_subdomains: HashSet::new(),
        }
    }

    /// 从上游域名提取前缀
    /// 
    /// # Examples
    /// 
    /// ```
    /// use sm_proxy::domains::SubdomainGenerator;
    /// 
    /// let generator = SubdomainGenerator::new();
    /// assert_eq!(generator.extract_domain_prefix("www.google.com"), "www");
    /// assert_eq!(generator.extract_domain_prefix("mail.google.com"), "mail");
    /// assert_eq!(generator.extract_domain_prefix("google.com"), "google");
    /// assert_eq!(generator.extract_domain_prefix("api.v2.example.com"), "api");
    /// ```
    pub fn extract_domain_prefix(&self, domain: &str) -> String {
        let parts: Vec<&str> = domain.split('.').collect();
        
        if parts.len() >= 2 {
            // 对于多级域名，取第一部分作为前缀
            // www.google.com → www
            // mail.google.com → mail
            // api.v2.example.com → api
            parts[0].to_string()
        } else {
            // 对于单级域名，直接使用域名本身
            // google.com → google (虽然这种情况很少见)
            domain.to_string()
        }
    }

    /// 生成下游子域名
    /// 
    /// # Arguments
    /// 
    /// * `upstream_domain` - 上游域名
    /// * `downstream_base` - 下游基础域名 (如 a.com)
    /// 
    /// # Returns
    /// 
    /// 返回生成的下游子域名，如果前缀被占用则生成带随机后缀的版本
    pub async fn generate_downstream_subdomain(
        &mut self,
        upstream_domain: &str,
        downstream_base: &str,
    ) -> Result<String> {
        // 提取上游域名前缀
        let prefix = self.extract_domain_prefix(upstream_domain);
        
        // 生成目标子域名
        let target_subdomain = format!("{}.{}", prefix, downstream_base);
        
        debug!(
            "为上游域名 {} 生成目标子域名: {}",
            upstream_domain, target_subdomain
        );
        
        // 检查是否被占用
        if self.check_subdomain_conflict(&target_subdomain).await? {
            // 生成带随机后缀的子域名
            let random_suffix = self.generate_random_suffix(3, 8);
            let fallback_subdomain = format!("{}-{}.{}", prefix, random_suffix, downstream_base);
            
            warn!(
                "子域名 {} 已被占用，使用随机后缀: {}",
                target_subdomain, fallback_subdomain
            );
            
            // 将新生成的子域名标记为已占用
            self.occupied_subdomains.insert(fallback_subdomain.clone());
            
            Ok(fallback_subdomain)
        } else {
            // 将子域名标记为已占用
            self.occupied_subdomains.insert(target_subdomain.clone());
            
            debug!("子域名 {} 可用，已标记为占用", target_subdomain);
            
            Ok(target_subdomain)
        }
    }

    /// 检查子域名是否冲突
    /// 
    /// 这个方法会检查：
    /// 1. 内存缓存中是否已存在
    /// 2. 数据库中是否已有映射记录
    /// 
    /// # Arguments
    /// 
    /// * `subdomain` - 要检查的子域名
    /// 
    /// # Returns
    /// 
    /// 如果冲突返回 true，否则返回 false
    pub async fn check_subdomain_conflict(&self, subdomain: &str) -> Result<bool> {
        // 首先检查内存缓存
        if self.occupied_subdomains.contains(subdomain) {
            debug!("子域名 {} 在内存缓存中已存在", subdomain);
            return Ok(true);
        }
        
        // TODO: 这里需要检查数据库中是否已有映射记录
        // 由于需要访问数据库，这个功能将在集成时实现
        // 现在先返回 false，表示不冲突
        
        debug!("子域名 {} 检查通过，无冲突", subdomain);
        Ok(false)
    }

    /// 生成随机后缀
    /// 
    /// # Arguments
    /// 
    /// * `min_len` - 最小长度
    /// * `max_len` - 最大长度
    /// 
    /// # Returns
    /// 
    /// 返回指定长度范围内的随机字符串（小写字母和数字）
    /// 
    /// # Examples
    /// 
    /// ```
    /// use sm_proxy::domains::SubdomainGenerator;
    /// 
    /// let generator = SubdomainGenerator::new();
    /// let suffix = generator.generate_random_suffix(3, 8);
    /// assert!(suffix.len() >= 3 && suffix.len() <= 8);
    /// assert!(suffix.chars().all(|c| c.is_ascii_alphanumeric()));
    /// ```
    pub fn generate_random_suffix(&self, min_len: usize, max_len: usize) -> String {
        let len = thread_rng().gen_range(min_len..=max_len);
        let chars = "abcdefghijklmnopqrstuvwxyz0123456789";

        (0..len)
            .map(|_| {
                let idx = thread_rng().gen_range(0..chars.len());
                chars.chars().nth(idx).unwrap()
            })
            .collect()
    }

    /// 更新已占用子域名缓存
    /// 
    /// 这个方法用于从数据库加载已存在的映射，更新内存缓存
    /// 
    /// # Arguments
    /// 
    /// * `occupied_subdomains` - 已占用的子域名列表
    pub fn update_occupied_cache(&mut self, occupied_subdomains: Vec<String>) {
        self.occupied_subdomains.clear();
        self.occupied_subdomains.extend(occupied_subdomains);
        
        debug!(
            "更新已占用子域名缓存，共 {} 个",
            self.occupied_subdomains.len()
        );
    }

    /// 手动标记子域名为已占用
    /// 
    /// # Arguments
    /// 
    /// * `subdomain` - 要标记的子域名
    pub fn mark_subdomain_occupied(&mut self, subdomain: String) {
        self.occupied_subdomains.insert(subdomain.clone());
        debug!("手动标记子域名为已占用: {}", subdomain);
    }

    /// 获取已占用子域名数量
    pub fn get_occupied_count(&self) -> usize {
        self.occupied_subdomains.len()
    }

    /// 清空已占用子域名缓存
    pub fn clear_occupied_cache(&mut self) {
        self.occupied_subdomains.clear();
        debug!("清空已占用子域名缓存");
    }

    /// 验证域名格式是否有效
    /// 
    /// # Arguments
    /// 
    /// * `domain` - 要验证的域名
    /// 
    /// # Returns
    /// 
    /// 如果域名格式有效返回 true，否则返回 false
    pub fn validate_domain_format(&self, domain: &str) -> bool {
        // 基本格式检查
        if domain.is_empty() || domain.len() > 253 {
            return false;
        }

        // 必须包含点号
        if !domain.contains('.') {
            return false;
        }

        // 不能以点号开始或结束
        if domain.starts_with('.') || domain.ends_with('.') {
            return false;
        }

        // 不能包含连续的点号
        if domain.contains("..") {
            return false;
        }

        // 检查字符是否合法
        domain
            .chars()
            .all(|c| c.is_ascii_alphanumeric() || c == '.' || c == '-')
    }
}

impl Default for SubdomainGenerator {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_domain_prefix() {
        let generator = SubdomainGenerator::new();
        
        assert_eq!(generator.extract_domain_prefix("www.google.com"), "www");
        assert_eq!(generator.extract_domain_prefix("mail.google.com"), "mail");
        assert_eq!(generator.extract_domain_prefix("google.com"), "google");
        assert_eq!(generator.extract_domain_prefix("api.v2.example.com"), "api");
        assert_eq!(generator.extract_domain_prefix("subdomain.example.org"), "subdomain");
    }

    #[test]
    fn test_generate_random_suffix() {
        let generator = SubdomainGenerator::new();
        
        for _ in 0..10 {
            let suffix = generator.generate_random_suffix(3, 8);
            assert!(suffix.len() >= 3 && suffix.len() <= 8);
            assert!(suffix.chars().all(|c| c.is_ascii_alphanumeric()));
            assert!(suffix.chars().all(|c| c.is_lowercase() || c.is_ascii_digit()));
        }
    }

    #[test]
    fn test_validate_domain_format() {
        let generator = SubdomainGenerator::new();
        
        // 有效域名
        assert!(generator.validate_domain_format("example.com"));
        assert!(generator.validate_domain_format("sub.example.com"));
        assert!(generator.validate_domain_format("api-v2.example.org"));
        
        // 无效域名
        assert!(!generator.validate_domain_format(""));
        assert!(!generator.validate_domain_format("example"));
        assert!(!generator.validate_domain_format(".example.com"));
        assert!(!generator.validate_domain_format("example.com."));
        assert!(!generator.validate_domain_format("example..com"));
    }

    #[tokio::test]
    async fn test_generate_downstream_subdomain() {
        let mut generator = SubdomainGenerator::new();
        
        // 测试正常生成
        let result = generator
            .generate_downstream_subdomain("mail.google.com", "a.com")
            .await
            .unwrap();
        assert_eq!(result, "mail.a.com");
        
        // 测试冲突时的随机后缀生成
        // 由于第一次已经占用了 mail.a.com，第二次应该生成随机后缀
        let result2 = generator
            .generate_downstream_subdomain("mail.google.com", "a.com")
            .await
            .unwrap();
        assert!(result2.starts_with("mail-"));
        assert!(result2.ends_with(".a.com"));
        assert_ne!(result, result2);
    }

    #[test]
    fn test_occupied_cache_management() {
        let mut generator = SubdomainGenerator::new();
        
        // 测试手动标记
        generator.mark_subdomain_occupied("test.a.com".to_string());
        assert_eq!(generator.get_occupied_count(), 1);
        
        // 测试批量更新
        generator.update_occupied_cache(vec![
            "mail.a.com".to_string(),
            "www.a.com".to_string(),
            "api.a.com".to_string(),
        ]);
        assert_eq!(generator.get_occupied_count(), 3);
        
        // 测试清空
        generator.clear_occupied_cache();
        assert_eq!(generator.get_occupied_count(), 0);
    }
}
