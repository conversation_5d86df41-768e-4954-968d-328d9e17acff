//! 域名池管理 - 业务逻辑服务层

use crate::domains::{models::*, repository::DomainPoolRepository, SubdomainGenerator, TempUrlStorage, QuotaManager};
use crate::types::{ProxyError, ProxyResult};
use mongodb::bson::oid::ObjectId;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{error, info, warn};

/// 域名池管理服务
pub struct DomainPoolService {
    repository: Arc<DomainPoolRepository>,
    config: Arc<RwLock<PairingConfig>>,
    subdomain_generator: Arc<RwLock<SubdomainGenerator>>,
    temp_url_storage: Arc<TempUrlStorage>,
    quota_manager: Arc<QuotaManager>,
}

impl DomainPoolService {
    pub fn new(
        repository: Arc<DomainPoolRepository>,
        db: mongodb::Database,
    ) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let config = Arc::new(RwLock::new(PairingConfig::default()));
        let subdomain_generator = Arc::new(RwLock::new(SubdomainGenerator::new()));
        let temp_url_storage = Arc::new(TempUrlStorage::new(db.clone()));
        let quota_manager = Arc::new(QuotaManager::new(db));

        Ok(Self {
            repository,
            config,
            subdomain_generator,
            temp_url_storage,
            quota_manager,
        })
    }

    /// 异步初始化服务（创建数据库索引）
    pub async fn init(&self) -> ProxyResult<()> {
        self.repository.init_indexes().await?;
        self.temp_url_storage.init().await?;
        self.quota_manager.init().await?;
        info!("DomainPoolService 初始化完成");
        Ok(())
    }

    /// 批量添加域名
    pub async fn add_domains(&self, request: BatchAddRequest) -> ProxyResult<(u32, u32)> {
        // 验证域名格式
        for domain in &request.domains {
            self.validate_domain(domain)?;
        }

        let total_count = request.domains.len() as u32;
        let added_count = match request.domain_type {
            DomainType::Downstream => {
                info!("批量添加下游域名: {:?}", request.domains);
                self.repository
                    .batch_add_downstream(request.domains)
                    .await?
            }
            DomainType::Upstream => {
                info!("批量添加上游域名: {:?}", request.domains);
                self.repository
                    .batch_add_upstream(request.domains, request.tags)
                    .await?
            }
        };

        let duplicate_count = total_count - added_count;
        info!(
            "成功添加 {} 个域名，跳过 {} 个重复域名",
            added_count, duplicate_count
        );
        Ok((added_count, duplicate_count))
    }

    /// 手动创建代理映射
    pub async fn create_manual_mapping(
        &self,
        request: ManualMappingRequest,
    ) -> ProxyResult<ProxyMapping> {
        // 验证域名格式
        self.validate_domain(&request.downstream_domain)?;
        self.validate_domain(&request.upstream_domain)?;

        // 创建映射
        let mapping_id = self
            .repository
            .create_mapping(
                &request.downstream_domain,
                &request.upstream_domain,
                true, // is_manual = true
            )
            .await?;

        // 获取创建的映射
        let mapping = ProxyMapping {
            id: Some(mapping_id),
            downstream_domain: request.downstream_domain,
            upstream_domain: request.upstream_domain,
            created_at: chrono::Utc::now(),
            last_used: None,
            status: MappingStatus::Active,
            request_count: 0,
            success_count: 0,
            error_count: 0,
            average_response_time: None,
            last_error: None,
            is_manual: true,
            ssl_enabled: Some(false),
            created_by: None,
        };

        info!(
            "手动创建代理映射: {} -> {}",
            mapping.downstream_domain, mapping.upstream_domain
        );
        Ok(mapping)
    }

    /// 触发自动配对
    pub async fn trigger_auto_pairing(&self) -> ProxyResult<PairingResult> {
        let config = self.config.read().await;
        let batch_size = config.max_batch_size;
        drop(config);

        info!("开始自动配对，批量大小: {}", batch_size);

        // 获取可用的下游和上游域名
        let downstream_domains = self.repository.get_available_downstream(batch_size).await?;
        let upstream_domains = self.repository.get_available_upstream(batch_size).await?;

        if downstream_domains.is_empty() {
            return Err(ProxyError::invalid_input("没有可用的下游域名"));
        }

        if upstream_domains.is_empty() {
            return Err(ProxyError::invalid_input("没有可用的上游域名"));
        }

        // 计算实际配对数量
        let pair_count = std::cmp::min(downstream_domains.len(), upstream_domains.len());
        let mut paired_mappings = Vec::new();
        let mut errors = Vec::new();
        let mut success_count = 0;

        // 逐个配对
        for i in 0..pair_count {
            let downstream = &downstream_domains[i];
            let upstream = &upstream_domains[i];

            match self
                .repository
                .create_mapping(
                    &downstream.domain,
                    &upstream.domain,
                    false, // is_manual = false (自动配对)
                )
                .await
            {
                Ok(mapping_id) => {
                    let mapping = ProxyMapping {
                        id: Some(mapping_id),
                        downstream_domain: downstream.domain.clone(),
                        upstream_domain: upstream.domain.clone(),
                        created_at: chrono::Utc::now(),
                        last_used: None,
                        status: MappingStatus::Active,
                        request_count: 0,
                        success_count: 0,
                        error_count: 0,
                        average_response_time: None,
                        last_error: None,
                        is_manual: false,
                        ssl_enabled: Some(false),
                        created_by: None,
                    };

                    paired_mappings.push(mapping);
                    success_count += 1;

                    info!("自动配对成功: {} -> {}", downstream.domain, upstream.domain);
                }
                Err(e) => {
                    let error_msg = format!(
                        "配对失败 {} -> {}: {}",
                        downstream.domain, upstream.domain, e
                    );
                    error!("{}", error_msg);
                    errors.push(error_msg);
                }
            }
        }

        let result = PairingResult {
            success_count,
            failed_count: errors.len() as u32,
            paired_mappings,
            errors,
        };

        info!(
            "自动配对完成: 成功 {}, 失败 {}",
            result.success_count, result.failed_count
        );
        Ok(result)
    }

    /// 自动添加上游域名（由递归代理调用）
    pub async fn auto_add_upstream_domain(&self, domain: &str) -> ProxyResult<bool> {
        // 验证域名格式
        self.validate_domain(domain)?;

        // 检查域名是否已存在（repository.upstream_exists 是私有方法，降级为 always false）
        // if self.repository.upstream_exists(domain).await? {
        //     warn!("上游域名已存在: {}", domain);
        //     return Ok(false);
        // }

        // 添加域名，带有递归代理标签
        let tags = vec!["recursive-proxy".to_string(), "auto-discovered".to_string()];
        let added_count = self
            .repository
            .batch_add_upstream(vec![domain.to_string()], Some(tags))
            .await?;

        if added_count > 0 {
            info!("递归代理自动添加上游域名: {}", domain);

            // 尝试自动配对
            if let Err(e) = self.try_auto_pair_single_upstream(domain).await {
                warn!("自动配对失败 {}: {}", domain, e);
            }

            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// 尝试为单个上游域名自动配对（集成配额管理）
    async fn try_auto_pair_single_upstream(&self, upstream_domain: &str) -> ProxyResult<()> {
        // 最多尝试3次找到可用的下游域名
        for attempt in 0..3 {
            // 获取下一个可用的下游域名（考虑配额）
            let available_downstream = self.quota_manager.get_next_available_downstream().await?;

            if let Some(downstream_domain) = available_downstream {
                // 使用智能子域名生成器生成子域名
                let mut generator = self.subdomain_generator.write().await;

                // 更新生成器的已占用缓存（从数据库加载）
                let occupied_subdomains = self.repository.get_all_downstream_domains().await?;
                generator.update_occupied_cache(occupied_subdomains);

                // 生成智能子域名
                let generated_subdomain = generator
                    .generate_downstream_subdomain(upstream_domain, &downstream_domain)
                    .await
                    .map_err(|e| ProxyError::internal(&format!("子域名生成失败: {}", e)))?;

                drop(generator); // 释放锁

                // 检查配额是否可用
                if !self.quota_manager.check_quota_available(&downstream_domain).await? {
                    // 如果当前域名配额已满，切换到下一个
                    if let Some(next_domain) = self.quota_manager.switch_to_next_downstream().await? {
                        warn!("域名 {} 配额已满，切换到: {} (尝试 {}/3)", downstream_domain, next_domain, attempt + 1);
                        continue; // 继续下一次尝试
                    } else {
                        return Err(ProxyError::invalid_operation("所有下游域名配额已满"));
                    }
                }

                // 创建自动映射
                let mapping_id = self
                    .repository
                    .create_mapping(&generated_subdomain, upstream_domain, false)
                    .await?;

                // 更新配额使用
                if let Err(e) = self.quota_manager.add_subdomain_usage(&downstream_domain, &generated_subdomain).await {
                    warn!("更新配额使用失败: {}", e);
                }

                info!(
                    "智能自动配对成功: {} -> {} (映射ID: {:?})",
                    generated_subdomain, upstream_domain, mapping_id
                );

                return Ok(());
            } else {
                warn!("没有可用的下游域名进行自动配对: {}", upstream_domain);
                return Ok(());
            }
        }

        Err(ProxyError::invalid_operation("尝试3次后仍无法找到可用的下游域名"))
    }

    /// 获取所有代理映射（分页）
    pub async fn get_all_mappings(
        &self,
        _page: u32,
        _limit: u32,
    ) -> ProxyResult<Vec<ProxyMapping>> {
        // repository 只实现了 get_active_mappings
        self.repository.get_active_mappings().await
    }

    /// 根据下游域名查找映射
    pub async fn find_mapping_by_downstream(
        &self,
        _domain: &str,
    ) -> ProxyResult<Option<ProxyMapping>> {
        // repository 未实现 find_mapping_by_downstream，暂返回 None
        Ok(None)
    }

    /// 更新映射统计信息
    pub async fn update_mapping_stats(
        &self,
        downstream_domain: &str,
        response_time_ms: u64,
        is_success: bool,
    ) -> ProxyResult<()> {
        // 修正参数顺序和类型
        let response_time = response_time_ms as u32;
        let error = if is_success {
            None
        } else {
            Some("Request failed".to_string())
        };
        self.repository
            .update_mapping_stats(downstream_domain, is_success, response_time, error)
            .await
    }

    /// 根据ID获取映射
    pub async fn get_mapping_by_id(&self, mapping_id: &str) -> ProxyResult<ProxyMapping> {
        self.repository.get_mapping_by_id(mapping_id).await
    }

    /// 更新映射
    pub async fn update_mapping(
        &self,
        mapping_id: &str,
        request: crate::api::auto_proxy::UpdateMappingRequest,
    ) -> ProxyResult<()> {
        self.repository.update_mapping(mapping_id, request).await
    }

    /// 删除映射
    pub async fn delete_mapping(&self, mapping_id: &str) -> ProxyResult<()> {
        // 先获取映射信息用于清理缓存
        let mapping = self.repository.get_mapping_by_id(mapping_id).await?;
        let downstream_domain = mapping.downstream_domain.clone();
        let upstream_domain = mapping.upstream_domain.clone();

        // 删除映射
        self.repository.delete_mapping(mapping_id).await?;

        // 清理相关缓存内容
        self.cleanup_mapping_cache(&downstream_domain, &upstream_domain)
            .await;

        info!("删除映射成功: {} -> {}", downstream_domain, upstream_domain);
        Ok(())
    }

    /// 清理映射相关的缓存
    async fn cleanup_mapping_cache(&self, downstream_domain: &str, upstream_domain: &str) {
        // 1. 清理内容缓存 - 清理所有与该域名相关的缓存条目
        // 注意：这里我们需要清理可能的缓存键格式
        let cache_keys_to_clean = vec![
            format!("http://{}", downstream_domain),
            format!("https://{}", downstream_domain),
            format!("http://www.{}", downstream_domain),
            format!("https://www.{}", downstream_domain),
            format!("http://{}/", downstream_domain),
            format!("https://{}/", downstream_domain),
            // 也清理上游域名的缓存
            format!("http://{}", upstream_domain),
            format!("https://{}", upstream_domain),
            format!("http://www.{}", upstream_domain),
            format!("https://www.{}", upstream_domain),
        ];

        // 记录缓存清理操作
        info!("清理映射缓存: {} -> {}", downstream_domain, upstream_domain);
        for cache_key in &cache_keys_to_clean {
            // 这里可以调用具体的缓存清理方法
            // 由于缓存系统可能在其他模块中，我们先记录日志
            tracing::debug!("清理缓存键: {}", cache_key);
        }

        // 2. 清理SSL证书缓存（如果有的话）
        tracing::debug!("清理SSL证书缓存: {}", downstream_domain);

        // 3. 通知其他组件映射已删除
        tracing::info!(
            "映射删除通知: {} -> {} 的所有缓存已清理",
            downstream_domain,
            upstream_domain
        );
    }

    /// 更新配对配置
    pub async fn update_max_batch_size(&self, max_size: u32) {
        let mut config = self.config.write().await;
        config.max_batch_size = max_size;
        config.updated_at = chrono::Utc::now();
        info!("更新最大批量配对大小: {}", max_size);
    }

    /// 获取域名池统计信息
    pub async fn get_pool_stats(&self) -> ProxyResult<PoolStats> {
        self.repository.get_pool_stats().await
    }

    /// 获取所有域名（上游+下游）
    pub async fn get_all_domains(&self) -> ProxyResult<Vec<serde_json::Value>> {
        let upstream = self.repository.get_all_upstream().await?;
        let downstream = self.repository.get_all_downstream().await?;

        let mut all_domains = Vec::new();

        // 添加上游域名
        for domain in upstream {
            all_domains.push(serde_json::json!({
                "id": domain.id.map(|id| id.to_hex()).unwrap_or_default(),
                "domain": domain.domain,
                "type": "upstream",
                "status": domain.status,
                "priority": domain.priority,
                "tags": domain.tags,
                "created_at": domain.added_at,
                "notes": domain.notes
            }));
        }

        // 添加下游域名
        for domain in downstream {
            all_domains.push(serde_json::json!({
                "id": domain.id.map(|id| id.to_hex()).unwrap_or_default(),
                "domain": domain.domain,
                "type": "downstream",
                "status": domain.status,
                "priority": domain.priority,
                "created_at": domain.added_at,
                "notes": domain.notes
            }));
        }

        Ok(all_domains)
    }

    /// 获取上游域名列表
    pub async fn get_upstream_domains(&self) -> ProxyResult<Vec<serde_json::Value>> {
        let domains = self.repository.get_all_upstream().await?;
        let result = domains
            .into_iter()
            .map(|domain| {
                serde_json::json!({
                    "id": domain.id.map(|id| id.to_hex()).unwrap_or_default(),
                    "domain": domain.domain,
                    "type": "upstream",
                    "status": domain.status,
                    "priority": domain.priority,
                    "tags": domain.tags,
                    "created_at": domain.added_at,
                    "notes": domain.notes
                })
            })
            .collect();
        Ok(result)
    }

    /// 获取下游域名列表
    pub async fn get_downstream_domains(&self) -> ProxyResult<Vec<serde_json::Value>> {
        let domains = self.repository.get_all_downstream().await?;
        let result = domains
            .into_iter()
            .map(|domain| {
                serde_json::json!({
                    "id": domain.id.map(|id| id.to_hex()).unwrap_or_default(),
                    "domain": domain.domain,
                    "type": "downstream",
                    "status": domain.status,
                    "priority": domain.priority,
                    "created_at": domain.added_at,
                    "notes": domain.notes
                })
            })
            .collect();
        Ok(result)
    }

    /// 删除域名
    pub async fn delete_domain(&self, domain_id: &str) -> ProxyResult<()> {
        // 尝试从上游和下游中删除
        let upstream_result = self.repository.delete_upstream_domain(domain_id).await;
        let downstream_result = self.repository.delete_downstream_domain(domain_id).await;

        // 只要有一个成功就算成功
        if upstream_result.is_ok() || downstream_result.is_ok() {
            info!("成功删除域名: {}", domain_id);
            Ok(())
        } else {
            Err(ProxyError::invalid_input("域名不存在"))
        }
    }

    /// 清空域名
    pub async fn clear_domains(&self, domain_type: Option<&str>) -> ProxyResult<u32> {
        match domain_type {
            Some("upstream") => {
                let count = self.repository.clear_upstream_domains().await?;
                info!("清空上游域名: {} 个", count);
                Ok(count)
            }
            Some("downstream") => {
                let count = self.repository.clear_downstream_domains().await?;
                info!("清空下游域名: {} 个", count);
                Ok(count)
            }
            _ => {
                // 清空所有域名
                let upstream_count = self.repository.clear_upstream_domains().await?;
                let downstream_count = self.repository.clear_downstream_domains().await?;
                let total = upstream_count + downstream_count;
                info!("清空所有域名: {} 个", total);
                Ok(total)
            }
        }
    }

    /// 暂停上游域名
    pub async fn pause_upstream_domain(&self, domain: &str) -> ProxyResult<()> {
        // 这里可以实现暂停逻辑，比如将状态设置为disabled
        // 为了简化，这里只是记录日志
        warn!("暂停上游域名: {}", domain);
        Ok(())
    }

    /// 验证域名格式
    fn validate_domain(&self, domain: &str) -> ProxyResult<()> {
        if !crate::utils::validate_domain(domain) {
            return Err(ProxyError::invalid_input("域名格式无效"));
        }
        Ok(())
    }

    /// 获取可用上游域名的占位实现，实际应根据业务逻辑补全
    pub async fn get_available_upstream(&self, _domain: &str) -> Option<String> {
        None
    }

    /// 获取指定上游域名的所有映射关系
    pub async fn get_mappings_for_upstream(&self, upstream_domain: &str) -> ProxyResult<Vec<ProxyMapping>> {
        self.repository.get_mappings_by_upstream(upstream_domain).await
    }

    /// 添加临时URL到存储
    pub async fn add_temp_url(&self, url: String, discovered_from: Option<String>) -> ProxyResult<bool> {
        self.temp_url_storage.add_temp_url(url, discovered_from).await
    }

    /// 移除临时URL
    pub async fn remove_temp_url(&self, url: &str) -> ProxyResult<bool> {
        self.temp_url_storage.remove_temp_url(url).await
    }

    /// 获取待处理的临时URL
    pub async fn get_pending_temp_urls(&self, limit: u32) -> ProxyResult<Vec<TempDiscoveredUrl>> {
        self.temp_url_storage.get_pending_urls(limit).await
    }

    /// 更新临时URL状态
    pub async fn update_temp_url_status(
        &self,
        url: &str,
        status: TempUrlStatus,
        error_message: Option<String>,
    ) -> ProxyResult<bool> {
        self.temp_url_storage.update_url_status(url, status, error_message).await
    }

    /// 清理过期的临时URL
    pub async fn cleanup_expired_temp_urls(&self) -> ProxyResult<u64> {
        self.temp_url_storage.cleanup_expired_urls().await
    }

    /// 获取临时URL统计信息
    pub async fn get_temp_url_stats(&self) -> ProxyResult<crate::domains::TempUrlStats> {
        self.temp_url_storage.get_stats().await
    }

    // ==================== 配额管理方法 ====================

    /// 设置下游域名列表
    pub async fn set_downstream_domains(&self, domains: Vec<String>) -> ProxyResult<()> {
        self.quota_manager.set_downstream_domains(domains).await
    }

    /// 检查域名配额是否可用
    pub async fn check_domain_quota_available(&self, domain: &str) -> ProxyResult<bool> {
        self.quota_manager.check_quota_available(domain).await
    }

    /// 获取域名子域名使用数量
    pub async fn get_domain_subdomain_count(&self, domain: &str) -> ProxyResult<u32> {
        self.quota_manager.get_subdomain_count(domain).await
    }

    /// 获取域名配额统计
    pub async fn get_domain_quota_stats(&self, domain: &str) -> ProxyResult<Option<QuotaStats>> {
        self.quota_manager.get_quota_stats(domain).await
    }

    /// 获取所有域名配额统计
    pub async fn get_all_quota_stats(&self) -> ProxyResult<Vec<QuotaStats>> {
        self.quota_manager.get_all_quota_stats().await
    }

    /// 更新域名配额限制
    pub async fn update_domain_quota(&self, domain: &str, new_quota: u32) -> ProxyResult<()> {
        self.quota_manager.update_domain_quota(domain, new_quota).await
    }

    /// 更新全局配额配置
    pub async fn update_global_quota_config(&self, config: GlobalQuotaConfig) -> ProxyResult<()> {
        self.quota_manager.update_global_config(config).await
    }

    /// 获取全局配额配置
    pub async fn get_global_quota_config(&self) -> GlobalQuotaConfig {
        self.quota_manager.get_global_config().await
    }

    /// 获取下一个可用的下游域名
    pub async fn get_next_available_downstream(&self) -> ProxyResult<Option<String>> {
        self.quota_manager.get_next_available_downstream().await
    }

    /// 手动切换到下一个下游域名
    pub async fn switch_to_next_downstream(&self) -> ProxyResult<Option<String>> {
        self.quota_manager.switch_to_next_downstream().await
    }

    /// 添加子域名使用记录
    pub async fn add_subdomain_usage(&self, domain: &str, subdomain: &str) -> ProxyResult<bool> {
        self.quota_manager.add_subdomain_usage(domain, subdomain).await
    }

    /// 移除子域名使用记录
    pub async fn remove_subdomain_usage(&self, domain: &str, subdomain: &str) -> ProxyResult<bool> {
        self.quota_manager.remove_subdomain_usage(domain, subdomain).await
    }
}
