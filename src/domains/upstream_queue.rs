//! FIFO上游域名队列管理器
//!
//! 实现先进先出的上游域名处理机制，支持队列管理、自动处理、状态跟踪等功能

use crate::domains::models::{UpstreamQueueItem, UpstreamProcessStatus, FifoQueueConfig, UpstreamQueueStats};
use crate::types::{ProxyError, ProxyResult};
use chrono::Utc;
use futures::TryStreamExt;
use mongodb::bson::{doc, oid::ObjectId};
use std::collections::VecDeque;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};

/// FIFO上游域名队列管理器
pub struct UpstreamQueue {
    db: mongodb::Database,
    /// 内存队列缓存
    queue_cache: Arc<RwLock<VecDeque<UpstreamQueueItem>>>,
    /// FIFO配置
    config: Arc<RwLock<FifoQueueConfig>>,
    /// 队列位置计数器
    position_counter: Arc<RwLock<u64>>,
    /// 是否正在处理
    is_processing: Arc<RwLock<bool>>,
}

impl UpstreamQueue {
    /// 创建新的上游队列管理器
    pub fn new(db: mongodb::Database) -> Self {
        Self {
            db,
            queue_cache: Arc::new(RwLock::new(VecDeque::new())),
            config: Arc::new(RwLock::new(FifoQueueConfig::default())),
            position_counter: Arc::new(RwLock::new(0)),
            is_processing: Arc::new(RwLock::new(false)),
        }
    }

    /// 初始化队列管理器
    pub async fn init(&self) -> ProxyResult<()> {
        // 创建数据库索引
        self.create_indexes().await?;
        
        // 加载配置
        self.load_config().await?;
        
        // 加载现有队列
        self.load_queue_from_db().await?;
        
        info!("FIFO上游域名队列管理器初始化完成");
        Ok(())
    }

    /// 入队（添加上游域名到队列）
    pub async fn enqueue(&self, domain: String, priority: Option<u32>) -> ProxyResult<bool> {
        // 检查是否已存在
        if self.contains_domain(&domain).await? {
            debug!("域名已存在于队列中: {}", domain);
            return Ok(false);
        }

        // 检查队列长度限制
        let config = self.config.read().await;
        let max_length = config.max_queue_length;
        drop(config);

        let queue_size = {
            let queue = self.queue_cache.read().await;
            queue.len()
        };

        if queue_size >= max_length as usize {
            return Err(ProxyError::invalid_operation("队列已满"));
        }

        // 创建队列项
        let mut item = UpstreamQueueItem::new(domain.clone(), priority);
        
        // 设置队列位置
        {
            let mut counter = self.position_counter.write().await;
            *counter += 1;
            item.queue_position = *counter;
        }

        // 插入数据库
        let collection = self.queue_collection();
        let result = collection
            .insert_one(&item, None)
            .await
            .map_err(|e| ProxyError::database(&format!("入队失败: {}", e)))?;

        item.id = Some(result.inserted_id.as_object_id().unwrap());

        // 添加到内存队列（按优先级和位置排序）
        {
            let mut queue = self.queue_cache.write().await;
            
            // 找到合适的插入位置（优先级高的在前，同优先级按FIFO）
            let insert_pos = queue
                .iter()
                .position(|existing| {
                    existing.priority > item.priority || 
                    (existing.priority == item.priority && existing.queue_position > item.queue_position)
                })
                .unwrap_or(queue.len());
            
            queue.insert(insert_pos, item);
        }

        debug!("成功入队上游域名: {} (优先级: {})", domain, priority.unwrap_or(100));
        Ok(true)
    }

    /// 出队（获取下一个待处理的域名）
    pub async fn dequeue(&self) -> ProxyResult<Option<UpstreamQueueItem>> {
        let mut queue = self.queue_cache.write().await;
        
        // 找到第一个需要处理的项
        let pos = queue
            .iter()
            .position(|item| item.needs_processing());

        if let Some(index) = pos {
            let mut item = queue.remove(index).unwrap();
            item.mark_processing();
            
            // 更新数据库
            self.update_item_in_db(&item).await?;
            
            debug!("出队上游域名: {} (尝试次数: {})", item.domain, item.process_attempts);
            Ok(Some(item))
        } else {
            Ok(None)
        }
    }

    /// 查看队列头部（不移除）
    pub async fn peek(&self) -> ProxyResult<Option<UpstreamQueueItem>> {
        let queue = self.queue_cache.read().await;
        
        let item = queue
            .iter()
            .find(|item| item.needs_processing())
            .cloned();
        
        Ok(item)
    }

    /// 标记项目完成
    pub async fn mark_completed(&self, domain: &str, result: Option<String>) -> ProxyResult<bool> {
        let mut queue = self.queue_cache.write().await;
        
        if let Some(item) = queue.iter_mut().find(|item| item.domain == domain) {
            item.mark_completed(result);
            self.update_item_in_db(item).await?;
            
            debug!("标记域名处理完成: {}", domain);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// 标记项目失败
    pub async fn mark_failed(&self, domain: &str, error: String) -> ProxyResult<bool> {
        let mut queue = self.queue_cache.write().await;
        
        if let Some(item) = queue.iter_mut().find(|item| item.domain == domain) {
            item.mark_failed(error);
            
            // 如果可以重试，标记为重试状态
            if item.can_retry() {
                item.mark_retrying();
            }
            
            self.update_item_in_db(item).await?;
            
            debug!("标记域名处理失败: {} (可重试: {})", domain, item.can_retry());
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// 处理下一个上游域名
    pub async fn process_next_upstream<F, Fut>(&self, processor: F) -> ProxyResult<bool>
    where
        F: FnOnce(String) -> Fut,
        Fut: std::future::Future<Output = ProxyResult<String>>,
    {
        // 检查是否正在处理
        {
            let processing = self.is_processing.read().await;
            if *processing {
                debug!("队列正在处理中，跳过");
                return Ok(false);
            }
        }

        // 设置处理状态
        {
            let mut processing = self.is_processing.write().await;
            *processing = true;
        }

        let result = async {
            // 获取下一个待处理项
            if let Some(item) = self.dequeue().await? {
                let domain = item.domain.clone();
                
                debug!("开始处理上游域名: {}", domain);
                
                // 执行处理
                match processor(domain.clone()).await {
                    Ok(result) => {
                        self.mark_completed(&domain, Some(result)).await?;
                        info!("上游域名处理成功: {}", domain);
                        Ok(true)
                    }
                    Err(e) => {
                        let error_msg = e.to_string();
                        self.mark_failed(&domain, error_msg.clone()).await?;
                        warn!("上游域名处理失败: {} - {}", domain, error_msg);
                        Ok(false)
                    }
                }
            } else {
                debug!("队列中没有待处理的域名");
                Ok(false)
            }
        }.await;

        // 清除处理状态
        {
            let mut processing = self.is_processing.write().await;
            *processing = false;
        }

        result
    }

    /// 获取队列大小
    pub async fn size(&self) -> usize {
        let queue = self.queue_cache.read().await;
        queue.len()
    }

    /// 获取待处理项目数量
    pub async fn pending_count(&self) -> usize {
        let queue = self.queue_cache.read().await;
        queue.iter().filter(|item| item.needs_processing()).count()
    }

    /// 检查是否包含特定域名
    pub async fn contains_domain(&self, domain: &str) -> ProxyResult<bool> {
        let queue = self.queue_cache.read().await;
        Ok(queue.iter().any(|item| item.domain == domain))
    }

    /// 移除域名
    pub async fn remove_domain(&self, domain: &str) -> ProxyResult<bool> {
        let mut queue = self.queue_cache.write().await;
        
        if let Some(pos) = queue.iter().position(|item| item.domain == domain) {
            let item = queue.remove(pos).unwrap();
            
            // 从数据库删除
            if let Some(id) = item.id {
                let collection = self.queue_collection();
                collection
                    .delete_one(doc! { "_id": id }, None)
                    .await
                    .map_err(|e| ProxyError::database(&format!("删除队列项失败: {}", e)))?;
            }
            
            debug!("移除队列域名: {}", domain);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// 清空队列
    pub async fn clear(&self) -> ProxyResult<u64> {
        // 清空数据库
        let collection = self.queue_collection();
        let result = collection
            .delete_many(doc! {}, None)
            .await
            .map_err(|e| ProxyError::database(&format!("清空队列失败: {}", e)))?;

        // 清空内存队列
        {
            let mut queue = self.queue_cache.write().await;
            queue.clear();
        }

        // 重置计数器
        {
            let mut counter = self.position_counter.write().await;
            *counter = 0;
        }

        info!("清空队列，删除了 {} 个项目", result.deleted_count);
        Ok(result.deleted_count)
    }

    /// 获取队列统计信息
    pub async fn get_stats(&self) -> ProxyResult<Vec<UpstreamQueueStats>> {
        let queue = self.queue_cache.read().await;
        
        let stats: Vec<UpstreamQueueStats> = queue
            .iter()
            .map(|item| item.get_stats())
            .collect();
        
        Ok(stats)
    }

    /// 更新FIFO配置
    pub async fn update_config(&self, new_config: FifoQueueConfig) -> ProxyResult<()> {
        // 更新内存配置
        {
            let mut config = self.config.write().await;
            *config = new_config.clone();
        }
        
        // 更新数据库
        let collection = self.config_collection();
        let filter = doc! {};
        let update = doc! {
            "$set": mongodb::bson::to_document(&new_config)
                .map_err(|e| ProxyError::internal(&format!("序列化配置失败: {}", e)))?
        };
        
        collection
            .update_one(filter, update, None)
            .await
            .map_err(|e| ProxyError::database(&format!("更新FIFO配置失败: {}", e)))?;
        
        info!("更新FIFO队列配置");
        Ok(())
    }

    /// 获取FIFO配置
    pub async fn get_config(&self) -> FifoQueueConfig {
        let config = self.config.read().await;
        config.clone()
    }

    /// 更新数据库中的队列项
    async fn update_item_in_db(&self, item: &UpstreamQueueItem) -> ProxyResult<()> {
        if let Some(id) = item.id {
            let collection = self.queue_collection();
            let filter = doc! { "_id": id };
            let update = doc! {
                "$set": mongodb::bson::to_document(item)
                    .map_err(|e| ProxyError::internal(&format!("序列化队列项失败: {}", e)))?
            };

            collection
                .update_one(filter, update, None)
                .await
                .map_err(|e| ProxyError::database(&format!("更新队列项失败: {}", e)))?;
        }
        Ok(())
    }

    /// 从数据库加载队列
    async fn load_queue_from_db(&self) -> ProxyResult<()> {
        let collection = self.queue_collection();

        let cursor = collection
            .find(doc! {}, None)
            .await
            .map_err(|e| ProxyError::database(&format!("查询队列失败: {}", e)))?;

        let items: Vec<UpstreamQueueItem> = cursor
            .try_collect()
            .await
            .map_err(|e| ProxyError::database(&format!("收集队列项失败: {}", e)))?;

        // 按优先级和队列位置排序
        let mut sorted_items = items;
        sorted_items.sort_by(|a, b| {
            a.priority.cmp(&b.priority)
                .then(a.queue_position.cmp(&b.queue_position))
        });

        // 更新内存队列
        {
            let mut queue = self.queue_cache.write().await;
            queue.clear();
            for item in sorted_items {
                queue.push_back(item);
            }
        }

        // 更新位置计数器
        {
            let queue = self.queue_cache.read().await;
            let max_position = queue
                .iter()
                .map(|item| item.queue_position)
                .max()
                .unwrap_or(0);

            let mut counter = self.position_counter.write().await;
            *counter = max_position;
        }

        let queue_size = {
            let queue = self.queue_cache.read().await;
            queue.len()
        };

        debug!("从数据库加载了 {} 个队列项", queue_size);
        Ok(())
    }

    /// 加载配置
    async fn load_config(&self) -> ProxyResult<()> {
        let collection = self.config_collection();

        if let Some(config) = collection
            .find_one(doc! {}, None)
            .await
            .map_err(|e| ProxyError::database(&format!("查询FIFO配置失败: {}", e)))?
        {
            let mut config_guard = self.config.write().await;
            *config_guard = config;
        } else {
            // 如果没有配置，创建默认配置
            let default_config = FifoQueueConfig::default();
            collection
                .insert_one(&default_config, None)
                .await
                .map_err(|e| ProxyError::database(&format!("创建默认FIFO配置失败: {}", e)))?;
        }

        Ok(())
    }

    /// 创建数据库索引
    async fn create_indexes(&self) -> ProxyResult<()> {
        use mongodb::options::IndexOptions;
        use mongodb::IndexModel;

        let queue_collection = self.queue_collection();

        let indexes = vec![
            // 域名唯一索引
            IndexModel::builder()
                .keys(doc! { "domain": 1 })
                .options(IndexOptions::builder().unique(true).build())
                .build(),
            // 状态和优先级复合索引
            IndexModel::builder()
                .keys(doc! { "status": 1, "priority": 1, "queue_position": 1 })
                .build(),
            // 启用状态索引
            IndexModel::builder()
                .keys(doc! { "enabled": 1 })
                .build(),
        ];

        queue_collection
            .create_indexes(indexes, None)
            .await
            .map_err(|e| ProxyError::database(&format!("创建队列索引失败: {}", e)))?;

        debug!("FIFO队列索引创建完成");
        Ok(())
    }

    /// 获取队列集合
    fn queue_collection(&self) -> mongodb::Collection<UpstreamQueueItem> {
        self.db.collection("upstream_queue")
    }

    /// 获取配置集合
    fn config_collection(&self) -> mongodb::Collection<FifoQueueConfig> {
        self.db.collection("fifo_queue_config")
    }
}
