//! 统一映射策略测试验证
//! 
//! 验证用户核心需求：
//! - A.com/page1 首次发现 common.com 创建映射
//! - B.com/page2 后续发现 common.com 复用映射
//! - 所有后续发现都按此模式处理

#[cfg(test)]
mod tests {
    use crate::domains::{DomainPoolService, models::*};
    use crate::recursive_proxy::{RecursiveProxyService, context::RecursiveContext};
    use std::collections::HashMap;
    use tokio;

    /// 创建测试用的域名池服务
    async fn create_test_domain_pool() -> DomainPoolService {
        // 为了测试编译，创建一个简化的实现
        // 实际测试中需要使用真实的数据库连接
        use crate::domains::repository::DomainRepository;
        use std::sync::Arc;

        // 注意：这里需要真实的MongoDB连接来运行测试
        // 在CI/CD环境中应该使用测试数据库
        let connection_string = std::env::var("TEST_MONGODB_URL")
            .unwrap_or_else(|_| "mongodb://localhost:27017/sm_proxy_test".to_string());

        let repository = Arc::new(DomainRepository::new(&connection_string).await.unwrap());
        DomainPoolService::new(repository).await.unwrap()
    }

    /// 创建测试用的递归代理服务
    async fn create_test_recursive_service() -> RecursiveProxyService {
        // 为了测试编译，创建一个简化的实现
        // 实际测试中需要完整的服务配置
        use crate::recursive_proxy::config::RecursiveConfig;

        let config = RecursiveConfig::default();
        let domain_pool = create_test_domain_pool().await;

        RecursiveProxyService::new(config, domain_pool).await.unwrap()
    }

    #[tokio::test]
    async fn test_first_discovery_creates_mapping() {
        // 测试首次发现创建映射
        let domain_pool = create_test_domain_pool().await;
        
        // 模拟A.com/page1首次发现common.com
        let upstream_domain = "common.com";
        let parent_url = "https://A.com/page1";
        let discovery_method = "HTML";
        let session_id = "test_session_1";
        let depth_level = 1;

        // 执行发现处理
        let result = domain_pool.handle_discovered_domain(
            upstream_domain,
            parent_url,
            discovery_method,
            session_id,
            depth_level,
        ).await;

        assert!(result.is_ok(), "首次发现应该成功创建映射");
        let downstream_domain = result.unwrap();
        
        // 验证映射已创建
        let mapping = domain_pool.check_existing_mapping_first(upstream_domain).await;
        assert!(mapping.is_ok(), "应该能够查询到映射");
        assert!(mapping.unwrap().is_some(), "映射应该存在");
        
        println!("✅ 首次发现测试通过: {} -> {}", upstream_domain, downstream_domain);
    }

    #[tokio::test]
    async fn test_subsequent_discovery_reuses_mapping() {
        // 测试后续发现复用映射
        let domain_pool = create_test_domain_pool().await;
        
        let upstream_domain = "common.com";
        
        // 第一次发现 (A.com/page1)
        let first_result = domain_pool.handle_discovered_domain(
            upstream_domain,
            "https://A.com/page1",
            "HTML",
            "session_1",
            1,
        ).await.unwrap();

        // 第二次发现 (B.com/page2)
        let second_result = domain_pool.handle_discovered_domain(
            upstream_domain,
            "https://B.com/page2",
            "HTML",
            "session_2",
            1,
        ).await.unwrap();

        // 验证两次发现使用相同的下游域名
        assert_eq!(first_result, second_result, "后续发现应该复用相同的映射");
        
        // 验证发现次数增加
        let stats = domain_pool.get_mapping_discovery_stats(upstream_domain).await.unwrap();
        assert!(stats.is_some(), "应该有发现统计");
        let stats = stats.unwrap();
        assert_eq!(stats.total_discoveries, 2, "应该记录2次发现");
        assert_eq!(stats.unique_parent_urls, 2, "应该有2个不同的父页面");
        
        println!("✅ 后续发现复用测试通过: 两次发现都使用 {}", first_result);
    }

    #[tokio::test]
    async fn test_global_content_replacement() {
        // 测试全局内容替换
        let service = create_test_recursive_service().await;
        
        // 准备测试内容
        let html_content = r#"
            <html>
                <head>
                    <link rel="stylesheet" href="https://common.com/style.css">
                    <script src="https://common.com/script.js"></script>
                </head>
                <body>
                    <img src="https://common.com/image.png">
                    <a href="https://common.com/page">Link</a>
                </body>
            </html>
        "#;

        // 创建映射关系
        let mut mappings = HashMap::new();
        mappings.insert("common.com".to_string(), "sub1.downstream.com".to_string());

        // 执行内容替换
        let result = service.replace_domains_in_content(
            html_content.as_bytes(),
            &mappings,
            "text/html",
        ).await;

        assert!(result.is_ok(), "内容替换应该成功");
        let replaced_content = String::from_utf8_lossy(&result.unwrap());
        
        // 验证所有common.com都被替换为sub1.downstream.com
        assert!(!replaced_content.contains("common.com"), "不应该包含原始域名");
        assert!(replaced_content.contains("sub1.downstream.com"), "应该包含替换后的域名");
        
        // 验证替换的完整性
        let common_count = replaced_content.matches("sub1.downstream.com").count();
        assert_eq!(common_count, 4, "应该有4处替换");
        
        println!("✅ 全局内容替换测试通过: 所有引用都指向 sub1.downstream.com");
    }

    #[tokio::test]
    async fn test_user_scenario_complete_flow() {
        // 测试完整的用户场景
        let service = create_test_recursive_service().await;
        
        // 场景1: A.com/page1 首次发现 common.com/resource.js
        let mut context1 = RecursiveContext::new("session_1".to_string(), 0);
        
        let page1_content = r#"
            <html>
                <script src="https://common.com/resource.js"></script>
            </html>
        "#;

        // 提取和处理URL
        let discovered_urls1 = service.extract_and_process_urls(
            page1_content.as_bytes(),
            "text/html",
            "https://A.com/page1",
            &mut context1,
        ).await.unwrap();

        assert!(discovered_urls1.contains(&"common.com".to_string()), "应该发现common.com");
        
        // 获取映射关系
        let mapping1 = context1.mappings.get("common.com").cloned();
        assert!(mapping1.is_some(), "应该有映射关系");
        let downstream1 = mapping1.unwrap();

        // 场景2: B.com/page2 后续发现 common.com/resource.js
        let mut context2 = RecursiveContext::new("session_2".to_string(), 0);
        
        let page2_content = r#"
            <html>
                <script src="https://common.com/resource.js"></script>
            </html>
        "#;

        // 提取和处理URL
        let discovered_urls2 = service.extract_and_process_urls(
            page2_content.as_bytes(),
            "text/html",
            "https://B.com/page2",
            &mut context2,
        ).await.unwrap();

        assert!(discovered_urls2.contains(&"common.com".to_string()), "应该发现common.com");
        
        // 获取映射关系
        let mapping2 = context2.mappings.get("common.com").cloned();
        assert!(mapping2.is_some(), "应该有映射关系");
        let downstream2 = mapping2.unwrap();

        // 验证两个场景使用相同的下游域名
        assert_eq!(downstream1, downstream2, "两个场景应该使用相同的下游域名");

        // 验证内容替换的一致性
        let replaced1 = service.replace_domains_in_content(
            page1_content.as_bytes(),
            &context1.mappings,
            "text/html",
        ).await.unwrap();

        let replaced2 = service.replace_domains_in_content(
            page2_content.as_bytes(),
            &context2.mappings,
            "text/html",
        ).await.unwrap();

        let content1 = String::from_utf8_lossy(&replaced1);
        let content2 = String::from_utf8_lossy(&replaced2);

        assert!(content1.contains(&downstream1), "页面1应该包含正确的下游域名");
        assert!(content2.contains(&downstream2), "页面2应该包含正确的下游域名");
        assert!(!content1.contains("common.com"), "页面1不应该包含原始域名");
        assert!(!content2.contains("common.com"), "页面2不应该包含原始域名");

        println!("✅ 完整用户场景测试通过:");
        println!("   A.com/page1 -> common.com -> {}", downstream1);
        println!("   B.com/page2 -> common.com -> {}", downstream2);
        println!("   两者使用相同的下游域名: {}", downstream1 == downstream2);
    }

    #[tokio::test]
    async fn test_mapping_reuse_statistics() {
        // 测试映射复用统计
        let domain_pool = create_test_domain_pool().await;
        
        let upstream_domain = "example.com";
        
        // 多次发现同一域名
        for i in 1..=5 {
            let parent_url = format!("https://site{}.com/page", i);
            let session_id = format!("session_{}", i);
            
            domain_pool.handle_discovered_domain(
                upstream_domain,
                &parent_url,
                "HTML",
                &session_id,
                1,
            ).await.unwrap();
        }

        // 获取复用统计
        let reuse_stats = domain_pool.get_mapping_reuse_stats().await.unwrap();
        assert!(reuse_stats.total_mappings >= 1, "应该至少有1个映射");
        assert!(reuse_stats.total_discoveries >= 5, "应该至少有5次发现");
        assert!(reuse_stats.reuse_rate > 0.0, "复用率应该大于0");

        // 获取热门映射
        let hottest = domain_pool.get_hottest_mappings(10).await.unwrap();
        assert!(!hottest.is_empty(), "应该有热门映射");
        
        let example_mapping = hottest.iter().find(|(domain, _)| domain == upstream_domain);
        assert!(example_mapping.is_some(), "应该找到example.com的映射");
        assert_eq!(example_mapping.unwrap().1, 5, "应该有5次发现");

        println!("✅ 映射复用统计测试通过:");
        println!("   总映射数: {}", reuse_stats.total_mappings);
        println!("   总发现数: {}", reuse_stats.total_discoveries);
        println!("   复用率: {:.2}%", reuse_stats.reuse_rate * 100.0);
    }

    #[tokio::test]
    async fn test_concurrent_discovery_consistency() {
        // 测试并发发现的一致性
        let domain_pool = create_test_domain_pool().await;
        
        let upstream_domain = "concurrent.com";
        let mut handles = Vec::new();

        // 并发发现同一域名
        for i in 0..10 {
            let pool = domain_pool.clone();
            let domain = upstream_domain.to_string();
            let parent_url = format!("https://site{}.com/page", i);
            let session_id = format!("session_{}", i);
            
            let handle = tokio::spawn(async move {
                pool.handle_discovered_domain(
                    &domain,
                    &parent_url,
                    "HTML",
                    &session_id,
                    1,
                ).await
            });
            
            handles.push(handle);
        }

        // 等待所有任务完成
        let results: Vec<_> = futures::future::join_all(handles).await;
        
        // 验证所有结果都成功
        let downstream_domains: Vec<String> = results
            .into_iter()
            .map(|r| r.unwrap().unwrap())
            .collect();

        // 验证所有并发发现都使用相同的下游域名
        let first_downstream = &downstream_domains[0];
        for downstream in &downstream_domains {
            assert_eq!(downstream, first_downstream, "所有并发发现应该使用相同的下游域名");
        }

        // 验证发现次数正确
        let stats = domain_pool.get_mapping_discovery_stats(upstream_domain).await.unwrap();
        assert!(stats.is_some(), "应该有发现统计");
        let stats = stats.unwrap();
        assert_eq!(stats.total_discoveries, 10, "应该记录10次发现");

        println!("✅ 并发发现一致性测试通过: 10个并发请求都使用 {}", first_downstream);
    }

    #[tokio::test]
    async fn test_content_type_specific_replacement() {
        // 测试不同内容类型的替换
        let service = create_test_recursive_service().await;

        let mut mappings = HashMap::new();
        mappings.insert("cdn.example.com".to_string(), "cdn1.proxy.com".to_string());

        // 测试HTML内容替换
        let html_content = r#"<link href="https://cdn.example.com/style.css"><script src="https://cdn.example.com/app.js"></script>"#;
        let html_result = service.replace_domains_in_content(
            html_content.as_bytes(),
            &mappings,
            "text/html",
        ).await.unwrap();
        let html_replaced = String::from_utf8_lossy(&html_result);
        assert!(html_replaced.contains("cdn1.proxy.com"), "HTML中应该替换域名");
        assert!(!html_replaced.contains("cdn.example.com"), "HTML中不应该包含原始域名");

        // 测试CSS内容替换
        let css_content = r#"@import url('https://cdn.example.com/fonts.css'); background: url('https://cdn.example.com/bg.jpg');"#;
        let css_result = service.replace_domains_in_content(
            css_content.as_bytes(),
            &mappings,
            "text/css",
        ).await.unwrap();
        let css_replaced = String::from_utf8_lossy(&css_result);
        assert!(css_replaced.contains("cdn1.proxy.com"), "CSS中应该替换域名");
        assert!(!css_replaced.contains("cdn.example.com"), "CSS中不应该包含原始域名");

        // 测试JavaScript内容替换
        let js_content = r#"fetch('https://cdn.example.com/api/data'); const url = "https://cdn.example.com/config";"#;
        let js_result = service.replace_domains_in_content(
            js_content.as_bytes(),
            &mappings,
            "application/javascript",
        ).await.unwrap();
        let js_replaced = String::from_utf8_lossy(&js_result);
        assert!(js_replaced.contains("cdn1.proxy.com"), "JavaScript中应该替换域名");
        assert!(!js_replaced.contains("cdn.example.com"), "JavaScript中不应该包含原始域名");

        println!("✅ 内容类型特定替换测试通过: HTML, CSS, JavaScript 都正确替换");
    }

    #[tokio::test]
    async fn test_discovery_context_tracking() {
        // 测试发现上下文追踪
        let domain_pool = create_test_domain_pool().await;

        let upstream_domain = "tracking.com";

        // 从不同来源发现同一域名
        let discoveries = vec![
            ("https://site1.com/page1", "HTML", "session_1"),
            ("https://site2.com/page2", "CSS", "session_2"),
            ("https://site3.com/page3", "JavaScript", "session_3"),
        ];

        for (parent_url, method, session_id) in discoveries {
            domain_pool.handle_discovered_domain(
                upstream_domain,
                parent_url,
                method,
                session_id,
                1,
            ).await.unwrap();
        }

        // 获取发现历史
        let history = domain_pool.get_domain_discovery_history(upstream_domain).await.unwrap();
        assert!(history.is_some(), "应该有发现历史");

        let history = history.unwrap();
        assert_eq!(history.total_discoveries, 3, "应该有3次发现");
        assert_eq!(history.unique_parent_urls, 3, "应该有3个不同的父页面");
        assert_eq!(history.method_counts.len(), 3, "应该有3种不同的发现方法");
        assert_eq!(history.session_counts.len(), 3, "应该有3个不同的会话");

        // 验证父页面URL列表
        let parent_urls = domain_pool.get_all_parent_urls_for_domain(upstream_domain).await.unwrap();
        assert_eq!(parent_urls.len(), 3, "应该有3个父页面URL");
        assert!(parent_urls.contains(&"https://site1.com/page1".to_string()));
        assert!(parent_urls.contains(&"https://site2.com/page2".to_string()));
        assert!(parent_urls.contains(&"https://site3.com/page3".to_string()));

        println!("✅ 发现上下文追踪测试通过:");
        println!("   总发现次数: {}", history.total_discoveries);
        println!("   唯一父页面: {}", history.unique_parent_urls);
        println!("   发现方法: {:?}", history.method_counts.keys().collect::<Vec<_>>());
    }

    #[tokio::test]
    async fn test_edge_cases_and_error_handling() {
        // 测试边界情况和错误处理
        let domain_pool = create_test_domain_pool().await;

        // 测试空域名
        let result = domain_pool.handle_discovered_domain(
            "",
            "https://test.com",
            "HTML",
            "session_1",
            1,
        ).await;
        assert!(result.is_err(), "空域名应该返回错误");

        // 测试无效域名
        let result = domain_pool.handle_discovered_domain(
            "invalid..domain",
            "https://test.com",
            "HTML",
            "session_1",
            1,
        ).await;
        assert!(result.is_err(), "无效域名应该返回错误");

        // 测试非常长的域名
        let long_domain = "a".repeat(300) + ".com";
        let result = domain_pool.handle_discovered_domain(
            &long_domain,
            "https://test.com",
            "HTML",
            "session_1",
            1,
        ).await;
        assert!(result.is_err(), "过长域名应该返回错误");

        // 测试查询不存在的域名统计
        let stats = domain_pool.get_mapping_discovery_stats("nonexistent.com").await.unwrap();
        assert!(stats.is_none(), "不存在的域名应该返回None");

        println!("✅ 边界情况和错误处理测试通过");
    }

    #[tokio::test]
    async fn test_performance_and_scalability() {
        // 测试性能和可扩展性
        let domain_pool = create_test_domain_pool().await;

        let start_time = std::time::Instant::now();
        let domain_count = 100;

        // 创建大量不同的域名映射
        for i in 0..domain_count {
            let upstream_domain = format!("domain{}.com", i);
            let parent_url = format!("https://site{}.com/page", i);
            let session_id = format!("session_{}", i);

            domain_pool.handle_discovered_domain(
                &upstream_domain,
                &parent_url,
                "HTML",
                &session_id,
                1,
            ).await.unwrap();
        }

        let creation_time = start_time.elapsed();

        // 测试查询性能
        let query_start = std::time::Instant::now();

        for i in 0..domain_count {
            let upstream_domain = format!("domain{}.com", i);
            let mapping = domain_pool.check_existing_mapping_first(&upstream_domain).await.unwrap();
            assert!(mapping.is_some(), "应该找到映射");
        }

        let query_time = query_start.elapsed();

        // 验证性能指标
        let avg_creation_time = creation_time.as_millis() as f64 / domain_count as f64;
        let avg_query_time = query_time.as_millis() as f64 / domain_count as f64;

        assert!(avg_creation_time < 100.0, "平均创建时间应该小于100ms");
        assert!(avg_query_time < 10.0, "平均查询时间应该小于10ms");

        // 获取复用统计
        let reuse_stats = domain_pool.get_mapping_reuse_stats().await.unwrap();
        assert_eq!(reuse_stats.total_mappings, domain_count as u32, "映射数量应该正确");

        println!("✅ 性能和可扩展性测试通过:");
        println!("   创建{}个映射耗时: {:?} (平均: {:.2}ms)", domain_count, creation_time, avg_creation_time);
        println!("   查询{}个映射耗时: {:?} (平均: {:.2}ms)", domain_count, query_time, avg_query_time);
    }

    // 辅助函数：验证映射一致性
    async fn verify_mapping_consistency(
        domain_pool: &DomainPoolService,
        upstream_domain: &str,
        expected_downstream: &str,
    ) -> bool {
        if let Ok(Some(mapping)) = domain_pool.check_existing_mapping_first(upstream_domain).await {
            mapping.downstream_domain == expected_downstream
        } else {
            false
        }
    }

    // 辅助函数：创建测试内容
    fn create_test_html_content(domains: &[&str]) -> String {
        let mut content = String::from("<html><head>");

        for domain in domains {
            content.push_str(&format!(
                r#"<link rel="stylesheet" href="https://{}/style.css">"#,
                domain
            ));
            content.push_str(&format!(
                r#"<script src="https://{}/script.js"></script>"#,
                domain
            ));
        }

        content.push_str("</head><body>");

        for domain in domains {
            content.push_str(&format!(
                r#"<img src="https://{}/image.png">"#,
                domain
            ));
        }

        content.push_str("</body></html>");
        content
    }
}
