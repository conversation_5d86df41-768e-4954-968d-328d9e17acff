//! 递归代理服务核心

use super::{
    cache::IntelligentCache,
    config::RecursiveConfig,
    context::{
        RecursiveContext, RecursiveResponse, RecursiveSession, RecursiveStatus, ResponseParams,
        StepParams,
    },
    filter::ContentFilter,
    proxy_client::ProxyClient,
};
use crate::domains::{DomainExtractor, ContentReplacer};
use crate::domains::service::DomainPoolService;
use crate::types::{ProxyError, ProxyResult};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::RwLock;
use tracing::info;

/// 递归代理服务
pub struct RecursiveProxyService {
    /// 配置
    config: Arc<RwLock<RecursiveConfig>>,
    /// 代理客户端
    proxy_client: Arc<ProxyClient>,
    /// 域名提取器
    domain_extractor: DomainExtractor,
    /// 内容替换器
    content_replacer: Arc<RwLock<ContentReplacer>>,
    /// 内容过滤器
    content_filter: Arc<RwLock<ContentFilter>>,
    /// 智能缓存
    cache: Arc<IntelligentCache>,
    /// 域名池服务
    domain_pool: Arc<DomainPoolService>,
    /// 活跃会话
    active_sessions: Arc<RwLock<HashMap<String, RecursiveSession>>>,
}

impl RecursiveProxyService {
    /// 创建新的递归代理服务
    pub async fn new(
        config: RecursiveConfig,
        domain_pool: Arc<DomainPoolService>,
    ) -> ProxyResult<Self> {
        let proxy_client = Arc::new(
            ProxyClient::new(config.proxy.clone(), config.browser.clone())
                .await
                .map_err(|e| ProxyError::internal(&e.to_string()))?,
        );
        let domain_extractor = DomainExtractor::new()
            .map_err(|e| ProxyError::internal(&e.to_string()))?;
        let content_replacer = Arc::new(RwLock::new(
            ContentReplacer::new(std::collections::HashMap::new())
                .map_err(|e| ProxyError::internal(&e.to_string()))?
        ));
        let content_filter = Arc::new(RwLock::new(
            ContentFilter::new(config.filter.clone())
                .map_err(|e| ProxyError::internal(&e.to_string()))?,
        ));
        let cache = Arc::new(IntelligentCache::new(config.cache.clone()));
        // 初始化缓存系统
        cache.init().await.map_err(|e| ProxyError::internal(&e.to_string()))?;
        // 启动缓存清理任务
        cache.start_cleanup_task().await;
        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            proxy_client,
            domain_extractor,
            content_replacer,
            content_filter,
            cache,
            domain_pool,
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    /// 执行递归代理请求
    pub async fn proxy_recursive(
        &self,
        url: &str,
        method: &str,
        headers: HashMap<String, String>,
        body: Option<Vec<u8>>,
        mut context: RecursiveContext,
    ) -> ProxyResult<RecursiveResponse> {
        let config = self.config.read().await;
        if !config.enabled {
            return Err(ProxyError::invalid_operation("递归代理功能已禁用"));
        }
        if !context.should_continue(config.max_depth, config.max_domains) {
            return Err(ProxyError::invalid_operation("达到递归限制"));
        }
        drop(config);
        let mut session = RecursiveSession::new(
            context.session_id.clone(),
            context.original_url.clone(),
            context.client_ip.clone(),
        );
        {
            let mut sessions = self.active_sessions.write().await;
            sessions.insert(context.session_id.clone(), session.clone());
        }
        let result = self
            .execute_recursive_request(url, method, headers, body, &mut context, &mut session)
            .await;
        match &result {
            Ok(response) => {
                session.complete(RecursiveStatus::Success, response.domains.len());
            }
            Err(e) => {
                let status = if e.to_string().contains("最大深度") {
                    RecursiveStatus::MaxDepthReached
                } else if e.to_string().contains("最大域名") {
                    RecursiveStatus::MaxDomainsReached
                } else {
                    RecursiveStatus::NetworkError(e.to_string())
                };
                session.complete(status, context.domains.len());
            }
        }
        {
            let mut sessions = self.active_sessions.write().await;
            sessions.remove(&context.session_id);
        }
        result
    }

    /// 执行递归请求的核心逻辑（增强版 - 集成所有新功能）
    async fn execute_recursive_request(
        &self,
        url: &str,
        method: &str,
        headers: HashMap<String, String>,
        body: Option<Vec<u8>>,
        context: &mut RecursiveContext,
        session: &mut RecursiveSession,
    ) -> ProxyResult<RecursiveResponse> {
        let start_time = Instant::now();
        let mut current_url = url.to_string();
        let mut redirect_count = 0;
        let config = self.config.read().await.clone();

        // 记录处理开始
        info!("开始处理递归请求: {} (会话: {})", current_url, context.session_id);

        // 跟踪重定向链
        loop {
            // 1. 检查映射关系缓存
            if let Some((downstream, upstream)) = self.extract_mapping_from_url(&current_url, context).await {
                if let Ok(Some(cached_content)) = self.cache.get_cached_content(&downstream, &upstream).await {
                    info!("使用映射关系缓存: {} -> {} (大小: {} 字节)", downstream, upstream, cached_content.len());

                    let step = context.create_step(StepParams {
                        url: current_url.clone(),
                        method: method.to_string(),
                        status_code: 200,
                        response_time_ms: 0,
                        from_cache: true,
                        discovered_count: 0,
                        error: None,
                        redirect_url: None,
                    });
                    session.add_step(step);

                    return Ok(RecursiveResponse::new(ResponseParams {
                        status_code: 200,
                        headers: HashMap::new(),
                        body: cached_content,
                        content_type: "text/html".to_string(),
                        from_cache: true,
                        chain: context.upstream_chain.clone(),
                        domains: context.domains.clone(),
                        processing_time_ms: start_time.elapsed().as_millis() as u64,
                        final_url: current_url,
                    }));
                }
            }

            // 2. 检查传统缓存
            let cache_key = format!("{}:{}", method, current_url);
            if let Some(cached_entry) = self.cache.get(&cache_key).await {
                info!("使用传统缓存响应: {}", current_url);

                let step = context.create_step(StepParams {
                    url: current_url.clone(),
                    method: method.to_string(),
                    status_code: 200,
                    response_time_ms: 0,
                    from_cache: true,
                    discovered_count: 0,
                    error: None,
                    redirect_url: None,
                });
                session.add_step(step);

                return Ok(RecursiveResponse::new(ResponseParams {
                    status_code: 200,
                    headers: HashMap::new(),
                    body: cached_entry.content,
                    content_type: cached_entry.content_type,
                    from_cache: true,
                    chain: context.upstream_chain.clone(),
                    domains: context.domains.clone(),
                    processing_time_ms: start_time.elapsed().as_millis() as u64,
                    final_url: current_url,
                }));
            }

            // 3. 发送请求（增强错误处理）
            let request_start = Instant::now();
            debug!("发送请求到: {} (方法: {})", current_url, method);

            let response = match self
                .proxy_client
                .request(method, &current_url, Some(headers.clone()), body.clone())
                .await
            {
                Ok(resp) => resp,
                Err(e) => {
                    let error_msg = format!("请求失败: {}", e);
                    error!("{}", error_msg);

                    // 记录失败步骤
                    let step = context.create_step(StepParams {
                        url: current_url.clone(),
                        method: method.to_string(),
                        status_code: 0,
                        response_time_ms: request_start.elapsed().as_millis() as u64,
                        from_cache: false,
                        discovered_count: 0,
                        error: Some(error_msg.clone()),
                        redirect_url: None,
                    });
                    session.add_step(step);

                    return Err(ProxyError::internal(&error_msg));
                }
            };

            let status_code = response.status().as_u16();
            let response_headers: HashMap<String, String> = response
                .headers()
                .iter()
                .map(|(k, v)| (k.to_string(), v.to_str().unwrap_or("").to_string()))
                .collect();

            let response_time = request_start.elapsed().as_millis() as u64;
            debug!("收到响应: {} (状态: {}, 耗时: {}ms)", current_url, status_code, response_time);

            // 检查是否需要跟踪重定向
            if config.trigger.follow_redirects && response.status().is_redirection() {
                if let Some(location) = response_headers
                    .get("location")
                    .or_else(|| response_headers.get("Location"))
                {
                    if redirect_count < config.trigger.max_redirects {
                        redirect_count += 1;

                        let step = context.create_step(StepParams {
                            url: current_url.clone(),
                            method: method.to_string(),
                            status_code,
                            response_time_ms: response_time,
                            from_cache: false,
                            discovered_count: 0,
                            error: None,
                            redirect_url: Some(location.clone()),
                        });
                        session.add_step(step);

                        current_url = self.resolve_redirect_url(&current_url, location)?;
                        context.add_to_chain(current_url.clone());
                        continue;
                    }
                }
            }

            // 4. 读取响应体（增强错误处理）
            let response_body = match response.bytes().await {
                Ok(bytes) => bytes.to_vec(),
                Err(e) => {
                    let error_msg = format!("读取响应体失败: {}", e);
                    error!("{}", error_msg);

                    let step = context.create_step(StepParams {
                        url: current_url.clone(),
                        method: method.to_string(),
                        status_code,
                        response_time_ms: response_time,
                        from_cache: false,
                        discovered_count: 0,
                        error: Some(error_msg.clone()),
                        redirect_url: None,
                    });
                    session.add_step(step);

                    return Err(ProxyError::internal(&error_msg));
                }
            };

            let content_type = response_headers
                .get("content-type")
                .or_else(|| response_headers.get("Content-Type"))
                .cloned()
                .unwrap_or_else(|| "text/html".to_string());

            debug!("响应体大小: {} 字节, 内容类型: {}", response_body.len(), content_type);

            // 5. 缓存响应（保持原有的内存缓存）
            if config.cache.enabled {
                if let Err(e) = self
                    .cache
                    .set(cache_key, response_body.clone(), content_type.clone())
                    .await
                {
                    warn!("缓存响应失败: {}", e);
                }
            }

            // 6. 检查是否应该触发递归处理
            if self.should_trigger_recursive(&config, status_code, &content_type) {
                debug!("触发递归处理: {} (状态: {}, 类型: {})", current_url, status_code, content_type);

                // 提取URL和域名（增强错误处理）
                let discovered_urls = match self
                    .extract_and_process_urls(&response_body, &content_type, &current_url, context)
                    .await
                {
                    Ok(urls) => urls,
                    Err(e) => {
                        warn!("提取和处理URL失败: {}", e);
                        // 继续处理，但不进行域名发现
                        Vec::new()
                    }
                };

                let step = context.create_step(StepParams {
                    url: current_url.clone(),
                    method: method.to_string(),
                    status_code,
                    response_time_ms: response_time,
                    from_cache: false,
                    discovered_count: discovered_urls.len(),
                    error: None,
                    redirect_url: None,
                });
                session.add_step(step);

                // 7. 替换响应内容中的域名（增强错误处理）
                let modified_body = match self
                    .replace_domains_in_content(&response_body, &context.mappings, &content_type)
                    .await
                {
                    Ok(body) => body,
                    Err(e) => {
                        warn!("内容替换失败，使用原始内容: {}", e);
                        response_body.clone()
                    }
                };

                // 8. 缓存处理后的内容到映射关系缓存
                if let Some((downstream, upstream)) = self.extract_mapping_from_url(&current_url, &context).await {
                    debug!("缓存处理后内容: {} -> {} (大小: {} 字节)", downstream, upstream, modified_body.len());

                    if let Err(e) = self.cache.cache_processed_content(
                        &downstream,
                        &upstream,
                        &modified_body,
                        &content_type,
                    ).await {
                        warn!("缓存处理后内容失败: {}", e);
                    } else {
                        debug!("成功缓存处理后内容");
                    }
                } else {
                    debug!("未找到映射关系，跳过映射缓存");
                }

                let total_processing_time = start_time.elapsed().as_millis() as u64;
                info!(
                    "递归处理完成: {} (发现域名: {}, 映射数: {}, 总耗时: {}ms)",
                    current_url,
                    discovered_urls.len(),
                    context.mappings.len(),
                    total_processing_time
                );

                return Ok(RecursiveResponse::new(ResponseParams {
                    status_code,
                    headers: response_headers,
                    body: modified_body,
                    content_type,
                    from_cache: false,
                    chain: context.upstream_chain.clone(),
                    domains: context.domains.clone(),
                    processing_time_ms: total_processing_time,
                    final_url: current_url,
                }));
            } else {
                // 9. 非递归处理（直接返回响应）
                debug!("跳过递归处理: {} (状态: {}, 类型: {})", current_url, status_code, content_type);

                let step = context.create_step(StepParams {
                    url: current_url.clone(),
                    method: method.to_string(),
                    status_code,
                    response_time_ms: response_time,
                    from_cache: false,
                    discovered_count: 0,
                    error: None,
                    redirect_url: None,
                });
                session.add_step(step);

                let total_processing_time = start_time.elapsed().as_millis() as u64;
                info!(
                    "非递归处理完成: {} (状态: {}, 耗时: {}ms)",
                    current_url,
                    status_code,
                    total_processing_time
                );

                return Ok(RecursiveResponse::new(ResponseParams {
                    status_code,
                    headers: response_headers,
                    body: response_body,
                    content_type,
                    from_cache: false,
                    chain: context.upstream_chain.clone(),
                    domains: context.domains.clone(),
                    processing_time_ms: total_processing_time,
                    final_url: current_url,
                }));
            }
        }
    }

    /// 检查是否应该触发递归
    fn should_trigger_recursive(
        &self,
        config: &RecursiveConfig,
        status_code: u16,
        content_type: &str,
    ) -> bool {
        // 检查状态码
        if config.trigger.status_codes.contains(&status_code) {
            return true;
        }

        // 检查内容类型
        for trigger_type in &config.trigger.content_types {
            if content_type.contains(trigger_type) {
                return true;
            }
        }

        false
    }

    /// 提取和处理URL
    async fn extract_and_process_urls(
        &self,
        content: &[u8],
        content_type: &str,
        base_url: &str,
        context: &mut RecursiveContext,
    ) -> ProxyResult<Vec<String>> {
        let content_str = String::from_utf8_lossy(content);

        // 使用新的域名提取器
        let domains = if content_type.contains("text/html") {
            self.domain_extractor.extract_from_html(&content_str)
                .map_err(|e| ProxyError::internal(&e.to_string()))?
        } else if content_type.contains("text/css") {
            self.domain_extractor.extract_from_css(&content_str)
                .map_err(|e| ProxyError::internal(&e.to_string()))?
        } else if content_type.contains("application/json") {
            self.domain_extractor.extract_from_json(&content_str)
                .map_err(|e| ProxyError::internal(&e.to_string()))?
        } else {
            self.domain_extractor.extract_from_text(&content_str)
                .map_err(|e| ProxyError::internal(&e.to_string()))?
        };

        let domains: Vec<String> = domains.into_iter().collect();

        // 过滤域名
        let filter = self.content_filter.read().await;
        let filtered_domains = filter.filter_domains(domains);
        drop(filter);

        // 添加到域名池并创建映射
        for domain in &filtered_domains {
            if let Ok(added) = self.domain_pool.auto_add_upstream_domain(domain).await {
                if added {
                    context.add_domain(domain.clone());

                    // 自动配对已经在 auto_add_upstream_domain 中处理了
                    // 这里只需要获取生成的映射关系
                    if let Ok(mappings) = self.domain_pool.get_mappings_for_upstream(domain).await {
                        for mapping in mappings {
                            context.add_mapping(domain.clone(), mapping.downstream_domain);
                        }
                    }
                }
            }
        }

        // 触发FIFO处理（如果没有发现新域名）
        self.trigger_fifo_processing_if_needed(&filtered_domains, context).await?;

        Ok(filtered_domains)
    }

    /// 替换内容中的域名（增强版）
    async fn replace_domains_in_content(
        &self,
        content: &[u8],
        domain_mappings: &HashMap<String, String>,
        content_type: &str,
    ) -> ProxyResult<Vec<u8>> {
        let content_str = String::from_utf8_lossy(content);

        // 更新内容替换器的映射
        {
            let mut replacer = self.content_replacer.write().await;
            replacer.update_mappings(domain_mappings.clone())
                .map_err(|e| ProxyError::internal(&e.to_string()))?;
        }

        // 执行增强的替换
        let replacer = self.content_replacer.read().await;
        let modified_content = replacer.replace_by_content_type(&content_str, content_type)
            .map_err(|e| ProxyError::internal(&e.to_string()))?;

        // 验证替换结果
        if !replacer.validate_replacement(&content_str, &modified_content) {
            warn!("内容替换验证失败，使用原始内容");
            return Ok(content.to_vec());
        }

        // 获取替换统计信息
        let stats = replacer.get_replacement_stats(&content_str, &modified_content);
        debug!(
            "内容替换完成 - 原始大小: {}, 替换后大小: {}, 替换次数: {}, 使用映射: {}",
            stats.original_size, stats.replaced_size, stats.replacement_count, stats.mappings_used
        );

        Ok(modified_content.into_bytes())
    }

    /// 解析重定向URL
    fn resolve_redirect_url(&self, base_url: &str, location: &str) -> ProxyResult<String> {
        use url::Url;

        if location.starts_with("http://") || location.starts_with("https://") {
            Ok(location.to_string())
        } else {
            let base =
                Url::parse(base_url).map_err(|e| ProxyError::invalid_input(&e.to_string()))?;
            let resolved = base
                .join(location)
                .map_err(|e| ProxyError::invalid_input(&e.to_string()))?;
            Ok(resolved.to_string())
        }
    }

    /// 获取活跃会话
    pub async fn get_active_sessions(&self) -> HashMap<String, RecursiveSession> {
        self.active_sessions.read().await.clone()
    }

    /// 清理过期的活跃会话（防止内存泄漏）
    pub async fn cleanup_expired_sessions(&self) {
        let mut sessions = self.active_sessions.write().await;
        let now = chrono::Utc::now();
        let timeout_duration = chrono::Duration::hours(1); // 1小时超时

        sessions.retain(|_, session| {
            // 保留最近1小时内活跃的会话
            (now - session.start_time) < timeout_duration
        });

        if sessions.len() > 1000 {
            // 如果会话数量过多，保留最新的500个
            let mut session_vec: Vec<_> = sessions.drain().collect();
            session_vec.sort_by(|a, b| b.1.start_time.cmp(&a.1.start_time));
            session_vec.truncate(500);
            sessions.extend(session_vec);
        }
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: RecursiveConfig) {
        let mut config = self.config.write().await;
        *config = new_config;
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> RecursiveProxyStats {
        let sessions = self.active_sessions.read().await;
        let cache_stats = self.cache.get_stats().await;

        RecursiveProxyStats {
            active_sessions: sessions.len(),
            cache_hit_rate: cache_stats.hit_rate,
            total_cache_entries: cache_stats.entry_count,
            cache_size_mb: cache_stats.total_size / (1024 * 1024),
        }
    }

    /// 启动递归代理（API控制）
    pub async fn start(&self) -> ProxyResult<()> {
        let mut config = self.config.write().await;
        config.enabled = true;
        Ok(())
    }

    /// 停止递归代理（API控制）
    pub async fn stop(&self) -> ProxyResult<()> {
        let mut config = self.config.write().await;
        config.enabled = false;
        Ok(())
    }

    /// 从URL和上下文中提取映射关系
    async fn extract_mapping_from_url(
        &self,
        url: &str,
        context: &RecursiveContext,
    ) -> Option<(String, String)> {
        // 尝试从上下文的映射中找到对应关系
        for (upstream, downstream) in &context.mappings {
            if url.contains(upstream) {
                return Some((downstream.clone(), upstream.clone()));
            }
        }

        // 如果没有找到，尝试从URL解析
        if let Ok(parsed_url) = url::Url::parse(url) {
            if let Some(host) = parsed_url.host_str() {
                // 查询数据库中的映射关系
                if let Ok(mappings) = self.domain_pool.get_mappings_for_upstream(host).await {
                    if let Some(mapping) = mappings.first() {
                        return Some((mapping.downstream_domain.clone(), mapping.upstream_domain.clone()));
                    }
                }
            }
        }

        None
    }

    /// 更新父页面缓存中的URL引用
    pub async fn update_parent_cache_references(
        &self,
        parent_downstream: &str,
        parent_upstream: &str,
        old_url: &str,
        new_url: &str,
    ) -> ProxyResult<()> {
        self.cache
            .update_parent_cache(parent_downstream, parent_upstream, old_url, new_url)
            .await
            .map_err(|e| ProxyError::internal(&format!("更新父页面缓存失败: {}", e)))
    }

    /// 获取缓存的处理后内容
    pub async fn get_cached_processed_content(
        &self,
        downstream_domain: &str,
        upstream_domain: &str,
    ) -> ProxyResult<Option<Vec<u8>>> {
        self.cache
            .get_cached_content(downstream_domain, upstream_domain)
            .await
            .map_err(|e| ProxyError::internal(&format!("获取缓存内容失败: {}", e)))
    }

    /// 清理映射缓存
    pub async fn cleanup_mapping_cache(
        &self,
        downstream_domain: &str,
        upstream_domain: &str,
    ) -> ProxyResult<()> {
        self.cache
            .cleanup_mapping_cache(downstream_domain, upstream_domain)
            .await
            .map_err(|e| ProxyError::internal(&format!("清理映射缓存失败: {}", e)))
    }

    /// 获取映射缓存统计信息
    pub async fn get_mapping_cache_stats(&self) -> HashMap<String, super::cache::MappingCacheMetadata> {
        self.cache.get_mapping_cache_stats().await
    }

    /// 更新父页面缓存中的URL引用（增强版）
    pub async fn update_parent_cache_with_enhanced_replacer(
        &self,
        cache_dir_path: &str,
        old_url: &str,
        new_url: &str,
    ) -> ProxyResult<u32> {
        let replacer = self.content_replacer.read().await;

        let updated_count = replacer
            .update_cached_directory(cache_dir_path, old_url, new_url)
            .await
            .map_err(|e| ProxyError::internal(&format!("更新父页面缓存失败: {}", e)))?;

        info!("更新父页面缓存完成: {} (更新了 {} 个文件)", cache_dir_path, updated_count);
        Ok(updated_count)
    }

    /// 验证内容替换结果
    pub async fn validate_content_replacement(
        &self,
        original_content: &str,
        replaced_content: &str,
    ) -> bool {
        let replacer = self.content_replacer.read().await;
        replacer.validate_replacement(original_content, replaced_content)
    }

    /// 获取内容替换统计信息
    pub async fn get_content_replacement_stats(
        &self,
        original_content: &str,
        replaced_content: &str,
    ) -> super::super::domains::replacer::ReplacementStats {
        let replacer = self.content_replacer.read().await;
        replacer.get_replacement_stats(original_content, replaced_content)
    }

    /// 处理FIFO队列中的上游域名
    pub async fn process_fifo_queue(&self) -> ProxyResult<u32> {
        let config = self.config.read().await;
        if !config.enabled {
            return Err(ProxyError::invalid_operation("递归代理功能已禁用"));
        }

        let fifo_config = self.domain_pool.get_fifo_config().await;
        if !fifo_config.enabled {
            debug!("FIFO队列处理已禁用");
            return Ok(0);
        }

        let batch_size = fifo_config.batch_size;
        drop(config);

        let mut processed_count = 0;

        // 批量处理队列中的域名
        for _ in 0..batch_size {
            match self.domain_pool.process_next_upstream_domain().await {
                Ok(true) => {
                    processed_count += 1;
                    debug!("FIFO队列处理成功，已处理 {} 个域名", processed_count);
                }
                Ok(false) => {
                    debug!("FIFO队列中没有待处理的域名");
                    break;
                }
                Err(e) => {
                    warn!("FIFO队列处理失败: {}", e);
                    break;
                }
            }
        }

        if processed_count > 0 {
            info!("FIFO队列批量处理完成，处理了 {} 个上游域名", processed_count);
        }

        Ok(processed_count)
    }

    /// 当页面没有发现新URL时，触发FIFO处理
    pub async fn trigger_fifo_processing_if_needed(
        &self,
        discovered_urls: &[String],
        context: &RecursiveContext,
    ) -> ProxyResult<()> {
        // 如果没有发现新URL，触发FIFO处理
        if discovered_urls.is_empty() {
            debug!("当前页面没有发现新URL，触发FIFO队列处理");

            match self.process_fifo_queue().await {
                Ok(count) => {
                    if count > 0 {
                        info!("FIFO处理完成，处理了 {} 个上游域名", count);
                    }
                }
                Err(e) => {
                    warn!("FIFO处理失败: {}", e);
                }
            }
        } else {
            debug!("发现了 {} 个新URL，跳过FIFO处理", discovered_urls.len());
        }

        Ok(())
    }

    /// 添加域名到FIFO队列
    pub async fn add_domains_to_fifo_queue(
        &self,
        domains: Vec<String>,
        priority: Option<u32>,
    ) -> ProxyResult<u32> {
        self.domain_pool.batch_enqueue_upstream_domains(domains, priority).await
    }

    /// 获取FIFO队列状态
    pub async fn get_fifo_queue_status(&self) -> ProxyResult<FifoQueueStatus> {
        let stats = self.domain_pool.get_upstream_queue_stats().await?;
        let config = self.domain_pool.get_fifo_config().await;

        let total_count = stats.len();
        let pending_count = stats.iter().filter(|s| s.status == crate::domains::models::UpstreamProcessStatus::Pending).count();
        let processing_count = stats.iter().filter(|s| s.status == crate::domains::models::UpstreamProcessStatus::Processing).count();
        let completed_count = stats.iter().filter(|s| s.status == crate::domains::models::UpstreamProcessStatus::Completed).count();
        let failed_count = stats.iter().filter(|s| s.status == crate::domains::models::UpstreamProcessStatus::Failed).count();

        Ok(FifoQueueStatus {
            enabled: config.enabled,
            total_count,
            pending_count,
            processing_count,
            completed_count,
            failed_count,
            batch_size: config.batch_size,
            process_interval_seconds: config.process_interval_seconds,
        })
    }
}

/// FIFO队列状态信息
#[derive(Debug, Clone)]
pub struct FifoQueueStatus {
    pub enabled: bool,
    pub total_count: usize,
    pub pending_count: usize,
    pub processing_count: usize,
    pub completed_count: usize,
    pub failed_count: usize,
    pub batch_size: u32,
    pub process_interval_seconds: u64,
}

/// 递归代理统计信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct RecursiveProxyStats {
    pub active_sessions: usize,
    pub cache_hit_rate: f64,
    pub total_cache_entries: usize,
    pub cache_size_mb: usize,
}
