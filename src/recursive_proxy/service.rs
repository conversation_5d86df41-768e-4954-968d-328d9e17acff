//! 递归代理服务核心

use super::{
    cache::IntelligentCache,
    config::RecursiveConfig,
    context::{
        RecursiveContext, RecursiveResponse, RecursiveSession, RecursiveStatus, ResponseParams,
        StepParams,
    },
    filter::ContentFilter,
    proxy_client::ProxyClient,
};
use crate::domains::{DomainExtractor, ContentReplacer};
use crate::domains::service::DomainPoolService;
use crate::types::{ProxyError, ProxyResult};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::RwLock;
use tracing::info;

/// 递归代理服务
pub struct RecursiveProxyService {
    /// 配置
    config: Arc<RwLock<RecursiveConfig>>,
    /// 代理客户端
    proxy_client: Arc<ProxyClient>,
    /// 域名提取器
    domain_extractor: DomainExtractor,
    /// 内容替换器
    content_replacer: Arc<RwLock<ContentReplacer>>,
    /// 内容过滤器
    content_filter: Arc<RwLock<ContentFilter>>,
    /// 智能缓存
    cache: Arc<IntelligentCache>,
    /// 域名池服务
    domain_pool: Arc<DomainPoolService>,
    /// 活跃会话
    active_sessions: Arc<RwLock<HashMap<String, RecursiveSession>>>,
}

impl RecursiveProxyService {
    /// 创建新的递归代理服务
    pub async fn new(
        config: RecursiveConfig,
        domain_pool: Arc<DomainPoolService>,
    ) -> ProxyResult<Self> {
        let proxy_client = Arc::new(
            ProxyClient::new(config.proxy.clone(), config.browser.clone())
                .await
                .map_err(|e| ProxyError::internal(&e.to_string()))?,
        );
        let domain_extractor = DomainExtractor::new()
            .map_err(|e| ProxyError::internal(&e.to_string()))?;
        let content_replacer = Arc::new(RwLock::new(
            ContentReplacer::new(std::collections::HashMap::new())
                .map_err(|e| ProxyError::internal(&e.to_string()))?
        ));
        let content_filter = Arc::new(RwLock::new(
            ContentFilter::new(config.filter.clone())
                .map_err(|e| ProxyError::internal(&e.to_string()))?,
        ));
        let cache = Arc::new(IntelligentCache::new(config.cache.clone()));
        // 启动缓存清理任务
        cache.start_cleanup_task().await;
        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            proxy_client,
            domain_extractor,
            content_replacer,
            content_filter,
            cache,
            domain_pool,
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    /// 执行递归代理请求
    pub async fn proxy_recursive(
        &self,
        url: &str,
        method: &str,
        headers: HashMap<String, String>,
        body: Option<Vec<u8>>,
        mut context: RecursiveContext,
    ) -> ProxyResult<RecursiveResponse> {
        let config = self.config.read().await;
        if !config.enabled {
            return Err(ProxyError::invalid_operation("递归代理功能已禁用"));
        }
        if !context.should_continue(config.max_depth, config.max_domains) {
            return Err(ProxyError::invalid_operation("达到递归限制"));
        }
        drop(config);
        let mut session = RecursiveSession::new(
            context.session_id.clone(),
            context.original_url.clone(),
            context.client_ip.clone(),
        );
        {
            let mut sessions = self.active_sessions.write().await;
            sessions.insert(context.session_id.clone(), session.clone());
        }
        let result = self
            .execute_recursive_request(url, method, headers, body, &mut context, &mut session)
            .await;
        match &result {
            Ok(response) => {
                session.complete(RecursiveStatus::Success, response.domains.len());
            }
            Err(e) => {
                let status = if e.to_string().contains("最大深度") {
                    RecursiveStatus::MaxDepthReached
                } else if e.to_string().contains("最大域名") {
                    RecursiveStatus::MaxDomainsReached
                } else {
                    RecursiveStatus::NetworkError(e.to_string())
                };
                session.complete(status, context.domains.len());
            }
        }
        {
            let mut sessions = self.active_sessions.write().await;
            sessions.remove(&context.session_id);
        }
        result
    }

    /// 执行递归请求的核心逻辑
    async fn execute_recursive_request(
        &self,
        url: &str,
        method: &str,
        headers: HashMap<String, String>,
        body: Option<Vec<u8>>,
        context: &mut RecursiveContext,
        session: &mut RecursiveSession,
    ) -> ProxyResult<RecursiveResponse> {
        let start_time = Instant::now();
        let mut current_url = url.to_string();
        let mut redirect_count = 0;
        let config = self.config.read().await.clone();

        // 跟踪重定向链
        loop {
            // 检查缓存
            let cache_key = format!("{}:{}", method, current_url);
            if let Some(cached_entry) = self.cache.get(&cache_key).await {
                info!("使用缓存响应: {}", current_url);

                let step = context.create_step(StepParams {
                    url: current_url.clone(),
                    method: method.to_string(),
                    status_code: 200,
                    response_time_ms: 0,
                    from_cache: true,
                    discovered_count: 0,
                    error: None,
                    redirect_url: None,
                });
                session.add_step(step);

                return Ok(RecursiveResponse::new(ResponseParams {
                    status_code: 200,
                    headers: HashMap::new(),
                    body: cached_entry.content,
                    content_type: cached_entry.content_type,
                    from_cache: true,
                    chain: context.upstream_chain.clone(),
                    domains: context.domains.clone(),
                    processing_time_ms: start_time.elapsed().as_millis() as u64,
                    final_url: current_url,
                }));
            }

            // 发送请求
            let request_start = Instant::now();
            let response = self
                .proxy_client
                .request(method, &current_url, Some(headers.clone()), body.clone())
                .await
                .map_err(|e| ProxyError::internal(&e.to_string()))?;

            let status_code = response.status().as_u16();
            let response_headers: HashMap<String, String> = response
                .headers()
                .iter()
                .map(|(k, v)| (k.to_string(), v.to_str().unwrap_or("").to_string()))
                .collect();

            let response_time = request_start.elapsed().as_millis() as u64;

            // 检查是否需要跟踪重定向
            if config.trigger.follow_redirects && response.status().is_redirection() {
                if let Some(location) = response_headers
                    .get("location")
                    .or_else(|| response_headers.get("Location"))
                {
                    if redirect_count < config.trigger.max_redirects {
                        redirect_count += 1;

                        let step = context.create_step(StepParams {
                            url: current_url.clone(),
                            method: method.to_string(),
                            status_code,
                            response_time_ms: response_time,
                            from_cache: false,
                            discovered_count: 0,
                            error: None,
                            redirect_url: Some(location.clone()),
                        });
                        session.add_step(step);

                        current_url = self.resolve_redirect_url(&current_url, location)?;
                        context.add_to_chain(current_url.clone());
                        continue;
                    }
                }
            }

            // 读取响应体
            let response_body = response.bytes().await?.to_vec();
            let content_type = response_headers
                .get("content-type")
                .or_else(|| response_headers.get("Content-Type"))
                .cloned()
                .unwrap_or_else(|| "text/html".to_string());

            // 缓存响应
            if config.cache.enabled {
                let _ = self
                    .cache
                    .set(cache_key, response_body.clone(), content_type.clone())
                    .await;
            }

            // 检查是否应该触发递归
            if self.should_trigger_recursive(&config, status_code, &content_type) {
                // 提取URL和域名
                let discovered_urls = self
                    .extract_and_process_urls(&response_body, &content_type, &current_url, context)
                    .await?;

                let step = context.create_step(StepParams {
                    url: current_url.clone(),
                    method: method.to_string(),
                    status_code,
                    response_time_ms: response_time,
                    from_cache: false,
                    discovered_count: discovered_urls.len(),
                    error: None,
                    redirect_url: None,
                });
                session.add_step(step);

                // 替换响应内容中的域名
                let modified_body = self
                    .replace_domains_in_content(&response_body, &context.mappings)
                    .await?;

                return Ok(RecursiveResponse::new(ResponseParams {
                    status_code,
                    headers: response_headers,
                    body: modified_body,
                    content_type,
                    from_cache: false,
                    chain: context.upstream_chain.clone(),
                    domains: context.domains.clone(),
                    processing_time_ms: start_time.elapsed().as_millis() as u64,
                    final_url: current_url,
                }));
            } else {
                let step = context.create_step(StepParams {
                    url: current_url.clone(),
                    method: method.to_string(),
                    status_code,
                    response_time_ms: response_time,
                    from_cache: false,
                    discovered_count: 0,
                    error: None,
                    redirect_url: None,
                });
                session.add_step(step);

                return Ok(RecursiveResponse::new(ResponseParams {
                    status_code,
                    headers: response_headers,
                    body: response_body,
                    content_type,
                    from_cache: false,
                    chain: context.upstream_chain.clone(),
                    domains: context.domains.clone(),
                    processing_time_ms: start_time.elapsed().as_millis() as u64,
                    final_url: current_url,
                }));
            }
        }
    }

    /// 检查是否应该触发递归
    fn should_trigger_recursive(
        &self,
        config: &RecursiveConfig,
        status_code: u16,
        content_type: &str,
    ) -> bool {
        // 检查状态码
        if config.trigger.status_codes.contains(&status_code) {
            return true;
        }

        // 检查内容类型
        for trigger_type in &config.trigger.content_types {
            if content_type.contains(trigger_type) {
                return true;
            }
        }

        false
    }

    /// 提取和处理URL
    async fn extract_and_process_urls(
        &self,
        content: &[u8],
        content_type: &str,
        base_url: &str,
        context: &mut RecursiveContext,
    ) -> ProxyResult<Vec<String>> {
        let content_str = String::from_utf8_lossy(content);

        // 使用新的域名提取器
        let domains = if content_type.contains("text/html") {
            self.domain_extractor.extract_from_html(&content_str)
                .map_err(|e| ProxyError::internal(&e.to_string()))?
        } else if content_type.contains("text/css") {
            self.domain_extractor.extract_from_css(&content_str)
                .map_err(|e| ProxyError::internal(&e.to_string()))?
        } else if content_type.contains("application/json") {
            self.domain_extractor.extract_from_json(&content_str)
                .map_err(|e| ProxyError::internal(&e.to_string()))?
        } else {
            self.domain_extractor.extract_from_text(&content_str)
                .map_err(|e| ProxyError::internal(&e.to_string()))?
        };

        let domains: Vec<String> = domains.into_iter().collect();

        // 过滤域名
        let filter = self.content_filter.read().await;
        let filtered_domains = filter.filter_domains(domains);
        drop(filter);

        // 添加到域名池并创建映射
        for domain in &filtered_domains {
            if let Ok(added) = self.domain_pool.auto_add_upstream_domain(domain).await {
                if added {
                    context.add_domain(domain.clone());

                    // 获取对应的下游域名
                    // get_available_upstream 返回 Option<String>，不能用 ?，需先 .await，再 if let Some(mapping) = ...
                    let mapping_opt = self.domain_pool.get_available_upstream(domain).await;
                    if let Some(mapping) = mapping_opt {
                        // 这里需要获取下游域名，简化实现
                        let downstream = format!("proxy-{}.example.com", domain.replace(".", "-"));
                        context.add_mapping(domain.clone(), downstream);
                    }
                }
            }
        }

        Ok(filtered_domains)
    }

    /// 替换内容中的域名
    async fn replace_domains_in_content(
        &self,
        content: &[u8],
        domain_mappings: &HashMap<String, String>,
    ) -> ProxyResult<Vec<u8>> {
        let content_str = String::from_utf8_lossy(content);

        // 更新内容替换器的映射
        {
            let mut replacer = self.content_replacer.write().await;
            replacer.update_mappings(domain_mappings.clone())
                .map_err(|e| ProxyError::internal(&e.to_string()))?;
        }

        // 执行替换
        let replacer = self.content_replacer.read().await;
        let modified_content = replacer.replace_by_content_type(&content_str, "text/html")
            .map_err(|e| ProxyError::internal(&e.to_string()))?;

        Ok(modified_content.into_bytes())
    }

    /// 解析重定向URL
    fn resolve_redirect_url(&self, base_url: &str, location: &str) -> ProxyResult<String> {
        use url::Url;

        if location.starts_with("http://") || location.starts_with("https://") {
            Ok(location.to_string())
        } else {
            let base =
                Url::parse(base_url).map_err(|e| ProxyError::invalid_input(&e.to_string()))?;
            let resolved = base
                .join(location)
                .map_err(|e| ProxyError::invalid_input(&e.to_string()))?;
            Ok(resolved.to_string())
        }
    }

    /// 获取活跃会话
    pub async fn get_active_sessions(&self) -> HashMap<String, RecursiveSession> {
        self.active_sessions.read().await.clone()
    }

    /// 清理过期的活跃会话（防止内存泄漏）
    pub async fn cleanup_expired_sessions(&self) {
        let mut sessions = self.active_sessions.write().await;
        let now = chrono::Utc::now();
        let timeout_duration = chrono::Duration::hours(1); // 1小时超时

        sessions.retain(|_, session| {
            // 保留最近1小时内活跃的会话
            (now - session.start_time) < timeout_duration
        });

        if sessions.len() > 1000 {
            // 如果会话数量过多，保留最新的500个
            let mut session_vec: Vec<_> = sessions.drain().collect();
            session_vec.sort_by(|a, b| b.1.start_time.cmp(&a.1.start_time));
            session_vec.truncate(500);
            sessions.extend(session_vec);
        }
    }

    /// 更新配置
    pub async fn update_config(&self, new_config: RecursiveConfig) {
        let mut config = self.config.write().await;
        *config = new_config;
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> RecursiveProxyStats {
        let sessions = self.active_sessions.read().await;
        let cache_stats = self.cache.get_stats().await;

        RecursiveProxyStats {
            active_sessions: sessions.len(),
            cache_hit_rate: cache_stats.hit_rate,
            total_cache_entries: cache_stats.entry_count,
            cache_size_mb: cache_stats.total_size / (1024 * 1024),
        }
    }

    /// 启动递归代理（API控制）
    pub async fn start(&self) -> ProxyResult<()> {
        let mut config = self.config.write().await;
        config.enabled = true;
        Ok(())
    }

    /// 停止递归代理（API控制）
    pub async fn stop(&self) -> ProxyResult<()> {
        let mut config = self.config.write().await;
        config.enabled = false;
        Ok(())
    }
}

/// 递归代理统计信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct RecursiveProxyStats {
    pub active_sessions: usize,
    pub cache_hit_rate: f64,
    pub total_cache_entries: usize,
    pub cache_size_mb: usize,
}
