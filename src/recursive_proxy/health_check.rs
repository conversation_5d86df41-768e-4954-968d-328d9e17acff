//! 递归代理系统健康检查
//!
//! 提供完整的系统健康状态监控和诊断功能

use crate::domains::*;
use crate::recursive_proxy::service::RecursiveProxyService;
use crate::types::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, error, info, warn};

/// 系统健康检查器
pub struct SystemHealthChecker {
    recursive_service: Arc<RecursiveProxyService>,
}

impl SystemHealthChecker {
    /// 创建新的健康检查器
    pub fn new(recursive_service: Arc<RecursiveProxyService>) -> Self {
        Self {
            recursive_service,
        }
    }

    /// 执行完整的系统健康检查
    pub async fn check_system_health(&self) -> ProxyResult<SystemHealthReport> {
        info!("开始系统健康检查");
        
        let mut report = SystemHealthReport::new();
        
        // 1. 检查递归代理服务状态
        report.recursive_proxy = self.check_recursive_proxy_health().await?;
        
        // 2. 检查域名池服务状态
        report.domain_pool = self.check_domain_pool_health().await?;
        
        // 3. 检查FIFO队列状态
        report.fifo_queue = self.check_fifo_queue_health().await?;
        
        // 4. 检查配额管理状态
        report.quota_management = self.check_quota_management_health().await?;
        
        // 5. 检查缓存系统状态
        report.cache_system = self.check_cache_system_health().await?;
        
        // 6. 检查临时URL存储状态
        report.temp_url_storage = self.check_temp_url_storage_health().await?;
        
        // 7. 计算总体健康状态
        report.overall_status = self.calculate_overall_status(&report);
        
        info!("系统健康检查完成，总体状态: {:?}", report.overall_status);
        Ok(report)
    }

    /// 检查递归代理服务健康状态
    async fn check_recursive_proxy_health(&self) -> ProxyResult<ComponentHealth> {
        let mut health = ComponentHealth::new("递归代理服务");
        
        // 检查服务是否启用
        let config = self.recursive_service.get_config().await;
        if !config.enabled {
            health.add_warning("递归代理服务已禁用");
        }
        
        // 检查缓存状态
        let cache_stats = self.recursive_service.cache.get_stats().await;
        health.add_metric("缓存命中率", format!("{:.2}%", cache_stats.hit_rate * 100.0));
        health.add_metric("缓存大小", cache_stats.size.to_string());
        
        if cache_stats.hit_rate < 0.1 {
            health.add_warning("缓存命中率过低");
        }
        
        // 检查映射缓存统计
        let mapping_stats = self.recursive_service.get_mapping_cache_stats().await;
        health.add_metric("映射缓存数量", mapping_stats.len().to_string());
        
        health.status = HealthStatus::Healthy;
        Ok(health)
    }

    /// 检查域名池服务健康状态
    async fn check_domain_pool_health(&self) -> ProxyResult<ComponentHealth> {
        let mut health = ComponentHealth::new("域名池服务");
        
        // 检查上游域名数量
        let upstream_count = self.recursive_service.domain_pool.get_upstream_count().await?;
        health.add_metric("上游域名数量", upstream_count.to_string());
        
        if upstream_count == 0 {
            health.add_error("没有配置上游域名");
        }
        
        // 检查下游域名数量
        let downstream_count = self.recursive_service.domain_pool.get_downstream_count().await?;
        health.add_metric("下游域名数量", downstream_count.to_string());
        
        if downstream_count == 0 {
            health.add_error("没有配置下游域名");
        }
        
        // 检查映射关系数量
        let mapping_count = self.recursive_service.domain_pool.get_mapping_count().await?;
        health.add_metric("映射关系数量", mapping_count.to_string());
        
        health.status = if health.errors.is_empty() {
            HealthStatus::Healthy
        } else {
            HealthStatus::Unhealthy
        };
        
        Ok(health)
    }

    /// 检查FIFO队列健康状态
    async fn check_fifo_queue_health(&self) -> ProxyResult<ComponentHealth> {
        let mut health = ComponentHealth::new("FIFO队列");
        
        // 检查队列配置
        let config = self.recursive_service.domain_pool.get_fifo_config().await;
        if !config.enabled {
            health.add_warning("FIFO队列已禁用");
        }
        
        // 检查队列统计
        let stats = self.recursive_service.domain_pool.get_upstream_queue_stats().await?;
        let total_count = stats.len();
        let pending_count = stats.iter().filter(|s| s.status == UpstreamProcessStatus::Pending).count();
        let failed_count = stats.iter().filter(|s| s.status == UpstreamProcessStatus::Failed).count();
        
        health.add_metric("队列总数", total_count.to_string());
        health.add_metric("待处理数", pending_count.to_string());
        health.add_metric("失败数", failed_count.to_string());
        
        if failed_count > total_count / 2 {
            health.add_warning("队列失败率过高");
        }
        
        health.status = HealthStatus::Healthy;
        Ok(health)
    }

    /// 检查配额管理健康状态
    async fn check_quota_management_health(&self) -> ProxyResult<ComponentHealth> {
        let mut health = ComponentHealth::new("配额管理");
        
        // 检查全局配额配置
        let config = self.recursive_service.domain_pool.get_global_quota_config().await;
        if !config.enabled {
            health.add_warning("配额管理已禁用");
        }
        
        // 检查配额统计
        let stats = self.recursive_service.domain_pool.get_all_quota_stats().await?;
        let total_domains = stats.len();
        let full_domains = stats.iter().filter(|s| s.remaining_quota == 0).count();
        let warning_domains = stats.iter().filter(|s| s.usage_rate > 0.8).count();
        
        health.add_metric("管理域名数", total_domains.to_string());
        health.add_metric("配额已满域名", full_domains.to_string());
        health.add_metric("配额警告域名", warning_domains.to_string());
        
        if full_domains > total_domains / 2 {
            health.add_warning("过多域名配额已满");
        }
        
        health.status = HealthStatus::Healthy;
        Ok(health)
    }

    /// 检查缓存系统健康状态
    async fn check_cache_system_health(&self) -> ProxyResult<ComponentHealth> {
        let mut health = ComponentHealth::new("缓存系统");
        
        // 检查传统缓存
        let cache_stats = self.recursive_service.cache.get_stats().await;
        health.add_metric("缓存条目数", cache_stats.size.to_string());
        health.add_metric("缓存命中数", cache_stats.hits.to_string());
        health.add_metric("缓存未命中数", cache_stats.misses.to_string());
        
        // 检查映射缓存
        let mapping_cache = self.recursive_service.get_mapping_cache_stats().await;
        health.add_metric("映射缓存数", mapping_cache.len().to_string());
        
        // 计算总缓存大小
        let total_cache_size = cache_stats.size + mapping_cache.len();
        health.add_metric("总缓存大小", total_cache_size.to_string());
        
        health.status = HealthStatus::Healthy;
        Ok(health)
    }

    /// 检查临时URL存储健康状态
    async fn check_temp_url_storage_health(&self) -> ProxyResult<ComponentHealth> {
        let mut health = ComponentHealth::new("临时URL存储");
        
        // 检查临时URL统计
        let stats = self.recursive_service.domain_pool.get_temp_url_stats().await?;
        health.add_metric("总URL数", stats.total_count.to_string());
        health.add_metric("待处理数", stats.pending_count.to_string());
        health.add_metric("已完成数", stats.completed_count.to_string());
        health.add_metric("失败数", stats.failed_count.to_string());
        
        if stats.failed_count > stats.total_count / 2 {
            health.add_warning("临时URL失败率过高");
        }
        
        health.status = HealthStatus::Healthy;
        Ok(health)
    }

    /// 计算总体健康状态
    fn calculate_overall_status(&self, report: &SystemHealthReport) -> HealthStatus {
        let components = vec![
            &report.recursive_proxy,
            &report.domain_pool,
            &report.fifo_queue,
            &report.quota_management,
            &report.cache_system,
            &report.temp_url_storage,
        ];
        
        let unhealthy_count = components.iter().filter(|c| c.status == HealthStatus::Unhealthy).count();
        let warning_count = components.iter().filter(|c| c.status == HealthStatus::Warning).count();
        
        if unhealthy_count > 0 {
            HealthStatus::Unhealthy
        } else if warning_count > 2 {
            HealthStatus::Warning
        } else {
            HealthStatus::Healthy
        }
    }

    /// 执行快速健康检查
    pub async fn quick_health_check(&self) -> ProxyResult<bool> {
        // 检查关键组件是否正常工作
        let config = self.recursive_service.get_config().await;
        if !config.enabled {
            return Ok(false);
        }
        
        // 检查域名池是否有数据
        let upstream_count = self.recursive_service.domain_pool.get_upstream_count().await?;
        let downstream_count = self.recursive_service.domain_pool.get_downstream_count().await?;
        
        Ok(upstream_count > 0 && downstream_count > 0)
    }
}

/// 系统健康报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealthReport {
    pub overall_status: HealthStatus,
    pub recursive_proxy: ComponentHealth,
    pub domain_pool: ComponentHealth,
    pub fifo_queue: ComponentHealth,
    pub quota_management: ComponentHealth,
    pub cache_system: ComponentHealth,
    pub temp_url_storage: ComponentHealth,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl SystemHealthReport {
    fn new() -> Self {
        Self {
            overall_status: HealthStatus::Unknown,
            recursive_proxy: ComponentHealth::new("递归代理服务"),
            domain_pool: ComponentHealth::new("域名池服务"),
            fifo_queue: ComponentHealth::new("FIFO队列"),
            quota_management: ComponentHealth::new("配额管理"),
            cache_system: ComponentHealth::new("缓存系统"),
            temp_url_storage: ComponentHealth::new("临时URL存储"),
            timestamp: chrono::Utc::now(),
        }
    }
}

/// 组件健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentHealth {
    pub name: String,
    pub status: HealthStatus,
    pub metrics: HashMap<String, String>,
    pub warnings: Vec<String>,
    pub errors: Vec<String>,
}

impl ComponentHealth {
    fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            status: HealthStatus::Unknown,
            metrics: HashMap::new(),
            warnings: Vec::new(),
            errors: Vec::new(),
        }
    }
    
    fn add_metric(&mut self, key: &str, value: String) {
        self.metrics.insert(key.to_string(), value);
    }
    
    fn add_warning(&mut self, message: &str) {
        self.warnings.push(message.to_string());
        if self.status == HealthStatus::Healthy || self.status == HealthStatus::Unknown {
            self.status = HealthStatus::Warning;
        }
    }
    
    fn add_error(&mut self, message: &str) {
        self.errors.push(message.to_string());
        self.status = HealthStatus::Unhealthy;
    }
}

/// 健康状态枚举
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum HealthStatus {
    Healthy,
    Warning,
    Unhealthy,
    Unknown,
}
