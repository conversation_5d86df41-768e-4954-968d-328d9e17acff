//! 智能缓存系统 - 支持映射关系目录结构

use super::config::CacheConfig;
use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::fs;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

/// 缓存条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntry {
    /// 缓存内容
    pub content: Vec<u8>,
    /// 内容类型
    pub content_type: String,
    /// 创建时间
    pub created_at: u64,
    /// 过期时间
    pub expires_at: u64,
    /// 命中次数
    pub hit_count: u64,
    /// 最后访问时间
    pub last_accessed: u64,
    /// 内容大小（字节）
    pub size: usize,
}

/// 映射关系缓存元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MappingCacheMetadata {
    /// 下游域名
    pub downstream_domain: String,
    /// 上游域名
    pub upstream_domain: String,
    /// 缓存目录路径
    pub cache_dir: PathBuf,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后修改时间
    pub last_modified: DateTime<Utc>,
    /// 内容大小（字节）
    pub content_size: u64,
    /// 文件数量
    pub file_count: u32,
    /// 是否已处理（内容替换完成）
    pub is_processed: bool,
    /// 处理版本号
    pub version: u32,
}

/// 缓存文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheFileInfo {
    /// 文件路径（相对于缓存目录）
    pub relative_path: String,
    /// 文件大小
    pub size: u64,
    /// 内容类型
    pub content_type: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后修改时间
    pub last_modified: DateTime<Utc>,
    /// 是否已处理
    pub is_processed: bool,
}

/// 缓存统计信息
#[derive(Debug, Clone, Default)]
pub struct CacheStats {
    /// 总命中次数
    pub total_hits: u64,
    /// 总请求次数
    pub total_requests: u64,
    /// 当前缓存条目数
    pub entry_count: usize,
    /// 当前缓存大小（字节）
    pub total_size: usize,
    /// 命中率
    pub hit_rate: f64,
}

/// 智能缓存系统
pub struct IntelligentCache {
    /// 缓存存储（内存缓存）
    storage: Arc<RwLock<HashMap<String, CacheEntry>>>,
    /// 映射关系缓存元数据
    mapping_cache: Arc<RwLock<HashMap<String, MappingCacheMetadata>>>,
    /// 配置
    config: CacheConfig,
    /// 统计信息
    stats: Arc<RwLock<CacheStats>>,
    /// 最后清理时间
    last_cleanup: Arc<RwLock<Instant>>,
    /// 缓存根目录
    cache_root: PathBuf,
}

impl IntelligentCache {
    /// 创建新的智能缓存
    pub fn new(config: CacheConfig) -> Self {
        let cache_root = PathBuf::from("cache");
        Self {
            storage: Arc::new(RwLock::new(HashMap::new())),
            mapping_cache: Arc::new(RwLock::new(HashMap::new())),
            config,
            stats: Arc::new(RwLock::new(CacheStats::default())),
            last_cleanup: Arc::new(RwLock::new(Instant::now())),
            cache_root,
        }
    }

    /// 创建带自定义缓存目录的智能缓存
    pub fn with_cache_dir<P: AsRef<Path>>(config: CacheConfig, cache_dir: P) -> Self {
        let cache_root = cache_dir.as_ref().to_path_buf();
        Self {
            storage: Arc::new(RwLock::new(HashMap::new())),
            mapping_cache: Arc::new(RwLock::new(HashMap::new())),
            config,
            stats: Arc::new(RwLock::new(CacheStats::default())),
            last_cleanup: Arc::new(RwLock::new(Instant::now())),
            cache_root,
        }
    }

    /// 获取缓存内容
    pub async fn get(&self, key: &str) -> Option<CacheEntry> {
        if !self.config.enabled {
            return None;
        }

        let mut stats = self.stats.write().await;
        stats.total_requests += 1;

        let mut storage = self.storage.write().await;

        if let Some(entry) = storage.get_mut(key) {
            let now = self.current_timestamp();

            // 检查是否过期
            if entry.expires_at > now {
                entry.hit_count += 1;
                entry.last_accessed = now;
                stats.total_hits += 1;
                stats.hit_rate = stats.total_hits as f64 / stats.total_requests as f64;

                return Some(entry.clone());
            } else {
                // 移除过期条目
                storage.remove(key);
                stats.entry_count = storage.len();
                stats.total_size = storage.values().map(|e| e.size).sum();
            }
        }

        stats.hit_rate = stats.total_hits as f64 / stats.total_requests as f64;
        None
    }

    /// 设置缓存内容
    pub async fn set(&self, key: String, content: Vec<u8>, content_type: String) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let now = self.current_timestamp();
        let expires_at = now + self.config.ttl_seconds;
        let size = content.len();

        let entry = CacheEntry {
            content,
            content_type,
            created_at: now,
            expires_at,
            hit_count: 0,
            last_accessed: now,
            size,
        };

        let mut storage = self.storage.write().await;
        let mut stats = self.stats.write().await;

        // 检查缓存大小限制
        let max_size_bytes = self.config.max_size_mb * 1024 * 1024;
        let current_size = stats.total_size + size;

        if current_size > max_size_bytes as usize {
            // 触发智能清理
            drop(stats);
            drop(storage);
            self.intelligent_cleanup().await?;

            storage = self.storage.write().await;
            stats = self.stats.write().await;
        }

        storage.insert(key, entry);
        stats.entry_count = storage.len();
        stats.total_size = storage.values().map(|e| e.size).sum();

        Ok(())
    }

    /// 智能清理缓存
    pub async fn intelligent_cleanup(&self) -> Result<()> {
        let mut storage = self.storage.write().await;
        let mut stats = self.stats.write().await;
        let now = self.current_timestamp();

        let mut entries_to_remove = Vec::new();

        // 1. 移除过期条目
        for (key, entry) in storage.iter() {
            if entry.expires_at <= now {
                entries_to_remove.push(key.clone());
            }
        }

        // 2. 移除低命中率条目
        let mut hit_rates: Vec<(String, f64)> = storage
            .iter()
            .filter(|(key, _)| !entries_to_remove.contains(key))
            .map(|(key, entry)| {
                let age_seconds = now.saturating_sub(entry.created_at);
                let hit_rate = if age_seconds > 0 {
                    entry.hit_count as f64 / age_seconds as f64
                } else {
                    0.0
                };
                (key.clone(), hit_rate)
            })
            .collect();

        hit_rates.sort_by(|a, b| a.1.partial_cmp(&b.1).unwrap_or(std::cmp::Ordering::Equal));

        // 移除命中率低于阈值的条目
        for (key, hit_rate) in hit_rates {
            if hit_rate < self.config.hit_rate_threshold {
                entries_to_remove.push(key);
            }
        }

        // 3. 如果仍然超过大小限制，移除最旧的条目
        let max_size_bytes = self.config.max_size_mb * 1024 * 1024;
        let mut current_size = storage.values().map(|e| e.size).sum::<usize>();

        while current_size > max_size_bytes as usize && !storage.is_empty() {
            if let Some((oldest_key, oldest_entry)) = storage
                .iter()
                .filter(|(key, _)| !entries_to_remove.contains(key))
                .min_by_key(|(_, entry)| entry.last_accessed)
                .map(|(k, v)| (k.clone(), v.clone()))
            {
                entries_to_remove.push(oldest_key);
                current_size = current_size.saturating_sub(oldest_entry.size);
            } else {
                break;
            }
        }

        // 执行清理
        for key in entries_to_remove {
            storage.remove(&key);
        }

        stats.entry_count = storage.len();
        stats.total_size = storage.values().map(|e| e.size).sum();

        *self.last_cleanup.write().await = Instant::now();

        tracing::info!(
            "缓存清理完成: {} 个条目, {} MB",
            stats.entry_count,
            stats.total_size / (1024 * 1024)
        );

        Ok(())
    }

    /// 性能优化：批量缓存预热
    pub async fn batch_warmup(cache: Arc<IntelligentCache>, urls: Vec<String>) -> Result<()> {
        if !cache.config.enabled || urls.is_empty() {
            return Ok(());
        }

        tracing::info!("开始批量缓存预热，预热{}个URL", urls.len());

        // 性能优化：并发预热，但限制并发数避免过载
        let semaphore = Arc::new(tokio::sync::Semaphore::new(3));
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(5))
            .build()?;

        let mut tasks = Vec::new();
        for url in urls {
            let permit = semaphore.clone();
            let client = client.clone();
            let cache_clone = cache.clone();

            let task = tokio::spawn(async move {
                let _permit = permit.acquire().await.unwrap();

                // 跳过已缓存的URL
                if cache_clone.get(&url).await.is_some() {
                    return;
                }

                // 预热请求
                if let Ok(response) = client.get(&url).send().await {
                    let content_type = response
                        .headers()
                        .get("content-type")
                        .and_then(|v| v.to_str().ok())
                        .unwrap_or("text/html")
                        .to_string();

                    if let Ok(content) = response.bytes().await {
                        let _ = cache_clone.set(url, content.to_vec(), content_type).await;
                    }
                }
            });

            tasks.push(task);
        }

        // 等待所有任务完成
        futures::future::join_all(tasks).await;
        tracing::info!("批量缓存预热完成");
        Ok(())
    }

    /// 定期清理任务
    pub async fn start_cleanup_task(&self) {
        let storage = self.storage.clone();
        let config = self.config.clone();
        let last_cleanup = self.last_cleanup.clone();

        tokio::spawn(async move {
            let mut interval =
                tokio::time::interval(Duration::from_secs(config.cleanup_interval_seconds));

            loop {
                interval.tick().await;

                let should_cleanup = {
                    let last = last_cleanup.read().await;
                    last.elapsed() >= Duration::from_secs(config.cleanup_interval_seconds)
                };

                if should_cleanup {
                    // 创建临时缓存实例进行清理
                    let temp_cache = IntelligentCache {
                        storage: storage.clone(),
                        config: config.clone(),
                        stats: Arc::new(RwLock::new(CacheStats::default())),
                        last_cleanup: last_cleanup.clone(),
                    };

                    if let Err(e) = temp_cache.intelligent_cleanup().await {
                        tracing::error!("缓存清理失败: {}", e);
                    }
                }
            }
        });
    }

    /// 获取缓存统计信息
    pub async fn get_stats(&self) -> CacheStats {
        self.stats.read().await.clone()
    }

    /// 清空所有缓存
    pub async fn clear_all(&self) {
        let mut storage = self.storage.write().await;
        let mut stats = self.stats.write().await;

        storage.clear();
        stats.entry_count = 0;
        stats.total_size = 0;
    }

    /// 获取当前时间戳
    fn current_timestamp(&self) -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }

    /// 检查缓存键是否存在
    pub async fn contains_key(&self, key: &str) -> bool {
        let storage = self.storage.read().await;
        storage.contains_key(key)
    }

    /// 移除指定缓存条目
    pub async fn remove(&self, key: &str) -> bool {
        let mut storage = self.storage.write().await;
        let mut stats = self.stats.write().await;

        if storage.remove(key).is_some() {
            stats.entry_count = storage.len();
            stats.total_size = storage.values().map(|e| e.size).sum();
            true
        } else {
            false
        }
    }

    /// 获取缓存键列表
    pub async fn get_keys(&self) -> Vec<String> {
        let storage = self.storage.read().await;
        storage.keys().cloned().collect()
    }

    // ==================== 映射关系缓存方法 ====================

    /// 生成缓存路径
    pub fn generate_cache_path(&self, downstream_domain: &str, upstream_domain: &str) -> PathBuf {
        let cache_dir_name = format!("{}_to_{}",
            downstream_domain.replace(".", "_"),
            upstream_domain.replace(".", "_")
        );
        self.cache_root.join(cache_dir_name)
    }

    /// 缓存处理后的内容
    pub async fn cache_processed_content(
        &self,
        downstream_domain: &str,
        upstream_domain: &str,
        content: &[u8],
        content_type: &str,
    ) -> Result<()> {
        let cache_dir = self.generate_cache_path(downstream_domain, upstream_domain);

        // 创建缓存目录
        fs::create_dir_all(&cache_dir).await?;

        // 写入主内容文件
        let content_file = cache_dir.join("index.html");
        fs::write(&content_file, content).await?;

        // 创建或更新元数据
        let metadata = MappingCacheMetadata {
            downstream_domain: downstream_domain.to_string(),
            upstream_domain: upstream_domain.to_string(),
            cache_dir: cache_dir.clone(),
            created_at: Utc::now(),
            last_modified: Utc::now(),
            content_size: content.len() as u64,
            file_count: 1,
            is_processed: true,
            version: 1,
        };

        // 写入元数据文件
        let metadata_file = cache_dir.join("metadata.json");
        let metadata_json = serde_json::to_string_pretty(&metadata)?;
        fs::write(&metadata_file, metadata_json).await?;

        // 更新内存缓存
        let cache_key = format!("{}_{}", downstream_domain, upstream_domain);
        {
            let mut mapping_cache = self.mapping_cache.write().await;
            mapping_cache.insert(cache_key, metadata);
        }

        debug!(
            "缓存处理后内容: {} -> {} (大小: {} 字节)",
            downstream_domain, upstream_domain, content.len()
        );

        Ok(())
    }

    /// 更新父页面缓存
    pub async fn update_parent_cache(
        &self,
        parent_downstream: &str,
        parent_upstream: &str,
        old_url: &str,
        new_url: &str,
    ) -> Result<()> {
        let cache_dir = self.generate_cache_path(parent_downstream, parent_upstream);
        let content_file = cache_dir.join("index.html");

        // 检查文件是否存在
        if !content_file.exists() {
            warn!("父页面缓存文件不存在: {:?}", content_file);
            return Ok(());
        }

        // 读取现有内容
        let mut content = fs::read_to_string(&content_file).await?;

        // 替换URL
        content = content.replace(old_url, new_url);

        // 写回文件
        fs::write(&content_file, content.as_bytes()).await?;

        // 更新元数据
        let cache_key = format!("{}_{}", parent_downstream, parent_upstream);
        {
            let mut mapping_cache = self.mapping_cache.write().await;
            if let Some(metadata) = mapping_cache.get_mut(&cache_key) {
                metadata.last_modified = Utc::now();
                metadata.version += 1;
                metadata.content_size = content.len() as u64;

                // 更新元数据文件
                let metadata_file = cache_dir.join("metadata.json");
                let metadata_json = serde_json::to_string_pretty(metadata)?;
                if let Err(e) = fs::write(&metadata_file, metadata_json).await {
                    warn!("更新元数据文件失败: {}", e);
                }
            }
        }

        debug!(
            "更新父页面缓存: {} -> {} (替换: {} -> {})",
            parent_downstream, parent_upstream, old_url, new_url
        );

        Ok(())
    }

    /// 获取缓存内容
    pub async fn get_cached_content(
        &self,
        downstream_domain: &str,
        upstream_domain: &str,
    ) -> Result<Option<Vec<u8>>> {
        let cache_dir = self.generate_cache_path(downstream_domain, upstream_domain);
        let content_file = cache_dir.join("index.html");

        if content_file.exists() {
            let content = fs::read(&content_file).await?;
            debug!(
                "读取缓存内容: {} -> {} (大小: {} 字节)",
                downstream_domain, upstream_domain, content.len()
            );
            Ok(Some(content))
        } else {
            Ok(None)
        }
    }

    /// 获取映射缓存元数据
    pub async fn get_mapping_metadata(
        &self,
        downstream_domain: &str,
        upstream_domain: &str,
    ) -> Option<MappingCacheMetadata> {
        let cache_key = format!("{}_{}", downstream_domain, upstream_domain);
        let mapping_cache = self.mapping_cache.read().await;
        mapping_cache.get(&cache_key).cloned()
    }

    /// 清理映射缓存
    pub async fn cleanup_mapping_cache(
        &self,
        downstream_domain: &str,
        upstream_domain: &str,
    ) -> Result<()> {
        let cache_dir = self.generate_cache_path(downstream_domain, upstream_domain);

        // 删除缓存目录
        if cache_dir.exists() {
            fs::remove_dir_all(&cache_dir).await?;
            debug!("删除缓存目录: {:?}", cache_dir);
        }

        // 从内存缓存中移除
        let cache_key = format!("{}_{}", downstream_domain, upstream_domain);
        {
            let mut mapping_cache = self.mapping_cache.write().await;
            mapping_cache.remove(&cache_key);
        }

        Ok(())
    }

    /// 获取所有映射缓存统计
    pub async fn get_mapping_cache_stats(&self) -> HashMap<String, MappingCacheMetadata> {
        let mapping_cache = self.mapping_cache.read().await;
        mapping_cache.clone()
    }

    /// 初始化缓存系统（加载现有缓存）
    pub async fn init(&self) -> Result<()> {
        // 创建缓存根目录
        fs::create_dir_all(&self.cache_root).await?;

        // 扫描现有缓存目录
        self.load_existing_cache().await?;

        info!("缓存系统初始化完成，缓存根目录: {:?}", self.cache_root);
        Ok(())
    }

    /// 加载现有缓存
    async fn load_existing_cache(&self) -> Result<()> {
        let mut entries = fs::read_dir(&self.cache_root).await?;
        let mut loaded_count = 0;

        while let Some(entry) = entries.next_entry().await? {
            if entry.file_type().await?.is_dir() {
                let dir_name = entry.file_name().to_string_lossy().to_string();
                let metadata_file = entry.path().join("metadata.json");

                if metadata_file.exists() {
                    match fs::read_to_string(&metadata_file).await {
                        Ok(metadata_json) => {
                            match serde_json::from_str::<MappingCacheMetadata>(&metadata_json) {
                                Ok(metadata) => {
                                    let cache_key = format!("{}_{}",
                                        metadata.downstream_domain,
                                        metadata.upstream_domain
                                    );
                                    let mut mapping_cache = self.mapping_cache.write().await;
                                    mapping_cache.insert(cache_key, metadata);
                                    loaded_count += 1;
                                }
                                Err(e) => {
                                    warn!("解析缓存元数据失败 {}: {}", dir_name, e);
                                }
                            }
                        }
                        Err(e) => {
                            warn!("读取缓存元数据失败 {}: {}", dir_name, e);
                        }
                    }
                }
            }
        }

        info!("加载了 {} 个映射缓存", loaded_count);
        Ok(())
    }
}
