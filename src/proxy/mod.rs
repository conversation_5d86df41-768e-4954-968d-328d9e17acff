//! 递归反向代理模块
//!
//! 实现自动蔓延反向代理功能，包括：
//! - 自动URL提取和域名发现
//! - 智能缓存和性能优化
//! - 第三方代理IP支持
//! - 浏览器指纹伪装
//! - 黑名单和过滤系统

pub mod cache;
pub mod config;
pub mod context;
pub mod filter;
pub mod proxy_client;
pub mod service;

// 重新导出主要类型
pub use config::RecursiveConfig;
pub use service::RecursiveProxyService;

use serde::{Deserialize, Serialize};

/// URL提取规则配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtractionRule {
    /// 规则名称
    pub name: String,
    /// 正则表达式模式
    pub pattern: String,
    /// 是否启用
    pub enabled: bool,
    /// 优先级
    pub priority: u32,
    /// 适用的内容类型
    pub content_types: Vec<String>,
}

impl Default for ExtractionRule {
    fn default() -> Self {
        Self {
            name: "default".to_string(),
            pattern: r"https?://[^\s]+".to_string(),
            enabled: true,
            priority: 1,
            content_types: vec!["text/html".to_string()],
        }
    }
}
