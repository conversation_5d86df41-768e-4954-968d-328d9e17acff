/// 自动化反向代理核心模块
/// 
/// 集成域名映射、SSL证书管理和内容替换功能
/// 实现完整的自动化反向代理流程

use std::sync::Arc;
use std::collections::HashMap;
use anyhow::{Result, anyhow};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;

use crate::domain_mapping::{DomainMappingManager, DomainMappingConfig};
use crate::ssl::{SslManager, SslManagerConfig};
use crate::domain_mapping::extractor::DomainExtractor;
use crate::domain_mapping::replacer::ContentReplacer;

/// 自动代理管理器
pub struct AutoProxyManager {
    /// 域名映射管理器
    domain_manager: Arc<DomainMappingManager>,
    /// SSL证书管理器
    ssl_manager: Arc<RwLock<SslManager>>,
    /// 域名提取器
    domain_extractor: DomainExtractor,
    /// 内容替换器
    content_replacer: Arc<RwLock<ContentReplacer>>,
    /// 配置
    config: AutoProxyConfig,
    /// 数据库连接
    db: Arc<mongodb::Database>,
}

/// 自动代理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoProxyConfig {
    /// 域名映射配置
    pub domain_mapping: DomainMappingConfig,
    /// SSL管理配置
    pub ssl_manager: SslManagerConfig,
    /// 是否启用自动SSL申请
    pub enable_auto_ssl: bool,
    /// 是否启用内容替换
    pub enable_content_replacement: bool,
    /// 递归代理配置
    pub recursive_config: RecursiveProxyConfig,
}

/// 递归代理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveProxyConfig {
    /// 是否启用递归代理
    pub enabled: bool,
    /// 递归深度
    pub max_depth: u32,
    /// 检查频率（分钟）
    pub check_interval_minutes: u64,
    /// 并发数量
    pub concurrency: u32,
    /// 超时时间（秒）
    pub timeout_seconds: u64,
    /// 内容大小阈值（KB）
    pub content_threshold_kb: u32,
}

impl Default for AutoProxyConfig {
    fn default() -> Self {
        Self {
            domain_mapping: DomainMappingConfig::default(),
            ssl_manager: SslManagerConfig::default(),
            enable_auto_ssl: true,
            enable_content_replacement: true,
            recursive_config: RecursiveProxyConfig {
                enabled: true,
                max_depth: 3,
                check_interval_minutes: 5,
                concurrency: 10,
                timeout_seconds: 30,
                content_threshold_kb: 300,
            },
        }
    }
}

/// 代理请求结果
#[derive(Debug, Clone)]
pub struct ProxyResult {
    /// 下游域名
    pub downstream_domain: String,
    /// 上游域名
    pub upstream_domain: String,
    /// 响应内容
    pub content: String,
    /// 内容类型
    pub content_type: String,
    /// 是否成功
    pub success: bool,
    /// 响应时间（毫秒）
    pub response_time_ms: u64,
    /// 内容大小（字节）
    pub content_size: usize,
    /// 发现的新域名
    pub discovered_domains: Vec<String>,
}

impl AutoProxyManager {
    /// 创建新的自动代理管理器
    pub async fn new(
        config: AutoProxyConfig,
        db: Arc<mongodb::Database>,
    ) -> Result<Self> {
        // 初始化域名映射管理器
        let domain_manager = Arc::new(
            DomainMappingManager::new(db.clone(), config.domain_mapping.clone()).await?
        );

        // 初始化SSL管理器
        let mut ssl_manager = SslManager::new(config.ssl_manager.clone(), db.clone()).await?;
        if config.enable_auto_ssl {
            ssl_manager.initialize_acme_client().await?;
        }
        let ssl_manager = Arc::new(RwLock::new(ssl_manager));

        // 初始化域名提取器
        let domain_extractor = DomainExtractor::new()?;

        // 初始化内容替换器
        let initial_mappings = HashMap::new(); // 将在运行时更新
        let content_replacer = Arc::new(RwLock::new(
            ContentReplacer::new(initial_mappings)?
        ));

        let manager = Self {
            domain_manager,
            ssl_manager,
            domain_extractor,
            content_replacer,
            config,
            db,
        };

        // 更新内容替换器的映射
        manager.update_content_replacer_mappings().await?;

        Ok(manager)
    }

    /// 处理代理请求
    pub async fn handle_proxy_request(
        &self,
        downstream_domain: &str,
        path: &str,
        method: &str,
        headers: &HashMap<String, String>,
        body: Option<&[u8]>,
    ) -> Result<ProxyResult> {
        let start_time = std::time::Instant::now();

        // 获取上游域名映射
        let upstream_domain = self.domain_manager
            .get_upstream_by_downstream(downstream_domain)
            .ok_or_else(|| anyhow!("未找到域名映射: {}", downstream_domain))?;

        tracing::info!("代理请求: {} -> {}{}", downstream_domain, upstream_domain, path);

        // 构建上游URL
        let upstream_url = format!("https://{}{}", upstream_domain, path);

        // 发送请求到上游
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(self.config.recursive_config.timeout_seconds))
            .build()?;

        let mut request = match method {
            "GET" => client.get(&upstream_url),
            "POST" => client.post(&upstream_url),
            "PUT" => client.put(&upstream_url),
            "DELETE" => client.delete(&upstream_url),
            _ => client.get(&upstream_url),
        };

        // 添加请求头
        for (key, value) in headers {
            if key.to_lowercase() != "host" {
                request = request.header(key, value);
            }
        }

        // 添加请求体
        if let Some(body) = body {
            request = request.body(body.to_vec());
        }

        // 发送请求
        let response = request.send().await?;
        let content_type = response.headers()
            .get("content-type")
            .and_then(|v| v.to_str().ok())
            .unwrap_or("text/html")
            .to_string();

        let content = response.text().await?;
        let content_size = content.len();
        let response_time_ms = start_time.elapsed().as_millis() as u64;

        // 提取新域名
        let discovered_domains = if self.config.enable_content_replacement {
            self.extract_domains_from_content(&content, &content_type).await?
        } else {
            Vec::new()
        };

        // 替换内容中的域名
        let processed_content = if self.config.enable_content_replacement {
            self.replace_content_domains(&content, &content_type).await?
        } else {
            content
        };

        // 更新映射统计
        self.domain_manager.update_mapping_stats(downstream_domain).await?;

        // 处理发现的新域名
        if !discovered_domains.is_empty() {
            self.process_discovered_domains(&discovered_domains).await?;
        }

        let result = ProxyResult {
            downstream_domain: downstream_domain.to_string(),
            upstream_domain,
            content: processed_content,
            content_type,
            success: true,
            response_time_ms,
            content_size,
            discovered_domains,
        };

        Ok(result)
    }

    /// 创建新的域名映射
    pub async fn create_domain_mapping(&self, upstream_domain: &str) -> Result<String> {
        // 获取或创建域名映射
        let downstream_domain = self.domain_manager
            .get_or_create_mapping(upstream_domain).await?;

        // 如果启用了自动SSL，申请证书
        if self.config.enable_auto_ssl {
            self.request_ssl_certificate_for_domain(&downstream_domain).await?;
        }

        // 更新内容替换器映射
        self.update_content_replacer_mappings().await?;

        Ok(downstream_domain)
    }

    /// 为域名申请SSL证书
    async fn request_ssl_certificate_for_domain(&self, domain: &str) -> Result<()> {
        // 提取根域名
        let root_domain = if let Some(dot_pos) = domain.find('.') {
            &domain[dot_pos + 1..]
        } else {
            domain
        };

        let ssl_manager = self.ssl_manager.read().await;
        
        // 检查是否已有有效证书
        if let Some(cert) = ssl_manager.get_certificate(root_domain) {
            if cert.status == crate::ssl::CertStatus::Valid &&
               cert.expires_at > Utc::now() + chrono::Duration::days(30) {
                tracing::info!("域名 {} 已有有效证书", root_domain);
                return Ok(());
            }
        }

        drop(ssl_manager);

        // 申请通配符证书
        let ssl_manager = self.ssl_manager.read().await;
        ssl_manager.request_wildcard_certificate(root_domain).await?;

        tracing::info!("为域名 {} 申请SSL证书成功", root_domain);
        Ok(())
    }

    /// 从内容中提取域名
    async fn extract_domains_from_content(
        &self,
        content: &str,
        content_type: &str,
    ) -> Result<Vec<String>> {
        let domains = if content_type.contains("text/html") {
            self.domain_extractor.extract_from_html(content)?
        } else if content_type.contains("text/css") {
            self.domain_extractor.extract_from_css(content)?
        } else if content_type.contains("application/json") {
            self.domain_extractor.extract_from_json(content)?
        } else {
            self.domain_extractor.extract_from_text(content)?
        };

        Ok(domains.into_iter().collect())
    }

    /// 替换内容中的域名
    async fn replace_content_domains(
        &self,
        content: &str,
        content_type: &str,
    ) -> Result<String> {
        let replacer = self.content_replacer.read().await;
        replacer.replace_by_content_type(content, content_type)
    }

    /// 处理发现的新域名
    async fn process_discovered_domains(&self, domains: &[String]) -> Result<()> {
        for domain in domains {
            // 检查是否已存在映射
            if self.domain_manager.get_downstream_by_upstream(domain).is_none() {
                // 创建新映射
                if let Err(e) = self.create_domain_mapping(domain).await {
                    tracing::warn!("创建域名映射失败: {} - {}", domain, e);
                }
            }
        }
        Ok(())
    }

    /// 更新内容替换器的映射
    async fn update_content_replacer_mappings(&self) -> Result<()> {
        let mappings = self.domain_manager.get_all_mappings();
        let mut mapping_dict = HashMap::new();

        for mapping in mappings {
            mapping_dict.insert(mapping.upstream_domain, mapping.downstream_domain);
        }

        let mut replacer = self.content_replacer.write().await;
        replacer.update_mappings(mapping_dict)?;

        Ok(())
    }

    /// 获取系统统计信息
    pub async fn get_statistics(&self) -> Result<AutoProxyStatistics> {
        let mappings = self.domain_manager.get_all_mappings();
        let ssl_manager = self.ssl_manager.read().await;
        let certificates = ssl_manager.get_all_certificates();

        let stats = AutoProxyStatistics {
            total_mappings: mappings.len(),
            active_mappings: mappings.iter().filter(|m| {
                matches!(m.status, crate::domain_mapping::MappingStatus::Active)
            }).count(),
            total_certificates: certificates.len(),
            valid_certificates: certificates.iter().filter(|c| {
                matches!(c.status, crate::ssl::CertStatus::Valid)
            }).count(),
            total_requests: mappings.iter().map(|m| m.request_count).sum(),
            recursive_enabled: self.config.recursive_config.enabled,
        };

        Ok(stats)
    }
}

/// 自动代理统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoProxyStatistics {
    pub total_mappings: usize,
    pub active_mappings: usize,
    pub total_certificates: usize,
    pub valid_certificates: usize,
    pub total_requests: u64,
    pub recursive_enabled: bool,
}
