use anyhow::{anyhow, Result};
use chrono::{DateTime, Duration as ChronoDuration, Utc};
use parking_lot::RwLock;
use rustls_acme::{caches::DirCache, AcmeConfig};
use serde::{Deserialize, Serialize};
/// SSL证书自动申请和管理模块
///
/// 核心功能：
/// 1. 通配符SSL证书自动申请
/// 2. 阿里云DNS API集成
/// 3. 证书自动续期
/// 4. 证书文件管理
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::Duration;
use tokio::fs;
use tokio::time::sleep;
use trust_dns_resolver::{config::*, TokioAsyncResolver};

pub mod acme_client;
pub mod cert_storage;
pub mod dns_provider;

/// SSL证书信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SslCertificate {
    pub domain: String,
    pub cert_path: String,
    pub key_path: String,
    pub created_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
    pub status: CertStatus,
    pub last_renewal_attempt: Option<DateTime<Utc>>,
    pub renewal_attempts: u32,
    pub acme_account_id: Option<String>,
}

/// 证书状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CertStatus {
    Pending,  // 申请中
    Valid,    // 有效
    Expired,  // 已过期
    Failed,   // 申请失败
    Renewing, // 续期中
}

/// SSL管理器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SslManagerConfig {
    pub cert_dir: String,
    pub acme_directory_url: String,
    pub contact_email: String,
    pub dns_provider: DnsProviderConfig,
    pub renewal_days_before_expiry: i64,
    pub max_renewal_attempts: u32,
    pub retry_interval_minutes: u64,
}

/// DNS提供商配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DnsProviderConfig {
    pub provider: String, // "aliyun", "cloudflare", etc.
    pub access_key_id: String,
    pub access_key_secret: String,
    pub region: Option<String>,
}

impl Default for SslManagerConfig {
    fn default() -> Self {
        Self {
            cert_dir: "certs".to_string(),
            acme_directory_url: "https://acme-v02.api.letsencrypt.org/directory".to_string(),
            contact_email: "<EMAIL>".to_string(),
            dns_provider: DnsProviderConfig {
                provider: "aliyun".to_string(),
                access_key_id: String::new(),
                access_key_secret: String::new(),
                region: Some("cn-hangzhou".to_string()),
            },
            renewal_days_before_expiry: 30,
            max_renewal_attempts: 3,
            retry_interval_minutes: 3,
        }
    }
}

/// SSL证书管理器
pub struct SslManager {
    config: SslManagerConfig,
    certificates: Arc<RwLock<HashMap<String, SslCertificate>>>,
    db: Arc<mongodb::Database>,
    dns_resolver: TokioAsyncResolver,
    acme_client: Option<Arc<acme_client::AcmeClient>>,
    renewal_tasks: Arc<RwLock<HashMap<String, tokio::task::JoinHandle<()>>>>,
}

impl SslManager {
    /// 创建新的SSL管理器
    pub async fn new(config: SslManagerConfig, db: Arc<mongodb::Database>) -> Result<Self> {
        // 创建证书目录
        fs::create_dir_all(&config.cert_dir).await?;

        // 初始化DNS解析器
        let dns_resolver =
            TokioAsyncResolver::tokio(ResolverConfig::default(), ResolverOpts::default());

        let manager = Self {
            config,
            certificates: Arc::new(RwLock::new(HashMap::new())),
            db,
            dns_resolver,
            acme_client: None,
            renewal_tasks: Arc::new(RwLock::new(HashMap::new())),
        };

        // 从数据库加载现有证书
        manager.load_certificates_from_db().await?;

        // 启动续期检查任务
        manager.start_renewal_checker().await;

        Ok(manager)
    }

    /// 初始化ACME客户端
    pub async fn initialize_acme_client(&mut self) -> Result<()> {
        let acme_client = acme_client::AcmeClient::new(
            &self.config.acme_directory_url,
            &self.config.contact_email,
            &self.config.cert_dir,
            self.config.dns_provider.clone(),
        )
        .await?;

        self.acme_client = Some(Arc::new(acme_client));
        tracing::info!("ACME客户端初始化成功");
        Ok(())
    }

    /// 申请通配符SSL证书
    pub async fn request_wildcard_certificate(&self, domain: &str) -> Result<SslCertificate> {
        let acme_client = self
            .acme_client
            .as_ref()
            .ok_or_else(|| anyhow!("ACME客户端未初始化"))?;

        tracing::info!("开始申请通配符证书: *.{}", domain);

        // 检查是否已存在有效证书
        if let Some(existing_cert) = self.get_certificate(domain) {
            if existing_cert.status == CertStatus::Valid
                && existing_cert.expires_at
                    > Utc::now() + ChronoDuration::days(self.config.renewal_days_before_expiry)
            {
                tracing::info!("域名 {} 已有有效证书，无需重新申请", domain);
                return Ok(existing_cert);
            }
        }

        // 更新证书状态为申请中
        let mut cert = SslCertificate {
            domain: domain.to_string(),
            cert_path: self.get_cert_path(domain),
            key_path: self.get_key_path(domain),
            created_at: Utc::now(),
            expires_at: Utc::now(), // 临时值，申请成功后更新
            status: CertStatus::Pending,
            last_renewal_attempt: Some(Utc::now()),
            renewal_attempts: 0,
            acme_account_id: None,
        };

        self.save_certificate(&cert).await?;

        // 申请证书
        match acme_client.request_wildcard_certificate(domain).await {
            Ok((cert_pem, key_pem, expires_at)) => {
                // 保存证书文件
                fs::write(&cert.cert_path, cert_pem).await?;
                fs::write(&cert.key_path, key_pem).await?;

                // 更新证书信息
                cert.expires_at = expires_at;
                cert.status = CertStatus::Valid;
                cert.created_at = Utc::now();

                self.save_certificate(&cert).await?;

                tracing::info!("通配符证书申请成功: *.{}", domain);
                Ok(cert)
            }
            Err(e) => {
                // 更新失败状态
                cert.status = CertStatus::Failed;
                cert.renewal_attempts += 1;
                self.save_certificate(&cert).await?;

                tracing::error!("通配符证书申请失败: *.{}, 错误: {}", domain, e);

                // 如果未达到最大重试次数，安排重试
                if cert.renewal_attempts < self.config.max_renewal_attempts {
                    self.schedule_retry(domain);
                }

                Err(e)
            }
        }
    }

    /// 续期证书
    pub async fn renew_certificate(&self, domain: &str) -> Result<()> {
        let mut cert = self
            .get_certificate(domain)
            .ok_or_else(|| anyhow!("证书不存在: {}", domain))?;

        tracing::info!("开始续期证书: *.{}", domain);

        cert.status = CertStatus::Renewing;
        cert.last_renewal_attempt = Some(Utc::now());
        cert.renewal_attempts += 1;

        self.save_certificate(&cert).await?;

        // 重新申请证书
        match self.request_wildcard_certificate(domain).await {
            Ok(_) => {
                tracing::info!("证书续期成功: *.{}", domain);
                Ok(())
            }
            Err(e) => {
                cert.status = CertStatus::Failed;
                self.save_certificate(&cert).await?;

                tracing::error!("证书续期失败: *.{}, 错误: {}", domain, e);
                Err(e)
            }
        }
    }

    /// 获取证书信息
    pub fn get_certificate(&self, domain: &str) -> Option<SslCertificate> {
        self.certificates.read().get(domain).cloned()
    }

    /// 获取所有证书
    pub fn get_all_certificates(&self) -> Vec<SslCertificate> {
        self.certificates.read().values().cloned().collect()
    }

    /// 检查证书是否需要续期
    pub fn needs_renewal(&self, domain: &str) -> bool {
        if let Some(cert) = self.get_certificate(domain) {
            if cert.status != CertStatus::Valid {
                return false;
            }

            let renewal_threshold =
                Utc::now() + ChronoDuration::days(self.config.renewal_days_before_expiry);
            cert.expires_at <= renewal_threshold
        } else {
            false
        }
    }

    /// 获取证书文件路径
    pub fn get_cert_path(&self, domain: &str) -> String {
        format!("{}/{}.pem", self.config.cert_dir, domain)
    }

    /// 获取私钥文件路径
    pub fn get_key_path(&self, domain: &str) -> String {
        format!("{}/{}.key", self.config.cert_dir, domain)
    }

    /// 从数据库加载证书信息
    async fn load_certificates_from_db(&self) -> Result<()> {
        use mongodb::bson::doc;

        let collection = self.db.collection::<SslCertificate>("ssl_certificates");
        let mut cursor = collection.find(doc! {}, None).await?;

        let mut certificates = self.certificates.write();

        while cursor.advance().await? {
            let cert = cursor.deserialize_current()?;
            certificates.insert(cert.domain.clone(), cert);
        }

        tracing::info!("从数据库加载了 {} 个SSL证书", certificates.len());
        Ok(())
    }

    /// 保存证书到数据库
    async fn save_certificate(&self, cert: &SslCertificate) -> Result<()> {
        use mongodb::bson::doc;
        use mongodb::options::ReplaceOptions;

        // 更新内存缓存
        self.certificates
            .write()
            .insert(cert.domain.clone(), cert.clone());

        // 保存到数据库
        let collection = self.db.collection::<SslCertificate>("ssl_certificates");
        let filter = doc! { "domain": &cert.domain };
        let options = ReplaceOptions::builder().upsert(true).build();

        collection.replace_one(filter, cert, options).await?;
        Ok(())
    }

    /// 启动续期检查任务
    async fn start_renewal_checker(&self) {
        let certificates = self.certificates.clone();
        let config = self.config.clone();
        let manager = self.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(3600)); // 每小时检查一次

            loop {
                interval.tick().await;

                let certs: Vec<String> = certificates
                    .read()
                    .iter()
                    .filter_map(|(domain, cert)| {
                        if cert.status == CertStatus::Valid {
                            let renewal_threshold = Utc::now()
                                + ChronoDuration::days(config.renewal_days_before_expiry);
                            if cert.expires_at <= renewal_threshold {
                                Some(domain.clone())
                            } else {
                                None
                            }
                        } else {
                            None
                        }
                    })
                    .collect();

                for domain in certs {
                    tracing::info!("检测到证书需要续期: {}", domain);
                    if let Err(e) = manager.renew_certificate(&domain).await {
                        tracing::error!("自动续期失败: {}, 错误: {}", domain, e);
                    }
                }
            }
        });
    }

    /// 安排重试任务
    fn schedule_retry(&self, domain: &str) {
        let domain_str = domain.to_string();
        let manager = self.clone();
        let retry_interval = Duration::from_secs(self.config.retry_interval_minutes * 60);

        let task = tokio::spawn(async move {
            sleep(retry_interval).await;

            tracing::info!("重试申请证书: {}", domain_str);
            if let Err(e) = manager.request_wildcard_certificate(&domain_str).await {
                tracing::error!("重试申请证书失败: {}, 错误: {}", domain_str, e);
            }
        });

        self.renewal_tasks.write().insert(domain.to_string(), task);
    }
}

// 实现Clone trait以支持在异步任务中使用
impl Clone for SslManager {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            certificates: self.certificates.clone(),
            db: self.db.clone(),
            dns_resolver: self.dns_resolver.clone(),
            acme_client: self.acme_client.clone(),
            renewal_tasks: self.renewal_tasks.clone(),
        }
    }
}
