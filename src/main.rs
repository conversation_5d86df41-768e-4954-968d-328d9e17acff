//! SM - 智能代理系统
//!
//! 基于Pingora的高性能反向代理系统，提供：
//! - Pingora反向代理核心 (端口1911)
//! - Web管理界面 (端口1319)
//! - 递归代理功能
//! - 域名池管理

#![allow(dead_code)]
#![allow(unused_imports)]
#![allow(unused_variables)]
#![allow(unused_assignments)]

use std::sync::Arc;
use tokio::signal;
use tracing::{error, info};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

// 导入核心模块
mod api;
mod auth;
// mod auto_proxy; // 将被删除，功能集成到proxy_manager
// mod config; // 已删除，使用 proxy-config 替代
mod db;
mod domains;
// mod domain_mapping; // 已整合到domains中
mod error;
// mod integrated_proxy; // 将被删除，功能集成到proxy_manager
mod proxy_manager;
mod middleware;
mod proxy;
mod security;
mod services;
mod ssl;
mod types;
mod utils;

// 导入Pingora相关
use crate::types::ProxyResult;
use crate::utils::setup_logging;
use proxy_core::ProxyServerBuilder;
use proxy_config::{load_config, ProxyConfig};

/// 系统配置
#[derive(Clone)]
pub struct SystemConfig {
    pub web_addr: String,
    pub proxy_addr: String,
    pub mongodb_uri: String,
}

impl Default for SystemConfig {
    fn default() -> Self {
        Self {
            web_addr: std::env::var("WEB_ADDR").unwrap_or_else(|_| "0.0.0.0:1319".to_string()),
            proxy_addr: std::env::var("PROXY_ADDR")
                .unwrap_or_else(|_| "127.0.0.1:1911".to_string()),
            mongodb_uri: std::env::var("MONGODB_URI")
                .unwrap_or_else(|_| "mongodb://localhost:27017/sm".to_string()),
        }
    }
}

impl From<ProxyConfig> for SystemConfig {
    fn from(config: ProxyConfig) -> Self {
        Self {
            web_addr: config.server.frontend_addr
                .unwrap_or_else(|| format!("{}:1319", config.server.listen)),
            proxy_addr: config.server.backend_addr
                .unwrap_or_else(|| format!("{}:1911", config.server.listen)),
            mongodb_uri: std::env::var("MONGODB_URI")
                .unwrap_or_else(|_| "mongodb://localhost:27017/sm_proxy".to_string()),
        }
    }
}

#[tokio::main]
async fn main() -> ProxyResult<()> {
    // 初始化日志
    setup_logging()?;

    info!("🚀 启动SM智能代理系统 (基于Pingora)...");

    // 加载配置 - 优先使用 proxy-config，回退到默认配置
    let config = match load_config("config/proxy.yaml").await {
        Ok(proxy_config) => {
            info!("✅ 使用 proxy-config 统一配置系统");
            SystemConfig::from(proxy_config)
        }
        Err(e) => {
            info!("⚠️ proxy-config 加载失败，使用默认配置: {}", e);
            SystemConfig::default()
        }
    };

    info!("📋 系统配置:");
    info!("  🌐 Web管理界面: http://{}", config.web_addr);
    info!("  🔄 Pingora代理: http://{}", config.proxy_addr);
    info!("  🗄️ MongoDB: {}", config.mongodb_uri);

    // 初始化数据库
    let database = init_database(&config.mongodb_uri).await?;
    info!("✅ 数据库连接成功");

    // 创建域名池服务 - 重新连接 MongoDB（简化实现）
    let mongo_client = mongodb::Client::with_uri_str(&config.mongodb_uri)
        .await
        .map_err(|e| anyhow::anyhow!("MongoDB客户端创建失败: {}", e))?;
    let mongo_db = mongo_client.database("sm");
    let domain_pool_repo = Arc::new(crate::domains::repository::DomainPoolRepository::new(
        mongo_db,
    ));
    let domain_pool_service = Arc::new(
        crate::domains::DomainPoolService::new(domain_pool_repo)
            .map_err(|e| anyhow::anyhow!("域名池服务创建失败: {}", e))?,
    );

    // 初始化域名池服务（创建数据库索引）
    if let Err(e) = domain_pool_service.init().await {
        tracing::warn!("域名池服务初始化失败: {}", e);
    }

    // 创建代理管理器
    let proxy_manager = Arc::new(proxy_manager::ProxyManager::new(
        domain_pool_service.clone(),
    ));

    // 初始化代理管理器
    if let Err(e) = proxy_manager.initialize().await {
        tracing::warn!("代理管理器初始化失败: {}", e);
    }

    // 启动Pingora反向代理服务 (端口1911)
    let proxy_handle = tokio::spawn(start_pingora_proxy(
        config.proxy_addr.clone(),
        proxy_manager.clone(),
    ));

    // 直接创建完整AppState（不使用适配器）
    let full_app_state = create_app_state_directly(
        database.clone(),
        domain_pool_service.clone(),
    ).await?;

    info!("✅ 完整AppState直接创建成功！包含所有功能：");
    info!("  🔧 性能监控: 已启用");
    info!("  🔄 递归代理: 已启用");
    info!("  📊 事件系统: 已启用");
    info!("  ⚙️ 配置管理: 已启用");

    // 启动Web管理界面 (端口1319) - 直接使用完整AppState
    let web_handle = tokio::spawn(start_web_interface_with_full_state(
        config.web_addr.clone(),
        full_app_state,
    ));

    info!("✅ 所有服务启动完成");
    info!("🛡️  安全架构 - 简单双服务方案:");
    info!("   📱 前端管理界面: 对外开放 ({})", config.web_addr);
    info!("   🔒 API服务: 仅内部访问 (127.0.0.1:1320)");
    info!("   🔄 Pingora代理服务: 仅内部访问 ({})", config.proxy_addr);
    info!("💡 通过防火墙确保API和代理端口不对外开放");

    // 等待信号或服务完成
    tokio::select! {
        result = proxy_handle => {
            match result {
                Ok(Ok(())) => info!("Pingora代理服务正常退出"),
                Ok(Err(e)) => error!("Pingora代理服务错误: {}", e),
                Err(e) => error!("Pingora代理任务错误: {}", e),
            }
        }
        result = web_handle => {
            match result {
                Ok(Ok(())) => info!("Web管理界面正常退出"),
                Ok(Err(e)) => error!("Web管理界面错误: {}", e),
                Err(e) => error!("Web管理任务错误: {}", e),
            }
        }
        _ = signal::ctrl_c() => {
            info!("收到退出信号，正在关闭服务...");
        }
    }

    info!("🛑 SM智能代理系统已停止");
    Ok(())
}



/// 初始化数据库连接
async fn init_database(mongodb_uri: &str) -> ProxyResult<Arc<dyn crate::types::Database>> {
    use services::database::DatabaseAdapter;

    // 从URI中提取数据库名称
    let db_name = mongodb_uri
        .split('/')
        .next_back()
        .unwrap_or("sm_proxy")
        .split('?')
        .next()
        .unwrap_or("sm_proxy");

    let database = DatabaseAdapter::new(mongodb_uri, db_name).await?;
    Ok(Arc::new(database))
}

// 旧的AppState创建函数已删除 - 现在使用create_app_state_directly

/// 直接创建完整AppState（不使用适配器）
async fn create_app_state_directly(
    database: Arc<dyn crate::types::Database>,
    domain_pool_service: Arc<crate::domains::DomainPoolService>,
) -> ProxyResult<Arc<crate::types::AppState>> {
    use crate::types::{AppState, PerfMonitor};
    use crate::auth::AuthService;
    use crate::proxy::service::RecursiveProxyService;
    use crate::proxy::config::RecursiveConfig;

    info!("🔧 直接创建完整AppState...");

    // 创建认证服务
    let auth_service = AuthService::new(database.clone());

    // 确保默认管理员用户存在
    if let Err(e) = auth_service.ensure_admin_user().await {
        tracing::warn!("创建默认管理员用户失败: {}", e);
    }

    // 创建性能监控器
    let perf_monitor = Arc::new(PerfMonitor::new());

    // 创建递归代理服务
    let recursive_config = RecursiveConfig::default();
    let recursive_proxy = Arc::new(
        RecursiveProxyService::new(recursive_config, domain_pool_service.clone())
            .await
            .map_err(|e| crate::types::ProxyError::internal(&format!("递归代理服务创建失败: {}", e)))?
    );

    // 直接创建完整的AppState
    let app_state = AppState::new(
        database,
        auth_service,
        domain_pool_service,
        perf_monitor,
        recursive_proxy,
    );

    info!("✅ 完整AppState直接创建完成");
    Ok(Arc::new(app_state))
}

/// 启动Pingora反向代理服务
async fn start_pingora_proxy(
    listen_addr: String,
    proxy_manager: Arc<proxy_manager::ProxyManager>,
) -> ProxyResult<()> {
    info!("🔄 启动Pingora反向代理服务: {}", listen_addr);

    // 从代理管理器获取当前配置
    let mut config = proxy_manager.get_current_config().await;

    // 允许空的上游配置，支持后续动态添加
    if config.upstreams.is_empty() {
        info!("未配置上游服务器，支持后续通过以下方式添加：");
        info!("  1. 递归代理自动发现");
        info!("  2. Web管理界面手工添加");
        info!("  3. API接口动态配置");
        info!("  4. 配置文件更新后重启");

        // 不添加默认配置，保持空配置允许动态添加
        config.upstreams = vec![];
    } else {
        info!(
            "使用动态上游服务器配置，包含 {} 个上游",
            config.upstreams.len()
        );
    }

    // 设置服务器配置
    config.server.listen = listen_addr.clone();

    // 构建Pingora服务器
    let server = ProxyServerBuilder::new(&config)
        .with_listen_addr(listen_addr)
        .build()?;

    // 在单独的任务中运行Pingora服务器
    tokio::task::spawn_blocking(move || {
        server.run_forever();
    })
    .await?;

    Ok(())
}

// 旧的Web界面启动函数已删除 - 现在使用start_web_interface_with_full_state

/// 直接使用完整AppState启动Web管理界面（最简洁的方案）
async fn start_web_interface_with_full_state(
    listen_addr: String,
    app_state: Arc<crate::types::AppState>,
) -> ProxyResult<()> {
    use axum::Router;
    use tower_http::trace::TraceLayer;
    use tower_http::services::ServeDir;

    info!("🌐 启动完整功能Web管理界面: {}", listen_addr);

    // 创建domain-pool路由（使用domain_pool_service）
    let domain_pool_routes = crate::domains::api::create_domain_pool_routes()
        .with_state(app_state.domain_pool_service.clone())
        .layer(axum::middleware::from_fn_with_state(
            app_state.auth_service.clone(),
            crate::auth::jwt_auth_middleware,
        ));

    // 直接创建使用完整AppState的路由
    let app = Router::new()
        // 添加auto_proxy的完整功能路由
        .merge(api::auto_proxy::routes())
        // 添加domain-pool路由
        .nest("/api/domain-pool", domain_pool_routes)
        // 添加静态文件服务
        .nest_service("/", ServeDir::new("frontend"))
        // 添加Extension中间件
        .layer(axum::Extension(app_state.clone()))
        // 添加追踪中间件
        .layer(TraceLayer::new_for_http())
        // 设置状态
        .with_state(app_state);

    info!("✅ 完整功能路由已配置");

    // 启动服务器
    let listener = tokio::net::TcpListener::bind(&listen_addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}
