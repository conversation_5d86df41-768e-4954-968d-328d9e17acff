//! 域名池管理统一模块
//!
//! 这个模块整合了域名池的所有功能：
//! - MongoDB数据模型
//! - 业务逻辑服务
//! - API接口
//! - 数据访问层
//! - 域名提取功能
//! - 内容替换功能

// 使用统一的domains模块实现
pub use crate::domains::api;
// 使用统一的domains模块实现
pub use crate::domains::models;
// 使用统一的domains模块实现
pub use crate::domains::repository;
// 使用统一的domains模块实现
pub use crate::domains::service;
// 使用统一的domains模块实现
pub use crate::domains::extractor;  // 重定向到统一实现
pub use crate::domains::replacer;   // 重定向到统一实现

// 统一导出
pub use crate::domains::models::*;
pub use crate::domains::service::DomainPoolService;
pub use crate::domains::DomainExtractor;  // 导出域名提取器
pub use crate::domains::ContentReplacer;   // 导出内容替换器
