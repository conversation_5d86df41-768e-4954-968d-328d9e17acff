//! 域名池管理统一模块
//!
//! 这个模块整合了域名池的所有功能：
//! - MongoDB数据模型
//! - 业务逻辑服务
//! - API接口
//! - 数据访问层
//! - 域名提取功能
//! - 内容替换功能

pub mod api;
pub mod models;
pub mod repository;
pub mod service;
pub mod extractor;  // 新增：域名提取功能
pub mod replacer;   // 新增：内容替换功能

// 统一导出
pub use models::*;
pub use service::DomainPoolService;
pub use extractor::DomainExtractor;  // 导出域名提取器
pub use replacer::ContentReplacer;   // 导出内容替换器
