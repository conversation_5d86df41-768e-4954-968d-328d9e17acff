/// 集成代理服务
/// 
/// 将自动域名映射、SSL证书管理、内容替换和缓存功能
/// 整合到一个统一的代理服务中

use std::sync::Arc;
use std::collections::HashMap;
use std::str::FromStr;
use anyhow::{Result, anyhow};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::auto_proxy::{AutoProxyManager, AutoProxyConfig};
use crate::proxy_middleware::{AutoProxyMiddleware, ProxyContext};
use crate::recursive_proxy::cache::IntelligentCache;

/// 集成代理服务
pub struct IntegratedProxyService {
    /// 自动代理管理器
    auto_proxy_manager: Arc<AutoProxyManager>,
    /// 自动代理中间件
    middleware: Arc<AutoProxyMiddleware>,
    /// 服务配置
    config: IntegratedProxyConfig,
    /// 服务状态
    status: Arc<RwLock<ServiceStatus>>,
    /// 统计信息
    stats: Arc<RwLock<ServiceStatistics>>,
}

/// 集成代理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegratedProxyConfig {
    /// 自动代理配置
    pub auto_proxy: AutoProxyConfig,
    /// 服务端口
    pub port: u16,
    /// 是否启用HTTPS
    pub enable_https: bool,
    /// 工作线程数
    pub worker_threads: usize,
    /// 请求超时时间（秒）
    pub request_timeout_seconds: u64,
    /// 最大并发连接数
    pub max_concurrent_connections: usize,
}

impl Default for IntegratedProxyConfig {
    fn default() -> Self {
        Self {
            auto_proxy: AutoProxyConfig::default(),
            port: 1911,
            enable_https: true,
            worker_threads: 4,
            request_timeout_seconds: 30,
            max_concurrent_connections: 1000,
        }
    }
}

/// 服务状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceStatus {
    pub running: bool,
    pub started_at: Option<DateTime<Utc>>,
    pub uptime_seconds: u64,
    pub active_connections: usize,
    pub last_error: Option<String>,
}

impl Default for ServiceStatus {
    fn default() -> Self {
        Self {
            running: false,
            started_at: None,
            uptime_seconds: 0,
            active_connections: 0,
            last_error: None,
        }
    }
}

/// 服务统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceStatistics {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub domains_discovered: u64,
    pub ssl_certificates_issued: u64,
    pub cache_hits: u64,
    pub cache_misses: u64,
    pub average_response_time_ms: f64,
    pub bytes_transferred: u64,
}

impl Default for ServiceStatistics {
    fn default() -> Self {
        Self {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            domains_discovered: 0,
            ssl_certificates_issued: 0,
            cache_hits: 0,
            cache_misses: 0,
            average_response_time_ms: 0.0,
            bytes_transferred: 0,
        }
    }
}

impl IntegratedProxyService {
    /// 创建新的集成代理服务
    pub async fn new(
        config: IntegratedProxyConfig,
        db: Arc<mongodb::Database>,
    ) -> Result<Self> {
        // 创建自动代理管理器
        let auto_proxy_manager = Arc::new(
            AutoProxyManager::new(config.auto_proxy.clone(), db).await?
        );

        // 创建自动代理中间件
        let middleware = Arc::new(
            AutoProxyMiddleware::new(auto_proxy_manager.clone()).await?
        );

        Ok(Self {
            auto_proxy_manager,
            middleware,
            config,
            status: Arc::new(RwLock::new(ServiceStatus::default())),
            stats: Arc::new(RwLock::new(ServiceStatistics::default())),
        })
    }

    /// 启动服务
    pub async fn start(&self) -> Result<()> {
        let mut status = self.status.write().await;
        if status.running {
            return Err(anyhow!("服务已在运行"));
        }

        status.running = true;
        status.started_at = Some(Utc::now());
        status.last_error = None;
        drop(status);

        tracing::info!("集成代理服务启动，端口: {}", self.config.port);
        
        // 这里可以启动实际的HTTP服务器
        // 例如使用axum或其他HTTP框架
        
        Ok(())
    }

    /// 停止服务
    pub async fn stop(&self) -> Result<()> {
        let mut status = self.status.write().await;
        if !status.running {
            return Err(anyhow!("服务未在运行"));
        }

        status.running = false;
        status.active_connections = 0;
        drop(status);

        tracing::info!("集成代理服务已停止");
        Ok(())
    }

    /// 处理代理请求
    pub async fn handle_request(
        &self,
        host: &str,
        path: &str,
        method: &str,
        headers: &HashMap<String, String>,
        body: Option<&[u8]>,
    ) -> Result<ProxyResponse> {
        let start_time = std::time::Instant::now();
        
        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            stats.total_requests += 1;
        }

        // 处理请求映射
        let http_headers = self.convert_headers(headers)?;
        let mut context = self.middleware
            .handle_request_mapping(host, path, method, &http_headers)
            .await?;

        // 如果没有找到上游域名，返回错误
        let upstream_domain = context.upstream_domain.clone()
            .ok_or_else(|| anyhow!("未找到域名映射: {}", host))?;

        // 发送请求到上游服务器
        let upstream_response = self.send_upstream_request(
            &upstream_domain,
            path,
            method,
            headers,
            body,
        ).await?;

        // 处理响应内容
        let processed_content = self.middleware
            .handle_response_processing(
                &mut context,
                &upstream_response.body,
                upstream_response.content_type.as_deref(),
            )
            .await?;

        let response_time = start_time.elapsed().as_millis() as u64;

        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            stats.successful_requests += 1;
            stats.bytes_transferred += processed_content.len() as u64;
            stats.domains_discovered += context.discovered_domains.len() as u64;
            
            // 更新平均响应时间
            let total_requests = stats.total_requests as f64;
            stats.average_response_time_ms = 
                (stats.average_response_time_ms * (total_requests - 1.0) + response_time as f64) / total_requests;
        }

        Ok(ProxyResponse {
            status_code: upstream_response.status_code,
            headers: upstream_response.headers,
            body: processed_content,
            content_type: upstream_response.content_type,
            response_time_ms: response_time,
            discovered_domains: context.discovered_domains,
            content_replaced: context.content_replaced,
        })
    }

    /// 发送请求到上游服务器
    async fn send_upstream_request(
        &self,
        upstream_domain: &str,
        path: &str,
        method: &str,
        headers: &HashMap<String, String>,
        body: Option<&[u8]>,
    ) -> Result<UpstreamResponse> {
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(self.config.request_timeout_seconds))
            .build()?;

        let url = format!("https://{}{}", upstream_domain, path);
        
        let mut request = match method {
            "GET" => client.get(&url),
            "POST" => client.post(&url),
            "PUT" => client.put(&url),
            "DELETE" => client.delete(&url),
            _ => client.get(&url),
        };

        // 添加请求头
        for (key, value) in headers {
            if key.to_lowercase() != "host" {
                request = request.header(key, value);
            }
        }

        // 添加请求体
        if let Some(body) = body {
            request = request.body(body.to_vec());
        }

        let response = request.send().await?;
        let status_code = response.status().as_u16();
        let headers = response.headers().clone();
        let content_type = headers.get("content-type")
            .and_then(|v| v.to_str().ok())
            .map(|s| s.to_string());
        
        let body = response.bytes().await?.to_vec();

        Ok(UpstreamResponse {
            status_code,
            headers: self.convert_reqwest_headers(&headers)?,
            body,
            content_type,
        })
    }

    /// 转换请求头格式
    fn convert_headers(&self, headers: &HashMap<String, String>) -> Result<reqwest::header::HeaderMap> {
        let mut header_map = reqwest::header::HeaderMap::new();
        for (key, value) in headers {
            let header_name = reqwest::header::HeaderName::from_str(key)?;
            let header_value = reqwest::header::HeaderValue::from_str(value)?;
            header_map.insert(header_name, header_value);
        }
        Ok(header_map)
    }

    /// 转换reqwest响应头格式
    fn convert_reqwest_headers(&self, headers: &reqwest::header::HeaderMap) -> Result<HashMap<String, String>> {
        let mut header_map = HashMap::new();
        for (key, value) in headers {
            let key_str = key.as_str().to_string();
            let value_str = value.to_str()?.to_string();
            header_map.insert(key_str, value_str);
        }
        Ok(header_map)
    }

    /// 获取服务状态
    pub async fn get_status(&self) -> ServiceStatus {
        let mut status = self.status.read().await.clone();
        
        if let Some(started_at) = status.started_at {
            status.uptime_seconds = (Utc::now() - started_at).num_seconds() as u64;
        }
        
        status
    }

    /// 获取服务统计信息
    pub async fn get_statistics(&self) -> ServiceStatistics {
        self.stats.read().await.clone()
    }

    /// 获取详细统计信息
    pub async fn get_detailed_statistics(&self) -> Result<DetailedStatistics> {
        let service_stats = self.get_statistics().await;
        let middleware_stats = self.middleware.get_statistics().await?;
        let auto_proxy_stats = self.auto_proxy_manager.get_statistics().await?;

        Ok(DetailedStatistics {
            service: service_stats,
            middleware: middleware_stats,
            auto_proxy: auto_proxy_stats,
        })
    }
}

/// 上游响应
#[derive(Debug)]
struct UpstreamResponse {
    pub status_code: u16,
    pub headers: HashMap<String, String>,
    pub body: Vec<u8>,
    pub content_type: Option<String>,
}

/// 代理响应
#[derive(Debug)]
pub struct ProxyResponse {
    pub status_code: u16,
    pub headers: HashMap<String, String>,
    pub body: Vec<u8>,
    pub content_type: Option<String>,
    pub response_time_ms: u64,
    pub discovered_domains: Vec<String>,
    pub content_replaced: bool,
}

/// 详细统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetailedStatistics {
    pub service: ServiceStatistics,
    pub middleware: crate::proxy_middleware::MiddlewareStatistics,
    pub auto_proxy: crate::auto_proxy::AutoProxyStatistics,
}
