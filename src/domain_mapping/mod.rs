/// 自动化域名映射管理模块
/// 
/// 核心功能：
/// 1. 自动生成下游子域名
/// 2. 管理域名映射关系
/// 3. 内存缓存优化
/// 4. 重定向跟踪

use std::collections::HashMap;
use std::sync::Arc;
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use anyhow::{Result, anyhow};
use publicsuffix::{List, Psl};
use url::Url;

// 使用统一的domains模块实现
pub use crate::domains::extractor;
pub use crate::domains::replacer;

/// 域名映射记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DomainMapping {
    pub id: String,
    pub upstream_domain: String,
    pub downstream_domain: String,
    pub root_domain: String,        // 下游的根域名
    pub subdomain_prefix: String,   // 子域名前缀
    pub ssl_enabled: bool,
    pub ssl_cert_path: Option<String>,
    pub created_at: DateTime<Utc>,
    pub last_used: Option<DateTime<Utc>>,
    pub request_count: u64,
    pub status: MappingStatus,
}

/// 映射状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MappingStatus {
    Active,      // 活跃使用中
    Inactive,    // 非活跃
    SslPending,  // SSL证书申请中
    SslFailed,   // SSL证书申请失败
    Error,       // 错误状态
}

/// 根域名配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RootDomainConfig {
    pub domain: String,
    pub max_subdomains: u32,        // 最大子域名数量
    pub used_subdomains: u32,       // 已使用子域名数量
    pub ssl_cert_path: Option<String>,
    pub ssl_cert_status: SslCertStatus,
    pub created_at: DateTime<Utc>,
    pub last_ssl_check: Option<DateTime<Utc>>,
}

/// SSL证书状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SslCertStatus {
    None,        // 未申请
    Pending,     // 申请中
    Valid,       // 有效
    Expired,     // 已过期
    Failed,      // 申请失败
}

/// 域名提取结果
#[derive(Debug, Clone)]
pub struct ExtractedDomain {
    pub domain: String,
    pub is_subdomain: bool,
    pub root_domain: String,
    pub subdomain_part: String,
}

/// 重定向跟踪结果
#[derive(Debug, Clone)]
pub struct RedirectResult {
    pub final_url: String,
    pub redirect_chain: Vec<String>,
    pub redirect_count: u32,
    pub success: bool,
}

/// 域名映射管理器
pub struct DomainMappingManager {
    /// 内存缓存：上游域名 -> 下游域名
    upstream_to_downstream: Arc<RwLock<HashMap<String, String>>>,
    /// 内存缓存：下游域名 -> 上游域名
    downstream_to_upstream: Arc<RwLock<HashMap<String, String>>>,
    /// 根域名配置缓存
    root_domains: Arc<RwLock<HashMap<String, RootDomainConfig>>>,
    /// 域名映射详细信息缓存
    mappings: Arc<RwLock<HashMap<String, DomainMapping>>>,
    /// 公共后缀列表（用于域名解析）
    suffix_list: Arc<List>,
    /// 数据库连接
    db: Arc<mongodb::Database>,
    /// 配置
    config: DomainMappingConfig,
}

/// 域名映射配置
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DomainMappingConfig {
    pub max_subdomains_per_root: u32,
    pub max_redirect_hops: u32,
    pub redirect_timeout_seconds: u64,
    pub ssl_cert_dir: String,
    pub enable_ssl_auto_request: bool,
}

impl Default for DomainMappingConfig {
    fn default() -> Self {
        Self {
            max_subdomains_per_root: 100,
            max_redirect_hops: 3,
            redirect_timeout_seconds: 30,
            ssl_cert_dir: "certs".to_string(),
            enable_ssl_auto_request: true,
        }
    }
}

impl DomainMappingManager {
    /// 创建新的域名映射管理器
    pub async fn new(db: Arc<mongodb::Database>, config: DomainMappingConfig) -> Result<Self> {
        // 加载公共后缀列表
        let suffix_list = Arc::new(List::new());
        
        let manager = Self {
            upstream_to_downstream: Arc::new(RwLock::new(HashMap::new())),
            downstream_to_upstream: Arc::new(RwLock::new(HashMap::new())),
            root_domains: Arc::new(RwLock::new(HashMap::new())),
            mappings: Arc::new(RwLock::new(HashMap::new())),
            suffix_list,
            db,
            config,
        };

        // 从数据库加载现有映射到内存缓存
        manager.load_mappings_from_db().await?;
        
        Ok(manager)
    }

    /// 从数据库加载所有映射到内存缓存
    async fn load_mappings_from_db(&self) -> Result<()> {
        use mongodb::bson::doc;
        
        // 加载域名映射
        let mappings_collection = self.db.collection::<DomainMapping>("domain_mappings");
        let mut cursor = mappings_collection.find(doc! {}, None).await?;
        
        let mut upstream_cache = self.upstream_to_downstream.write();
        let mut downstream_cache = self.downstream_to_upstream.write();
        let mut mappings_cache = self.mappings.write();
        
        while cursor.advance().await? {
            let mapping = cursor.deserialize_current()?;
            upstream_cache.insert(mapping.upstream_domain.clone(), mapping.downstream_domain.clone());
            downstream_cache.insert(mapping.downstream_domain.clone(), mapping.upstream_domain.clone());
            mappings_cache.insert(mapping.id.clone(), mapping);
        }
        
        // 加载根域名配置
        let root_domains_collection = self.db.collection::<RootDomainConfig>("root_domains");
        let mut cursor = root_domains_collection.find(doc! {}, None).await?;
        
        let mut root_domains_cache = self.root_domains.write();
        
        while cursor.advance().await? {
            let root_domain = cursor.deserialize_current()?;
            root_domains_cache.insert(root_domain.domain.clone(), root_domain);
        }
        
        tracing::info!("已从数据库加载 {} 个域名映射和 {} 个根域名配置", 
                      mappings_cache.len(), root_domains_cache.len());
        
        Ok(())
    }

    /// 获取或创建域名映射
    pub async fn get_or_create_mapping(&self, upstream_domain: &str) -> Result<String> {
        // 首先检查内存缓存
        if let Some(downstream) = self.upstream_to_downstream.read().get(upstream_domain) {
            return Ok(downstream.clone());
        }

        // 跟踪重定向获取最终域名
        let final_upstream = self.follow_redirects(upstream_domain).await?;
        
        // 再次检查缓存（可能重定向后的域名已存在）
        if let Some(downstream) = self.upstream_to_downstream.read().get(&final_upstream.final_url) {
            return Ok(downstream.clone());
        }

        // 创建新的映射
        self.create_new_mapping(&final_upstream.final_url).await
    }

    /// 跟踪重定向
    async fn follow_redirects(&self, url: &str) -> Result<RedirectResult> {
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(self.config.redirect_timeout_seconds))
            .redirect(reqwest::redirect::Policy::none())
            .build()?;

        let mut current_url = url.to_string();
        let mut redirect_chain = vec![current_url.clone()];
        let mut redirect_count = 0;

        while redirect_count < self.config.max_redirect_hops {
            let response = match client.head(&current_url).send().await {
                Ok(resp) => resp,
                Err(_) => break,
            };

            if response.status().is_redirection() {
                if let Some(location) = response.headers().get("location") {
                    if let Ok(location_str) = location.to_str() {
                        // 处理相对URL
                        current_url = if location_str.starts_with("http") {
                            location_str.to_string()
                        } else {
                            // 简单的相对URL处理
                            if let Ok(base) = Url::parse(&current_url) {
                                base.join(location_str)?.to_string()
                            } else {
                                break;
                            }
                        };
                        
                        redirect_chain.push(current_url.clone());
                        redirect_count += 1;
                    } else {
                        break;
                    }
                } else {
                    break;
                }
            } else {
                break;
            }
        }

        Ok(RedirectResult {
            final_url: current_url,
            redirect_chain,
            redirect_count,
            success: true,
        })
    }

    /// 创建新的域名映射
    async fn create_new_mapping(&self, upstream_domain: &str) -> Result<String> {
        // 解析上游域名，提取主体部分
        let extracted = self.extract_domain_parts(upstream_domain)?;
        
        // 获取可用的下游域名
        let downstream_domain = self.get_available_downstream_domain(&extracted).await?;
        
        // 创建映射记录
        let mapping = DomainMapping {
            id: uuid::Uuid::new_v4().to_string(),
            upstream_domain: upstream_domain.to_string(),
            downstream_domain: downstream_domain.clone(),
            root_domain: extracted.root_domain.clone(),
            subdomain_prefix: extracted.subdomain_part.clone(),
            ssl_enabled: self.config.enable_ssl_auto_request,
            ssl_cert_path: None,
            created_at: Utc::now(),
            last_used: Some(Utc::now()),
            request_count: 0,
            status: if self.config.enable_ssl_auto_request {
                MappingStatus::SslPending
            } else {
                MappingStatus::Active
            },
        };

        // 保存到数据库
        self.save_mapping_to_db(&mapping).await?;
        
        // 更新内存缓存
        self.upstream_to_downstream.write().insert(upstream_domain.to_string(), downstream_domain.clone());
        self.downstream_to_upstream.write().insert(downstream_domain.clone(), upstream_domain.to_string());
        self.mappings.write().insert(mapping.id.clone(), mapping);

        tracing::info!("创建新域名映射: {} -> {}", upstream_domain, downstream_domain);
        
        Ok(downstream_domain)
    }

    /// 保存映射到数据库
    async fn save_mapping_to_db(&self, mapping: &DomainMapping) -> Result<()> {
        let collection = self.db.collection::<DomainMapping>("domain_mappings");
        collection.insert_one(mapping, None).await?;
        Ok(())
    }

    /// 提取域名各部分
    fn extract_domain_parts(&self, domain: &str) -> Result<ExtractedDomain> {
        // 清理域名（移除协议、路径等）
        let clean_domain = self.clean_domain(domain)?;

        // 使用公共后缀列表解析域名
        // 简化的域名解析逻辑
        let parts: Vec<&str> = clean_domain.split('.').collect();

        // 假设最后两个部分是根域名（简化处理）
        let root_domain = if parts.len() >= 2 {
            format!("{}.{}", parts[parts.len()-2], parts[parts.len()-1])
        } else {
            clean_domain.clone()
        };

        // 判断是否为子域名
        if parts.len() > 2 {
            // 提取子域名部分
            let subdomain = parts[0..parts.len()-2].join(".");
            // 处理子域名
            let subdomain_part = if subdomain == "www" {
                // www视为根域名
                root_domain.split('.').next().unwrap_or(&root_domain).to_string()
            } else {
                // 其他子域名保持原样
                subdomain.to_string()
            };

            Ok(ExtractedDomain {
                domain: clean_domain,
                is_subdomain: subdomain != "www",
                root_domain,
                subdomain_part,
            })
        } else {
            // 根域名
            let domain_name = root_domain.split('.').next().unwrap_or(&root_domain).to_string();
            Ok(ExtractedDomain {
                domain: clean_domain,
                is_subdomain: false,
                root_domain,
                subdomain_part: domain_name,
            })
        }
    }

    /// 清理域名
    fn clean_domain(&self, domain: &str) -> Result<String> {
        let domain = domain.trim();

        // 移除协议
        let domain = if domain.starts_with("http://") {
            &domain[7..]
        } else if domain.starts_with("https://") {
            &domain[8..]
        } else {
            domain
        };

        // 移除路径和查询参数
        let domain = domain.split('/').next().unwrap_or(domain);
        let domain = domain.split('?').next().unwrap_or(domain);
        let domain = domain.split('#').next().unwrap_or(domain);

        // 移除端口号
        let domain = if let Some(pos) = domain.rfind(':') {
            if domain[pos+1..].chars().all(|c| c.is_ascii_digit()) {
                &domain[..pos]
            } else {
                domain
            }
        } else {
            domain
        };

        Ok(domain.to_lowercase())
    }

    /// 获取可用的下游域名
    async fn get_available_downstream_domain(&self, extracted: &ExtractedDomain) -> Result<String> {
        // 获取可用的根域名
        let root_domain = self.get_available_root_domain().await?;

        // 生成下游域名
        let downstream_domain = if extracted.is_subdomain {
            format!("{}.{}", extracted.subdomain_part, root_domain)
        } else {
            root_domain.clone()
        };

        // 检查是否已被使用
        if self.downstream_to_upstream.read().contains_key(&downstream_domain) {
            return Err(anyhow!("下游域名已被使用: {}", downstream_domain));
        }

        // 更新根域名使用计数
        self.increment_root_domain_usage(&root_domain).await?;

        Ok(downstream_domain)
    }

    /// 获取可用的根域名
    async fn get_available_root_domain(&self) -> Result<String> {
        let root_domains = self.root_domains.read();

        // 查找有可用子域名的根域名
        for (domain, config) in root_domains.iter() {
            if config.used_subdomains < config.max_subdomains {
                return Ok(domain.clone());
            }
        }

        drop(root_domains);

        // 如果没有可用的根域名，从数据库获取下游域名列表
        self.get_next_available_root_from_db().await
    }

    /// 从数据库获取下一个可用的根域名
    async fn get_next_available_root_from_db(&self) -> Result<String> {
        use mongodb::bson::doc;

        // 获取所有下游域名
        let domains_collection = self.db.collection::<serde_json::Value>("domains");
        let filter = doc! { "type": "downstream" };
        let mut cursor = domains_collection.find(filter, None).await?;

        while cursor.advance().await? {
            let domain_doc = cursor.deserialize_current()?;
            if let Some(domain) = domain_doc.get("domain").and_then(|v| v.as_str()) {
                // 检查这个域名是否已经在根域名配置中
                if !self.root_domains.read().contains_key(domain) {
                    // 创建新的根域名配置
                    let config = RootDomainConfig {
                        domain: domain.to_string(),
                        max_subdomains: self.config.max_subdomains_per_root,
                        used_subdomains: 0,
                        ssl_cert_path: None,
                        ssl_cert_status: SslCertStatus::None,
                        created_at: Utc::now(),
                        last_ssl_check: None,
                    };

                    // 保存到数据库
                    let root_collection = self.db.collection::<RootDomainConfig>("root_domains");
                    root_collection.insert_one(&config, None).await?;

                    // 更新内存缓存
                    self.root_domains.write().insert(domain.to_string(), config);

                    return Ok(domain.to_string());
                }
            }
        }

        Err(anyhow!("没有可用的下游域名"))
    }

    /// 增加根域名使用计数
    async fn increment_root_domain_usage(&self, root_domain: &str) -> Result<()> {
        use mongodb::bson::doc;

        // 更新内存缓存
        if let Some(config) = self.root_domains.write().get_mut(root_domain) {
            config.used_subdomains += 1;
        }

        // 更新数据库
        let collection = self.db.collection::<RootDomainConfig>("root_domains");
        let filter = doc! { "domain": root_domain };
        let update = doc! { "$inc": { "used_subdomains": 1 } };
        collection.update_one(filter, update, None).await?;

        Ok(())
    }

    /// 根据下游域名查找上游域名
    pub fn get_upstream_by_downstream(&self, downstream: &str) -> Option<String> {
        self.downstream_to_upstream.read().get(downstream).cloned()
    }

    /// 根据上游域名查找下游域名
    pub fn get_downstream_by_upstream(&self, upstream: &str) -> Option<String> {
        self.upstream_to_downstream.read().get(upstream).cloned()
    }

    /// 获取所有映射
    pub fn get_all_mappings(&self) -> Vec<DomainMapping> {
        self.mappings.read().values().cloned().collect()
    }

    /// 更新映射使用统计
    pub async fn update_mapping_stats(&self, downstream: &str) -> Result<()> {
        if let Some(upstream) = self.get_upstream_by_downstream(downstream) {
            // 更新内存缓存
            if let Some(mapping) = self.mappings.write().values_mut()
                .find(|m| m.downstream_domain == downstream) {
                mapping.last_used = Some(Utc::now());
                mapping.request_count += 1;
            }

            // 更新数据库
            use mongodb::bson::doc;
            let collection = self.db.collection::<DomainMapping>("domain_mappings");
            let filter = doc! { "downstream_domain": downstream };
            let update = doc! {
                "$set": { "last_used": Utc::now() },
                "$inc": { "request_count": 1 }
            };
            collection.update_one(filter, update, None).await?;
        }

        Ok(())
    }
}
