/// 域名提取器 - 从HTML/CSS/JSON内容中提取域名
use std::collections::HashSet;
use regex::Regex;
use anyhow::{Result, anyhow};
use html5ever::parse_document;
use html5ever::tendril::TendrilSink;
use markup5ever_rcdom::{RcDom, NodeData};
use markup5ever_rcdom::Handle;

/// 域名提取器
pub struct DomainExtractor {
    /// URL匹配正则表达式
    url_regex: Regex,
    /// 域名匹配正则表达式
    domain_regex: Regex,
    /// Base64 URL匹配正则表达式
    base64_url_regex: Regex,
}

impl DomainExtractor {
    /// 创建新的域名提取器
    pub fn new() -> Result<Self> {
        let url_regex = Regex::new(r#"https?://([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})"#)?;
        let domain_regex = Regex::new(r#"([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})"#)?;
        let base64_url_regex = Regex::new(r#"[A-Za-z0-9+/]{20,}={0,2}"#)?;
        
        Ok(Self {
            url_regex,
            domain_regex,
            base64_url_regex,
        })
    }

    /// 从HTML内容中提取域名
    pub fn extract_from_html(&self, html: &str) -> Result<HashSet<String>> {
        let mut domains = HashSet::new();
        
        // 解析HTML
        let dom = parse_document(RcDom::default(), Default::default())
            .from_utf8()
            .read_from(&mut html.as_bytes())?;
        
        // 遍历DOM树提取域名
        self.extract_from_node(&dom.document, &mut domains);
        
        // 同时使用正则表达式提取（防止遗漏）
        domains.extend(self.extract_with_regex(html)?);
        
        // 处理Base64编码的URL
        domains.extend(self.extract_from_base64(html)?);
        
        Ok(domains)
    }

    /// 从CSS内容中提取域名
    pub fn extract_from_css(&self, css: &str) -> Result<HashSet<String>> {
        let mut domains = HashSet::new();
        
        // CSS中的URL通常在url()函数中
        let css_url_regex = Regex::new(r#"url\s*\(\s*['""]?([^'")]+)['""]?\s*\)"#)?;
        
        for cap in css_url_regex.captures_iter(css) {
            if let Some(url) = cap.get(1) {
                let url_str = url.as_str();
                if let Some(domain) = self.extract_domain_from_url(url_str) {
                    domains.insert(domain);
                }
            }
        }
        
        // 同时使用通用正则表达式
        domains.extend(self.extract_with_regex(css)?);
        
        Ok(domains)
    }

    /// 从JSON内容中提取域名
    pub fn extract_from_json(&self, json: &str) -> Result<HashSet<String>> {
        let mut domains = HashSet::new();
        
        // 尝试解析JSON并提取字符串值
        if let Ok(value) = serde_json::from_str::<serde_json::Value>(json) {
            self.extract_from_json_value(&value, &mut domains);
        }
        
        // 同时使用正则表达式（防止JSON解析失败）
        domains.extend(self.extract_with_regex(json)?);
        
        Ok(domains)
    }

    /// 从任意文本内容中提取域名
    pub fn extract_from_text(&self, text: &str) -> Result<HashSet<String>> {
        let mut domains = HashSet::new();
        
        // 使用正则表达式提取
        domains.extend(self.extract_with_regex(text)?);
        
        // 处理Base64编码的URL
        domains.extend(self.extract_from_base64(text)?);
        
        Ok(domains)
    }

    /// 使用正则表达式提取域名
    fn extract_with_regex(&self, text: &str) -> Result<HashSet<String>> {
        let mut domains = HashSet::new();
        
        // 提取完整URL中的域名
        for cap in self.url_regex.captures_iter(text) {
            if let Some(domain) = cap.get(1) {
                let domain_str = domain.as_str().to_lowercase();
                if self.is_valid_domain(&domain_str) {
                    domains.insert(domain_str);
                }
            }
        }
        
        // 提取独立的域名
        for cap in self.domain_regex.captures_iter(text) {
            if let Some(domain) = cap.get(1) {
                let domain_str = domain.as_str().to_lowercase();
                if self.is_valid_domain(&domain_str) && !self.is_local_domain(&domain_str) {
                    domains.insert(domain_str);
                }
            }
        }
        
        Ok(domains)
    }

    /// 从Base64编码中提取域名
    fn extract_from_base64(&self, text: &str) -> Result<HashSet<String>> {
        let mut domains = HashSet::new();
        
        for cap in self.base64_url_regex.captures_iter(text) {
            if let Some(base64_str) = cap.get(0) {
                if let Ok(decoded) = base64::decode(base64_str.as_str()) {
                    if let Ok(decoded_str) = String::from_utf8(decoded) {
                        // 从解码后的字符串中提取域名
                        domains.extend(self.extract_with_regex(&decoded_str)?);
                    }
                }
            }
        }
        
        Ok(domains)
    }

    /// 从DOM节点中提取域名
    fn extract_from_node(&self, node: &Handle, domains: &mut HashSet<String>) {
        match &node.data {
            NodeData::Element { name, attrs, .. } => {
                let tag_name = name.local.as_ref();
                let attrs = attrs.borrow();
                
                // 提取不同标签的URL属性
                match tag_name {
                    "a" | "link" => {
                        if let Some(href) = self.get_attr_value(&attrs, "href") {
                            if let Some(domain) = self.extract_domain_from_url(&href) {
                                domains.insert(domain);
                            }
                        }
                    }
                    "img" | "script" => {
                        if let Some(src) = self.get_attr_value(&attrs, "src") {
                            if let Some(domain) = self.extract_domain_from_url(&src) {
                                domains.insert(domain);
                            }
                        }
                    }
                    "form" => {
                        if let Some(action) = self.get_attr_value(&attrs, "action") {
                            if let Some(domain) = self.extract_domain_from_url(&action) {
                                domains.insert(domain);
                            }
                        }
                    }
                    "iframe" => {
                        if let Some(src) = self.get_attr_value(&attrs, "src") {
                            if let Some(domain) = self.extract_domain_from_url(&src) {
                                domains.insert(domain);
                            }
                        }
                    }
                    _ => {}
                }
                
                // 检查所有属性中的URL
                for attr in attrs.iter() {
                    let value = attr.value.as_ref();
                    if value.starts_with("http://") || value.starts_with("https://") {
                        if let Some(domain) = self.extract_domain_from_url(value) {
                            domains.insert(domain);
                        }
                    }
                }
            }
            NodeData::Text { contents } => {
                // 从文本内容中提取域名
                let text = contents.borrow();
                if let Ok(extracted) = self.extract_with_regex(&text) {
                    domains.extend(extracted);
                }
            }
            _ => {}
        }
        
        // 递归处理子节点
        for child in node.children.borrow().iter() {
            self.extract_from_node(child, domains);
        }
    }

    /// 从JSON值中递归提取域名
    fn extract_from_json_value(&self, value: &serde_json::Value, domains: &mut HashSet<String>) {
        match value {
            serde_json::Value::String(s) => {
                if let Ok(extracted) = self.extract_with_regex(s) {
                    domains.extend(extracted);
                }
            }
            serde_json::Value::Array(arr) => {
                for item in arr {
                    self.extract_from_json_value(item, domains);
                }
            }
            serde_json::Value::Object(obj) => {
                for (_, val) in obj {
                    self.extract_from_json_value(val, domains);
                }
            }
            _ => {}
        }
    }

    /// 获取HTML属性值
    fn get_attr_value(&self, attrs: &[html5ever::Attribute], name: &str) -> Option<String> {
        attrs.iter()
            .find(|attr| attr.name.local.as_ref() == name)
            .map(|attr| attr.value.as_ref().to_string())
    }

    /// 从URL中提取域名
    fn extract_domain_from_url(&self, url: &str) -> Option<String> {
        if let Ok(parsed_url) = url::Url::parse(url) {
            if let Some(host) = parsed_url.host_str() {
                let domain = host.to_lowercase();
                if self.is_valid_domain(&domain) && !self.is_local_domain(&domain) {
                    return Some(domain);
                }
            }
        }
        None
    }

    /// 验证域名是否有效
    fn is_valid_domain(&self, domain: &str) -> bool {
        // 基本格式检查
        if domain.is_empty() || domain.len() > 253 {
            return false;
        }
        
        // 必须包含点号
        if !domain.contains('.') {
            return false;
        }
        
        // 不能以点号开始或结束
        if domain.starts_with('.') || domain.ends_with('.') {
            return false;
        }
        
        // 检查是否为IP地址
        if domain.parse::<std::net::IpAddr>().is_ok() {
            return false;
        }
        
        // 检查字符是否合法
        domain.chars().all(|c| c.is_ascii_alphanumeric() || c == '.' || c == '-')
    }

    /// 检查是否为本地域名
    fn is_local_domain(&self, domain: &str) -> bool {
        domain == "localhost" 
            || domain.ends_with(".local")
            || domain.ends_with(".localhost")
            || domain.starts_with("127.")
            || domain.starts_with("192.168.")
            || domain.starts_with("10.")
            || (domain.starts_with("172.") && {
                if let Some(second_octet) = domain.split('.').nth(1) {
                    if let Ok(num) = second_octet.parse::<u8>() {
                        num >= 16 && num <= 31
                    } else {
                        false
                    }
                } else {
                    false
                }
            })
    }
}

impl Default for DomainExtractor {
    fn default() -> Self {
        Self::new().expect("Failed to create DomainExtractor")
    }
}
