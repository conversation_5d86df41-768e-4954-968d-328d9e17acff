use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 域名配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Domain {
    pub id: Option<i64>,
    pub domain: String,
    pub backend_url: String,
    pub ssl_enabled: bool,
    pub cert_path: Option<String>,
    pub key_path: Option<String>,
    pub health_check_url: Option<String>,
    pub health_check_interval: Option<i64>,
    pub max_connections: Option<i64>,
    pub timeout: Option<i64>,
    pub retry_count: Option<i64>,
    pub load_balancing: Option<String>,
    pub cache_enabled: bool,
    pub cache_ttl: Option<i64>,
    pub rate_limit: Option<i64>,
    pub rate_limit_window: Option<i64>,
    pub is_active: bool,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// 用户信息 - 优化版本，兼容MongoDB BSON格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<bson::oid::ObjectId>,
    pub username: String,
    pub password_hash: String,
    pub role: String,
    pub permissions: bson::Bson,
    #[serde(default = "default_true")]
    pub is_active: bool,
    #[serde(default = "default_true")]
    pub force_password_change: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub last_login: Option<DateTime<Utc>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated_at: Option<DateTime<Utc>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub email: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub password_expires_at: Option<DateTime<Utc>>,
    #[serde(default)]
    pub failed_login_attempts: i32,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub locked_until: Option<DateTime<Utc>>,
}

/// 路由配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Route {
    pub id: Option<i64>,
    pub domain_id: i64,
    pub path: String,
    pub backend_url: String,
    pub method: Option<String>,
    pub headers: Option<String>, // JSON格式的头部信息
    pub priority: i32,
    pub is_active: bool,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// 黑名单条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlacklistEntry {
    pub id: Option<i64>,
    pub ip: String,
    pub reason: Option<String>,
    pub expires_at: Option<DateTime<Utc>>,
    pub created_at: Option<DateTime<Utc>>,
}

/// SSL证书信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Certificate {
    pub id: Option<i64>,
    pub domain: String,
    pub cert_data: String,
    pub key_data: String,
    pub expires_at: DateTime<Utc>,
    pub is_active: bool,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// 配置条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigEntry {
    pub id: Option<i64>,
    pub key: String,
    pub value: String,
    pub description: Option<String>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// 日志条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub id: Option<i64>,
    pub level: String,
    pub message: String,
    pub module: Option<String>,
    pub request_id: Option<String>,
    pub created_at: DateTime<Utc>,
}

/// 域名组
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DomainGroup {
    pub id: Option<i64>,
    pub name: String,
    pub description: Option<String>,
    pub domains: Vec<String>,
    pub load_balancing_strategy: String,
    pub health_check_enabled: bool,
    pub is_active: bool,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// 访问日志
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessLog {
    pub id: Option<String>,
    pub client_ip: String,
    pub method: String,
    pub uri: String,
    pub status_code: u16,
    pub response_time_ms: u64,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub user_agent: Option<String>,
    pub referer: Option<String>,
    pub domain: String,
    pub backend_url: String,
    pub cache_hit: bool,
    pub error_message: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl AccessLog {
    pub fn new(
        client_ip: String,
        method: String,
        uri: String,
        status_code: u16,
        response_time_ms: u64,
        domain: String,
        backend_url: String,
    ) -> Self {
        Self {
            id: None,
            client_ip,
            method,
            uri,
            status_code,
            response_time_ms,
            bytes_sent: 0,
            bytes_received: 0,
            user_agent: None,
            referer: None,
            domain,
            backend_url,
            cache_hit: false,
            error_message: None,
            timestamp: chrono::Utc::now(),
        }
    }
}

/// 递归代理链路历史
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveChainHistory {
    pub id: Option<String>,
    /// 原始请求URL
    pub original_url: String,
    /// 最终URL
    pub final_url: String,
    /// 完整的URL链路
    pub chain: Vec<String>,
    /// 链路长度
    pub chain_length: usize,
    /// 最终响应状态码
    pub status_code: u16,
    /// 是否来自缓存
    pub from_cache: bool,
    /// 总响应时间（毫秒）
    pub total_response_time_ms: u64,
    /// 每个链路的响应时间
    pub chain_response_times: Vec<u64>,
    /// 递归深度
    pub recursion_depth: u32,
    /// 客户端IP
    pub client_ip: String,
    /// 请求方法
    pub method: String,
    /// 最终响应大小（字节）
    pub response_size: u64,
    /// 错误信息（如有）
    pub error_message: Option<String>,
    /// 触发递归的原因
    pub trigger_reason: Option<String>,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
}

impl RecursiveChainHistory {
    pub fn new(
        original_url: String,
        final_url: String,
        chain: Vec<String>,
        status_code: u16,
        client_ip: String,
        method: String,
    ) -> Self {
        Self {
            id: None,
            original_url,
            final_url,
            chain_length: chain.len(),
            chain,
            status_code,
            from_cache: false,
            total_response_time_ms: 0,
            chain_response_times: Vec::new(),
            recursion_depth: 0,
            client_ip,
            method,
            response_size: 0,
            error_message: None,
            trigger_reason: None,
            created_at: chrono::Utc::now(),
        }
    }

    pub fn with_response_time(mut self, response_time_ms: u64) -> Self {
        self.total_response_time_ms = response_time_ms;
        self
    }

    pub fn with_chain_times(mut self, chain_times: Vec<u64>) -> Self {
        self.chain_response_times = chain_times;
        self
    }

    pub fn with_cache_status(mut self, from_cache: bool) -> Self {
        self.from_cache = from_cache;
        self
    }

    pub fn with_depth(mut self, depth: u32) -> Self {
        self.recursion_depth = depth;
        self
    }

    pub fn with_response_size(mut self, size: u64) -> Self {
        self.response_size = size;
        self
    }

    pub fn with_error(mut self, error: String) -> Self {
        self.error_message = Some(error);
        self
    }

    pub fn with_trigger_reason(mut self, reason: String) -> Self {
        self.trigger_reason = Some(reason);
        self
    }
}

/// 域名池使用统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DomainPoolUsageStats {
    pub id: Option<String>,
    /// 域名
    pub domain: String,
    /// 上游URL
    pub upstream_url: String,
    /// 使用次数
    pub usage_count: u64,
    /// 成功次数
    pub success_count: u64,
    /// 失败次数  
    pub failure_count: u64,
    /// 平均响应时间
    pub avg_response_time_ms: f64,
    /// 最后使用时间
    pub last_used_at: chrono::DateTime<chrono::Utc>,
    /// 统计时间窗口开始
    pub window_start: chrono::DateTime<chrono::Utc>,
    /// 统计时间窗口结束
    pub window_end: chrono::DateTime<chrono::Utc>,
}

impl DomainPoolUsageStats {
    pub fn new(domain: String, upstream_url: String) -> Self {
        let now = chrono::Utc::now();
        Self {
            id: None,
            domain,
            upstream_url,
            usage_count: 0,
            success_count: 0,
            failure_count: 0,
            avg_response_time_ms: 0.0,
            last_used_at: now,
            window_start: now,
            window_end: now,
        }
    }

    pub fn record_usage(&mut self, success: bool, response_time_ms: u64) {
        self.usage_count += 1;
        if success {
            self.success_count += 1;
        } else {
            self.failure_count += 1;
        }

        // 更新平均响应时间
        let total_time = self.avg_response_time_ms * (self.usage_count - 1) as f64;
        self.avg_response_time_ms =
            (total_time + response_time_ms as f64) / self.usage_count as f64;

        self.last_used_at = chrono::Utc::now();
    }
}

/// 递归代理配置历史
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveProxyConfigHistory {
    pub id: Option<String>,
    /// 配置版本
    pub version: u32,
    /// 配置内容（JSON格式）
    pub config_data: String,
    /// 变更原因
    pub change_reason: Option<String>,
    /// 操作用户
    pub changed_by: String,
    /// 是否为当前活跃配置
    pub is_active: bool,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// 递归链路查询过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveChainFilter {
    /// 按原始URL过滤
    pub original_url: Option<String>,
    /// 按客户端IP过滤
    pub client_ip: Option<String>,
    /// 按状态码过滤
    pub status_code: Option<u16>,
    /// 按递归深度过滤
    pub min_depth: Option<u32>,
    pub max_depth: Option<u32>,
    /// 按链路长度过滤
    pub min_chain_length: Option<usize>,
    pub max_chain_length: Option<usize>,
    /// 按时间范围过滤
    pub start_time: Option<chrono::DateTime<chrono::Utc>>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    /// 是否只显示缓存命中
    pub cache_hit_only: Option<bool>,
    /// 是否只显示有错误的记录
    pub error_only: Option<bool>,
}

/// 缓存规则配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheRule {
    /// 规则ID
    pub id: Option<mongodb::bson::oid::ObjectId>,
    /// 规则名称
    pub name: String,
    /// URL模式
    pub url_pattern: String,
    /// 缓存时间(秒)
    pub cache_duration: u64,
    /// 是否启用
    pub enabled: bool,
    /// 优先级
    pub priority: u32,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// 更新时间
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

impl Default for CacheRule {
    fn default() -> Self {
        let now = chrono::Utc::now();
        Self {
            id: None,
            name: "default".to_string(),
            url_pattern: ".*".to_string(),
            cache_duration: 3600, // 1小时
            enabled: true,
            priority: 1,
            created_at: now,
            updated_at: now,
        }
    }
}

/// 默认值函数
fn default_true() -> bool {
    true
}

// 自定义反序列化函数已移除，现在使用手动构建方式处理BSON数据
