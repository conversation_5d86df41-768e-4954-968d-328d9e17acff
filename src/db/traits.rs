use super::models::*;
use crate::types::ProxyResult;
use async_trait::async_trait;

/// 统一的数据库操作接口
#[async_trait]
pub trait DatabaseRepository: Send + Sync {
    // 健康检查
    async fn health_check(&self) -> ProxyResult<bool>;

    // 域名操作
    async fn insert_domain(&self, domain: &Domain) -> ProxyResult<String>;
    async fn get_domain(&self, domain: &str) -> ProxyResult<Option<Domain>>;
    async fn get_all_domains(&self) -> ProxyResult<Vec<Domain>>;
    async fn update_domain(&self, id: &str, domain: &Domain) -> ProxyResult<()>;
    async fn delete_domain(&self, id: &str) -> ProxyResult<()>;
    async fn batch_insert_domains(&self, domains: &[Domain]) -> ProxyResult<Vec<String>>;

    // 用户操作
    async fn insert_user(&self, user: &User) -> ProxyResult<String>;
    async fn get_user_by_username(&self, username: &str) -> ProxyResult<Option<User>>;
    async fn get_user_by_session(&self, session_token: &str) -> ProxyResult<Option<User>>;
    async fn update_user(&self, id: &str, user: &User) -> ProxyResult<()>;
    async fn delete_user(&self, id: &str) -> ProxyResult<()>;

    // 黑名单操作
    async fn add_to_blacklist(&self, entry: &BlacklistEntry) -> ProxyResult<String>;
    async fn is_ip_blacklisted(&self, ip: &str) -> ProxyResult<bool>;
    async fn remove_from_blacklist(&self, ip: &str) -> ProxyResult<()>;
    async fn cleanup_expired_blacklist(&self) -> ProxyResult<u64>;

    // 访问日志操作
    async fn log_access(&self, log: &AccessLog) -> ProxyResult<()>;
    async fn batch_log_access(&self, logs: &[AccessLog]) -> ProxyResult<()>;
    async fn cleanup_old_access_logs(&self, days: u32) -> ProxyResult<u64>;
}

/// 数据库配置接口
pub trait DatabaseConfig: Send + Sync {
    fn get_connection_string(&self) -> &str;
    fn get_max_connections(&self) -> u32;
    fn get_connection_timeout(&self) -> std::time::Duration;
}
