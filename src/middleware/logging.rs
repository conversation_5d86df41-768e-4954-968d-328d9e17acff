use super::{Middleware, Next};
use async_trait::async_trait;
use hyper::{Request, Response, Body};
use std::time::Instant;
use tracing::{info, error};

/// 请求日志中间件
pub struct LoggingMiddleware {
    log_body: bool,
    max_body_size: usize,
}

impl LoggingMiddleware {
    pub fn new(log_body: bool, max_body_size: usize) -> Self {
        Self {
            log_body,
            max_body_size,
        }
    }
}

#[async_trait]
impl Middleware for LoggingMiddleware {
    async fn handle(&self, req: Request<Body>, next: Next<'_>) -> hyper::Result<Response<Body>> {
        let start_time = Instant::now();
        let method = req.method().clone();
        let uri = req.uri().clone();
        
        info!(
            method = %method,
            uri = %uri,
            "Incoming request"
        );
        
        let response = next.run(req).await;
        
        match &response {
            Ok(resp) => {
                let duration = start_time.elapsed();
                info!(
                    method = %method,
                    uri = %uri,
                    status = %resp.status(),
                    duration_ms = duration.as_millis(),
                    "Request completed"
                );
            }
            Err(e) => {
                let duration = start_time.elapsed();
                error!(
                    method = %method,
                    uri = %uri,
                    error = %e,
                    duration_ms = duration.as_millis(),
                    "Request failed"
                );
            }
        }
        
        response
    }
}