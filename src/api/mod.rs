// API模块总体组织和公共结构

use axum::{
    extract::{Path, State},
    response::IntoResponse,
    routing::{get, post},
    Json, Router,
};
use std::sync::Arc;
// 移除anyhow::Result，统一用ProxyResult
// use anyhow::Result;
use tower_http::trace::TraceLayer;
// SimpleAppState导入已删除 - 现在统一使用AppState

// 导入各模块
// auth 模块已删除 - 使用简化认证系统
pub mod auto_proxy; // 统一的API模块，包含所有功能
mod blacklist;
mod config;
// mod csrf_token; // 暂时注释掉，文件不存在
// mod domain; // 域名管理API已整合到auto_proxy中
pub mod domain_group;
// pub mod domain_pool; // 暂时注释掉，文件不存在

mod security;
mod static_router;
mod status;

// 导入认证中间件
use crate::auth::jwt_auth_middleware;

/// API统一格式响应 - 使用统一的错误类型
#[derive(Debug, serde::Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub message: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error_code: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub request_id: Option<String>,
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            message: None,
            data: Some(data),
            error: None,
            error_code: None,
            request_id: None,
        }
    }

    /// 创建带消息的成功响应
    pub fn success_with_message(data: T, message: impl Into<String>) -> Self {
        Self {
            success: true,
            message: Some(message.into()),
            data: Some(data),
            error: None,
            error_code: None,
            request_id: None,
        }
    }

    /// 创建错误响应
    pub fn error_from_string(error: &str) -> ApiResponse<serde_json::Value> {
        ApiResponse {
            success: false,
            message: None,
            data: None,
            error: Some(error.to_string()),
            error_code: Some("GENERIC_ERROR".to_string()),
            request_id: None,
        }
    }

    /// 创建带有指定泛型类型的错误响应
    pub fn error_with_type<U>(error: &str) -> ApiResponse<U> {
        ApiResponse {
            success: false,
            message: None,
            data: None,
            error: Some(error.to_string()),
            error_code: Some("GENERIC_ERROR".to_string()),
            request_id: None,
        }
    }

    /// 创建自定义错误响应
    pub fn error(message: impl Into<String>) -> ApiResponse<serde_json::Value> {
        ApiResponse {
            success: false,
            message: None,
            data: None,
            error: Some(message.into()),
            error_code: Some("GENERIC_ERROR".to_string()),
            request_id: None,
        }
    }

    /// 添加请求ID
    pub fn with_request_id(mut self, request_id: impl Into<String>) -> Self {
        self.request_id = Some(request_id.into());
        self
    }

    /// 从代理错误创建错误响应
    pub fn error_from_proxy_error(error: &proxy_core::ProxyError) -> ApiResponse<T> {
        ApiResponse {
            success: false,
            data: None,
            message: Some(error.to_string()),
            error: Some(error.to_string()),
            error_code: Some("PROXY_ERROR".to_string()),
            request_id: None,
        }
    }

    /// 从本地代理错误创建错误响应
    pub fn error_from_local_proxy_error(error: &crate::types::ProxyError) -> ApiResponse<T> {
        ApiResponse {
            success: false,
            data: None,
            message: Some(error.to_string()),
            error: Some(error.to_string()),
            error_code: Some(format!("{:?}", error.kind)),
            request_id: None,
        }
    }

    /// 从任何错误类型创建错误响应
    pub fn error_any(error: impl std::fmt::Display) -> ApiResponse<T> {
        ApiResponse {
            success: false,
            data: None,
            message: Some(error.to_string()),
            error: Some(error.to_string()),
            error_code: Some("ERROR".to_string()),
            request_id: None,
        }
    }

    /// 兼容老版本的错误转换函数
    pub fn error_from_proxy_error_compat(error: &crate::types::ProxyError) -> ApiResponse<T> {
        Self::error_from_local_proxy_error(error)
    }
}

/// API结果类型 - 统一的API返回类型
pub type ApiResult<T> = std::result::Result<Json<ApiResponse<T>>, String>;

/// 便利函数 - 创建成功的API响应
pub fn api_success<T>(data: T) -> ApiResult<T> {
    Ok(Json(ApiResponse::success(data)))
}

/// 便利函数 - 创建成功的API响应（带消息）
pub fn api_success_with_message<T>(data: T, message: impl Into<String>) -> ApiResult<T> {
    Ok(Json(ApiResponse::success_with_message(data, message)))
}

/// 便利函数 - 创建错误的API响应
pub fn api_error<T>(error: String) -> ApiResult<T> {
    Err(error)
}

/// 健康检查响应
pub async fn health_check() -> impl IntoResponse {
    Json(ApiResponse::success("OK"))
}

/// API版本响应
pub async fn version() -> Json<ApiResponse<&'static str>> {
    Json(ApiResponse::success("Reverse Proxy API v1.0"))
}

// 注释掉复杂的路由器，使用简化版本
// pub fn create_api_router(state: Arc<AppState>) -> Router<()> { ... }

// 简化的路由器创建函数已删除 - 现在直接使用AppState

// create_router_with_services函数已删除 - 现在直接使用AppState

// 适配器函数已删除 - 现在直接使用AppState

// 适配器路由函数已删除 - 现在直接使用AppState



// ==================== 认证API已移至 api/auto_proxy.rs ====================
// 重复的认证API已删除，使用统一的auto_proxy.rs模块

// 认证相关函数已移至 api/auto_proxy.rs



// ==================== 域名组管理API已移至 api/auto_proxy.rs ====================
// 重复的域名组管理API已删除，使用统一的auto_proxy.rs模块

// ==================== 系统状态和配置管理API已移至 api/auto_proxy.rs ====================
// 重复的系统API已删除，使用统一的auto_proxy.rs模块

// ==================== 自动代理API已移至 api/auto_proxy.rs ====================
// 重复的自动代理API已删除，使用统一的auto_proxy.rs模块

// 更新自动代理配置函数已移至 api/auto_proxy.rs
