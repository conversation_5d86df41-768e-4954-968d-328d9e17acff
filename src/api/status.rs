use crate::api::ApiResponse;
use crate::types::AppState;
use axum::{extract::Extension, response::IntoResponse, Json};
use serde_json::json;
use std::sync::Arc;

/// 简化的系统状态检查
pub async fn get_status(Extension(_state): Extension<Arc<AppState>>) -> impl IntoResponse {
    Json(ApiResponse {
        success: true,
        message: Some("系统运行正常".to_string()),
        data: Some(json!({
            "status": "running",
            "version": env!("CARGO_PKG_VERSION"),
            "timestamp": chrono::Utc::now().to_rfc3339()
        })),
        error_code: None,
        request_id: None,
        error: None,
    })
}



/// 简化的系统统计
pub async fn get_system_stats(Extension(_state): Extension<Arc<AppState>>) -> impl IntoResponse {
    Json(ApiResponse {
        success: true,
        message: Some("获取系统统计成功".to_string()),
        data: Some(json!({
            "domains_count": 0,
            "active_proxies": 0,
            "requests_today": 0
        })),
        error_code: None,
        request_id: None,
        error: None,
    })
}





/// 简化的路由注册
pub fn routes() -> axum::Router<Arc<AppState>> {
    axum::Router::new()
        .route("/api/status", axum::routing::get(get_status))
        .route("/api/stats", axum::routing::get(get_system_stats))
}
