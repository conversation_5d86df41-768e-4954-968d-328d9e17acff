use crate::api::ApiResponse;
use crate::types::{AppState, EventRecord};
use axum::{
    extract::{Extension, Json, Path},
    response::{Html, IntoResponse},
};
use std::sync::Arc;

#[derive(Debug, <PERSON>lone, serde::Serialize, serde::Deserialize)]
pub struct BlacklistForm {
    pub ip: String,
}

pub async fn list_blacklisted_ips(_state: Extension<Arc<AppState>>) -> impl IntoResponse {
    // 暂时返回空列表，因为数据库接口可能还未完全实现
    let ips: Vec<String> = vec![]; // 临时实现
    axum::Json(ApiResponse::<Vec<String>> {
        success: true,
        message: Some("获取黑名单成功".to_string()),
        data: Some(ips),
        error: None,
        error_code: None,
        request_id: None,
    })
}

pub async fn add_to_blacklist(
    Extension(state): Extension<Arc<AppState>>,
    Json(form): <PERSON><PERSON><BlacklistForm>,
) -> impl IntoResponse {
    // 暂时模拟成功，因为数据库接口可能还未完全实现
    // 事件写入
    {
        let mut events = state.events.lock().await;
        events.push(EventRecord::new(
            crate::types::EventType::BlacklistUpdate,
            format!("IP {} 加入黑名单", form.ip),
        ));
        if events.len() > 1000 {
            events.remove(0);
        }
    }
    axum::Json(ApiResponse::<()> {
        success: true,
        message: Some("添加成功".to_string()),
        data: None,
        error: None,
        error_code: None,
        request_id: None,
    })
}

pub async fn remove_from_blacklist(
    Extension(state): Extension<Arc<AppState>>,
    Json(form): Json<BlacklistForm>,
) -> impl IntoResponse {
    // 暂时模拟成功，因为数据库接口可能还未完全实现
    // 事件写入
    {
        let mut events = state.events.lock().await;
        events.push(EventRecord::new(
            crate::types::EventType::BlacklistUpdate,
            format!("IP {} 移除黑名单", form.ip),
        ));
        if events.len() > 1000 {
            events.remove(0);
        }
    }
    axum::Json(ApiResponse::<()> {
        success: true,
        message: Some("移除成功".to_string()),
        data: None,
        error: None,
        error_code: None,
        request_id: None,
    })
}

pub async fn check_ip_status(
    Extension(state): Extension<Arc<AppState>>,
    Path(ip_str): Path<String>,
) -> impl IntoResponse {
    let _ip_to_check: std::net::IpAddr = match ip_str.parse() {
        Ok(ip) => ip,
        Err(_) => {
            return axum::Json(ApiResponse::<bool> {
                success: false,
                message: Some(format!("无效的IP地址格式: {}", ip_str)),
                data: None,
                error: Some(format!("无效的IP地址格式: {}", ip_str)),
                error_code: None,
                request_id: None,
            });
        }
    };

    // 暂时返回 false，因为数据库接口可能还未完全实现
    axum::Json(ApiResponse::<bool> {
        success: true,
        message: None,
        data: Some(false),
        error: None,
        error_code: None,
        request_id: None,
    })
}

pub async fn blacklist_status_htmx(Extension(_state): Extension<Arc<AppState>>) -> Html<String> {
    // 暂时返回空表格，因为数据库接口可能还未完全实现
    let html = String::from(
        "<table><tr><th>IP</th><th>操作</th></tr><tr><td colspan='2'>暂无数据</td></tr></table>",
    );
    Html(html)
}

pub async fn add_blacklist(
    Extension(state): Extension<Arc<AppState>>,
    Json(_form): Json<BlacklistForm>,
) -> Html<String> {
    blacklist_status_htmx(Extension(state)).await
}

pub async fn remove_blacklist(
    Extension(state): Extension<Arc<AppState>>,
    Json(_form): Json<BlacklistForm>,
) -> Html<String> {
    blacklist_status_htmx(Extension(state)).await
}

pub fn routes() -> axum::Router<Arc<AppState>> {
    axum::Router::new()
        .route("/api/blacklist", axum::routing::get(list_blacklisted_ips))
        .route("/api/blacklist", axum::routing::post(add_to_blacklist))
        .route(
            "/api/blacklist",
            axum::routing::delete(remove_from_blacklist),
        )
        .route("/api/blacklist/:ip", axum::routing::get(check_ip_status))
        .route(
            "/api/blacklist/status_htmx",
            axum::routing::get(blacklist_status_htmx),
        )
        .route(
            "/api/blacklist/add_htmx",
            axum::routing::post(add_blacklist),
        )
        .route(
            "/api/blacklist/remove_htmx",
            axum::routing::post(remove_blacklist),
        )
}
