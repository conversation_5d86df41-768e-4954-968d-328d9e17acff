/// DNS提供商抽象和实现
/// 支持阿里云DNS API进行DNS-01挑战

use std::sync::Arc;
use anyhow::{Result, anyhow};
use async_trait::async_trait;
use serde_json::Value;
use super::DnsProviderConfig;

/// DNS提供商trait
#[async_trait]
pub trait DnsProvider: Send + Sync {
    /// 添加TXT记录
    async fn add_txt_record(&self, domain: &str, name: &str, value: &str) -> Result<String>;
    
    /// 删除TXT记录
    async fn remove_txt_record(&self, domain: &str, name: &str) -> Result<()>;
    
    /// 查询TXT记录
    async fn get_txt_records(&self, domain: &str, name: &str) -> Result<Vec<String>>;
}

/// 创建DNS提供商实例
pub async fn create_dns_provider(config: DnsProviderConfig) -> Result<Arc<dyn DnsProvider>> {
    match config.provider.as_str() {
        "aliyun" => {
            let provider = AliyunDnsProvider::new(config).await?;
            Ok(Arc::new(provider))
        }
        _ => Err(anyhow!("不支持的DNS提供商: {}", config.provider)),
    }
}

/// 阿里云DNS提供商
pub struct AliyunDnsProvider {
    access_key_id: String,
    access_key_secret: String,
    region: String,
    client: reqwest::Client,
}

impl AliyunDnsProvider {
    /// 创建阿里云DNS提供商
    pub async fn new(config: DnsProviderConfig) -> Result<Self> {
        if config.access_key_id.is_empty() || config.access_key_secret.is_empty() {
            return Err(anyhow!("阿里云DNS配置不完整"));
        }
        
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()?;
        
        Ok(Self {
            access_key_id: config.access_key_id,
            access_key_secret: config.access_key_secret,
            region: config.region.unwrap_or_else(|| "cn-hangzhou".to_string()),
            client,
        })
    }

    /// 生成阿里云API签名
    fn generate_signature(&self, params: &std::collections::BTreeMap<String, String>) -> String {
        use hmac::{Hmac, Mac};
        use sha1::Sha1;
        
        // 构建规范化查询字符串
        let query_string: String = params
            .iter()
            .map(|(k, v)| format!("{}={}", self.url_encode(k), self.url_encode(v)))
            .collect::<Vec<_>>()
            .join("&");
        
        // 构建待签名字符串
        let string_to_sign = format!("GET&{}&{}", 
            self.url_encode("/"), 
            self.url_encode(&query_string)
        );
        
        // 计算HMAC-SHA1签名
        let signing_key = format!("{}&", self.access_key_secret);
        let mut mac = Hmac::<Sha1>::new_from_slice(signing_key.as_bytes()).unwrap();
        mac.update(string_to_sign.as_bytes());
        let signature = mac.finalize().into_bytes();
        
        base64::encode(signature)
    }

    /// URL编码
    fn url_encode(&self, input: &str) -> String {
        urlencoding::encode(input).to_string()
    }

    /// 调用阿里云DNS API
    async fn call_api(&self, action: &str, params: std::collections::BTreeMap<String, String>) -> Result<Value> {
        let mut all_params = params;
        
        // 添加公共参数
        all_params.insert("Action".to_string(), action.to_string());
        all_params.insert("Version".to_string(), "2015-01-09".to_string());
        all_params.insert("AccessKeyId".to_string(), self.access_key_id.clone());
        all_params.insert("SignatureMethod".to_string(), "HMAC-SHA1".to_string());
        all_params.insert("SignatureVersion".to_string(), "1.0".to_string());
        all_params.insert("SignatureNonce".to_string(), uuid::Uuid::new_v4().to_string());
        all_params.insert("Timestamp".to_string(), 
            chrono::Utc::now().format("%Y-%m-%dT%H:%M:%SZ").to_string());
        all_params.insert("Format".to_string(), "JSON".to_string());
        
        // 生成签名
        let signature = self.generate_signature(&all_params);
        all_params.insert("Signature".to_string(), signature);
        
        // 构建请求URL
        let endpoint = format!("https://alidns.{}.aliyuncs.com/", self.region);
        
        // 发送请求
        let response = self.client
            .get(&endpoint)
            .query(&all_params)
            .send()
            .await?;
        
        if !response.status().is_success() {
            return Err(anyhow!("阿里云DNS API请求失败: {}", response.status()));
        }
        
        let json: Value = response.json().await?;
        
        // 检查API响应错误
        if let Some(code) = json.get("Code") {
            if code.as_str() != Some("Success") {
                let message = json.get("Message")
                    .and_then(|m| m.as_str())
                    .unwrap_or("未知错误");
                return Err(anyhow!("阿里云DNS API错误: {} - {}", code, message));
            }
        }
        
        Ok(json)
    }

    /// 获取域名记录
    async fn get_domain_records(&self, domain: &str, record_type: &str, rr: &str) -> Result<Vec<Value>> {
        let mut params = std::collections::BTreeMap::new();
        params.insert("DomainName".to_string(), domain.to_string());
        params.insert("Type".to_string(), record_type.to_string());
        params.insert("RRKeyWord".to_string(), rr.to_string());
        
        let response = self.call_api("DescribeDomainRecords", params).await?;
        
        let records = response
            .get("DomainRecords")
            .and_then(|dr| dr.get("Record"))
            .and_then(|r| r.as_array())
            .unwrap_or(&vec![])
            .clone();
        
        Ok(records)
    }
}

#[async_trait]
impl DnsProvider for AliyunDnsProvider {
    async fn add_txt_record(&self, domain: &str, name: &str, value: &str) -> Result<String> {
        // 提取记录名称（去掉域名后缀）
        let rr = if name.ends_with(&format!(".{}", domain)) {
            name.strip_suffix(&format!(".{}", domain)).unwrap_or(name)
        } else {
            name
        };
        
        let mut params = std::collections::BTreeMap::new();
        params.insert("DomainName".to_string(), domain.to_string());
        params.insert("RR".to_string(), rr.to_string());
        params.insert("Type".to_string(), "TXT".to_string());
        params.insert("Value".to_string(), value.to_string());
        params.insert("TTL".to_string(), "600".to_string());
        
        let response = self.call_api("AddDomainRecord", params).await?;
        
        let record_id = response
            .get("RecordId")
            .and_then(|id| id.as_str())
            .ok_or_else(|| anyhow!("无法获取记录ID"))?;
        
        tracing::info!("成功添加DNS TXT记录: {} = {}, ID: {}", name, value, record_id);
        Ok(record_id.to_string())
    }

    async fn remove_txt_record(&self, domain: &str, name: &str) -> Result<()> {
        // 提取记录名称
        let rr = if name.ends_with(&format!(".{}", domain)) {
            name.strip_suffix(&format!(".{}", domain)).unwrap_or(name)
        } else {
            name
        };
        
        // 先查询记录ID
        let records = self.get_domain_records(domain, "TXT", rr).await?;
        
        for record in records {
            if let Some(record_id) = record.get("RecordId").and_then(|id| id.as_str()) {
                let mut params = std::collections::BTreeMap::new();
                params.insert("RecordId".to_string(), record_id.to_string());
                
                self.call_api("DeleteDomainRecord", params).await?;
                tracing::info!("成功删除DNS TXT记录: {}, ID: {}", name, record_id);
            }
        }
        
        Ok(())
    }

    async fn get_txt_records(&self, domain: &str, name: &str) -> Result<Vec<String>> {
        let rr = if name.ends_with(&format!(".{}", domain)) {
            name.strip_suffix(&format!(".{}", domain)).unwrap_or(name)
        } else {
            name
        };
        
        let records = self.get_domain_records(domain, "TXT", rr).await?;
        
        let txt_values: Vec<String> = records
            .iter()
            .filter_map(|record| {
                record.get("Value").and_then(|v| v.as_str()).map(|s| s.to_string())
            })
            .collect();
        
        Ok(txt_values)
    }
}

/// 模拟DNS提供商（用于测试）
pub struct MockDnsProvider {
    records: std::sync::Arc<tokio::sync::RwLock<std::collections::HashMap<String, String>>>,
}

impl MockDnsProvider {
    pub fn new() -> Self {
        Self {
            records: std::sync::Arc::new(tokio::sync::RwLock::new(std::collections::HashMap::new())),
        }
    }
}

#[async_trait]
impl DnsProvider for MockDnsProvider {
    async fn add_txt_record(&self, _domain: &str, name: &str, value: &str) -> Result<String> {
        let mut records = self.records.write().await;
        let record_id = uuid::Uuid::new_v4().to_string();
        records.insert(name.to_string(), value.to_string());
        tracing::info!("模拟添加DNS TXT记录: {} = {}", name, value);
        Ok(record_id)
    }

    async fn remove_txt_record(&self, _domain: &str, name: &str) -> Result<()> {
        let mut records = self.records.write().await;
        records.remove(name);
        tracing::info!("模拟删除DNS TXT记录: {}", name);
        Ok(())
    }

    async fn get_txt_records(&self, _domain: &str, name: &str) -> Result<Vec<String>> {
        let records = self.records.read().await;
        if let Some(value) = records.get(name) {
            Ok(vec![value.clone()])
        } else {
            Ok(vec![])
        }
    }
}
