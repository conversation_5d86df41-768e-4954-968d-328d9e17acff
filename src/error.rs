//! 应用层错误处理 - 统一版本
use anyhow::Result;

/// 统一的应用错误类型
#[derive(thiserror::Error, Debug)]
pub enum AppError {
    #[error("应用配置错误: {0}")]
    AppConfig(String),

    #[error("环境变量错误: {0}")]
    Environment(String),

    #[error("启动错误: {0}")]
    Startup(String),

    #[error("数据库错误: {0}")]
    Database(String),

    #[error("网络错误: {0}")]
    Network(String),

    #[error("认证错误: {0}")]
    Auth(String),

    #[error("授权错误: {0}")]
    Authorization(String),

    #[error("验证错误: {0}")]
    Validation(String),

    #[error("安全错误: {0}")]
    Security(String),

    #[error("缓存错误: {0}")]
    Cache(String),

    #[error("代理错误: {0}")]
    Proxy(String),

    #[error("递归代理错误: {0}")]
    RecursiveProxy(String),

    #[error("域名池错误: {0}")]
    DomainPool(String),

    #[error("内部错误: {0}")]
    Internal(String),

    #[error("输入无效: {0}")]
    InvalidInput(String),

    #[error("资源未找到: {0}")]
    NotFound(String),

    #[error("操作冲突: {0}")]
    Conflict(String),

    #[error("服务不可用: {0}")]
    ServiceUnavailable(String),

    #[error("超时: {0}")]
    Timeout(String),

    #[error("限流: {0}")]
    RateLimit(String),
}

// 注意：anyhow已经为所有实现std::error::Error的类型提供了From转换
// 所以不需要手动实现From<AppError> for anyhow::Error

// 便利函数 - 创建各种类型的错误
impl AppError {
    pub fn app_config(msg: impl Into<String>) -> Self {
        Self::AppConfig(msg.into())
    }

    pub fn environment(msg: impl Into<String>) -> Self {
        Self::Environment(msg.into())
    }

    pub fn startup(msg: impl Into<String>) -> Self {
        Self::Startup(msg.into())
    }

    pub fn database(msg: impl Into<String>) -> Self {
        Self::Database(msg.into())
    }

    pub fn network(msg: impl Into<String>) -> Self {
        Self::Network(msg.into())
    }

    pub fn auth(msg: impl Into<String>) -> Self {
        Self::Auth(msg.into())
    }

    pub fn authorization(msg: impl Into<String>) -> Self {
        Self::Authorization(msg.into())
    }

    pub fn validation(msg: impl Into<String>) -> Self {
        Self::Validation(msg.into())
    }

    pub fn security(msg: impl Into<String>) -> Self {
        Self::Security(msg.into())
    }

    pub fn cache(msg: impl Into<String>) -> Self {
        Self::Cache(msg.into())
    }

    pub fn proxy(msg: impl Into<String>) -> Self {
        Self::Proxy(msg.into())
    }

    pub fn recursive_proxy(msg: impl Into<String>) -> Self {
        Self::RecursiveProxy(msg.into())
    }

    pub fn domain_pool(msg: impl Into<String>) -> Self {
        Self::DomainPool(msg.into())
    }

    pub fn internal(msg: impl Into<String>) -> Self {
        Self::Internal(msg.into())
    }

    pub fn invalid_input(msg: impl Into<String>) -> Self {
        Self::InvalidInput(msg.into())
    }

    pub fn not_found(msg: impl Into<String>) -> Self {
        Self::NotFound(msg.into())
    }

    pub fn conflict(msg: impl Into<String>) -> Self {
        Self::Conflict(msg.into())
    }

    pub fn service_unavailable(msg: impl Into<String>) -> Self {
        Self::ServiceUnavailable(msg.into())
    }

    pub fn timeout(msg: impl Into<String>) -> Self {
        Self::Timeout(msg.into())
    }

    pub fn rate_limit(msg: impl Into<String>) -> Self {
        Self::RateLimit(msg.into())
    }

    /// 获取错误类型
    pub fn error_type(&self) -> &'static str {
        match self {
            Self::AppConfig(_) => "APP_CONFIG",
            Self::Environment(_) => "ENVIRONMENT",
            Self::Startup(_) => "STARTUP",
            Self::Database(_) => "DATABASE",
            Self::Network(_) => "NETWORK",
            Self::Auth(_) => "AUTH",
            Self::Authorization(_) => "AUTHORIZATION",
            Self::Validation(_) => "VALIDATION",
            Self::Security(_) => "SECURITY",
            Self::Cache(_) => "CACHE",
            Self::Proxy(_) => "PROXY",
            Self::RecursiveProxy(_) => "RECURSIVE_PROXY",
            Self::DomainPool(_) => "DOMAIN_POOL",
            Self::Internal(_) => "INTERNAL",
            Self::InvalidInput(_) => "INVALID_INPUT",
            Self::NotFound(_) => "NOT_FOUND",
            Self::Conflict(_) => "CONFLICT",
            Self::ServiceUnavailable(_) => "SERVICE_UNAVAILABLE",
            Self::Timeout(_) => "TIMEOUT",
            Self::RateLimit(_) => "RATE_LIMIT",
        }
    }

    /// 判断是否为客户端错误
    pub fn is_client_error(&self) -> bool {
        matches!(
            self,
            Self::Auth(_)
                | Self::Authorization(_)
                | Self::Validation(_)
                | Self::InvalidInput(_)
                | Self::NotFound(_)
                | Self::Conflict(_)
                | Self::RateLimit(_)
        )
    }

    /// 判断是否为服务器错误
    pub fn is_server_error(&self) -> bool {
        !self.is_client_error()
    }

    /// 获取HTTP状态码
    pub fn http_status_code(&self) -> u16 {
        match self {
            Self::Auth(_) => 401,
            Self::Authorization(_) => 403,
            Self::NotFound(_) => 404,
            Self::Conflict(_) => 409,
            Self::Validation(_) | Self::InvalidInput(_) => 400,
            Self::RateLimit(_) => 429,
            Self::ServiceUnavailable(_) => 503,
            Self::Timeout(_) => 504,
            _ => 500,
        }
    }
}

// 错误转换实现
impl From<mongodb::error::Error> for AppError {
    fn from(err: mongodb::error::Error) -> Self {
        Self::Database(err.to_string())
    }
}

impl From<reqwest::Error> for AppError {
    fn from(err: reqwest::Error) -> Self {
        Self::Network(err.to_string())
    }
}

impl From<serde_json::Error> for AppError {
    fn from(err: serde_json::Error) -> Self {
        Self::Internal(format!("JSON序列化错误: {}", err))
    }
}

impl From<std::io::Error> for AppError {
    fn from(err: std::io::Error) -> Self {
        Self::Internal(format!("IO错误: {}", err))
    }
}

impl From<url::ParseError> for AppError {
    fn from(err: url::ParseError) -> Self {
        Self::InvalidInput(format!("URL解析错误: {}", err))
    }
}

impl From<tokio::time::error::Elapsed> for AppError {
    fn from(err: tokio::time::error::Elapsed) -> Self {
        Self::Timeout(format!("操作超时: {}", err))
    }
}

// 与现有错误类型的兼容性
impl From<crate::types::ProxyError> for AppError {
    fn from(err: crate::types::ProxyError) -> Self {
        match err.kind {
            crate::types::ProxyErrorKind::InvalidInput => Self::InvalidInput(err.message),
            crate::types::ProxyErrorKind::Network => Self::Network(err.message),
            crate::types::ProxyErrorKind::Database => Self::Database(err.message),
            crate::types::ProxyErrorKind::Authentication => Self::Auth(err.message),
            crate::types::ProxyErrorKind::Security => Self::Security(err.message),
            crate::types::ProxyErrorKind::InvalidOperation => Self::InvalidInput(err.message),
            crate::types::ProxyErrorKind::Internal => Self::Internal(err.message),
        }
    }
}

impl From<proxy_core::ProxyError> for AppError {
    fn from(err: proxy_core::ProxyError) -> Self {
        Self::Proxy(err.to_string())
    }
}

// 为向后兼容保留的类型别名
pub type AppResult<T> = Result<T, AppError>;

/// 结果扩展trait，提供便利方法
pub trait AppResultExt<T> {
    /// 添加上下文信息
    fn with_context(self, context: &str) -> AppResult<T>;

    /// 映射错误类型
    fn map_err_to_app<F>(self, f: F) -> AppResult<T>
    where
        F: FnOnce() -> AppError;
}

impl<T, E> AppResultExt<T> for Result<T, E>
where
    E: Into<AppError>,
{
    fn with_context(self, context: &str) -> AppResult<T> {
        self.map_err(|e| {
            let app_err = e.into();
            AppError::internal(format!("{}: {}", context, app_err))
        })
    }

    fn map_err_to_app<F>(self, f: F) -> AppResult<T>
    where
        F: FnOnce() -> AppError,
    {
        self.map_err(|_| f())
    }
}

/// 便利宏 - 简化错误转换
#[macro_export]
macro_rules! app_error {
    (config: $msg:expr) => {
        AppError::app_config($msg)
    };
    (env: $msg:expr) => {
        AppError::environment($msg)
    };
    (startup: $msg:expr) => {
        AppError::startup($msg)
    };
    (database: $msg:expr) => {
        AppError::database($msg)
    };
    (network: $msg:expr) => {
        AppError::network($msg)
    };
    (auth: $msg:expr) => {
        AppError::auth($msg)
    };
    (validation: $msg:expr) => {
        AppError::validation($msg)
    };
    (security: $msg:expr) => {
        AppError::security($msg)
    };
    (internal: $msg:expr) => {
        AppError::internal($msg)
    };
    ($msg:expr) => {
        AppError::internal($msg)
    };
}
