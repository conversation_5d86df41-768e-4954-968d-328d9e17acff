//! 优化版本实现模块
//! 
//! 这个模块包含所有优化版本的实现，与现有代码并行存在
//! 通过配置开关可以选择使用优化版本或原版本
//! 默认使用原版本，确保系统稳定性

pub mod events;



use std::env;

/// 优化配置
#[derive(Debug, Clone)]
pub struct OptimizationConfig {
    /// 是否使用优化的事件系统
    pub use_optimized_events: bool,
    /// 是否使用优化的缓存系统
    pub use_optimized_cache: bool,
    /// 是否使用优化的域名处理
    pub use_optimized_domains: bool,
    /// 是否启用性能监控
    pub enable_monitoring: bool,
}

impl Default for OptimizationConfig {
    fn default() -> Self {
        Self {
            // 默认关闭所有优化，使用原有实现确保稳定性
            use_optimized_events: false,
            use_optimized_cache: false,
            use_optimized_domains: false,
            enable_monitoring: true, // 监控可以安全开启
        }
    }
}

impl OptimizationConfig {
    /// 从环境变量读取配置
    pub fn from_env() -> Self {
        Self {
            use_optimized_events: env::var("SM_USE_OPTIMIZED_EVENTS")
                .unwrap_or_default() == "true",
            use_optimized_cache: env::var("SM_USE_OPTIMIZED_CACHE")
                .unwrap_or_default() == "true",
            use_optimized_domains: env::var("SM_USE_OPTIMIZED_DOMAINS")
                .unwrap_or_default() == "true",
            enable_monitoring: env::var("SM_ENABLE_MONITORING")
                .unwrap_or("true".to_string()) == "true",
        }
    }
    
    /// 创建安全的测试配置（只启用监控）
    pub fn safe_test() -> Self {
        Self {
            use_optimized_events: false,
            use_optimized_cache: false,
            use_optimized_domains: false,
            enable_monitoring: true,
        }
    }
    
    /// 创建渐进式优化配置（只启用事件系统优化）
    pub fn progressive_events() -> Self {
        Self {
            use_optimized_events: true,
            use_optimized_cache: false,
            use_optimized_domains: false,
            enable_monitoring: true,
        }
    }
    
    /// 创建渐进式优化配置（启用事件和缓存优化）
    pub fn progressive_cache() -> Self {
        Self {
            use_optimized_events: true,
            use_optimized_cache: true,
            use_optimized_domains: false,
            enable_monitoring: true,
        }
    }
    
    /// 创建全面优化配置
    pub fn full_optimization() -> Self {
        Self {
            use_optimized_events: true,
            use_optimized_cache: true,
            use_optimized_domains: true,
            enable_monitoring: true,
        }
    }
    
    /// 检查是否启用了任何优化
    pub fn has_any_optimization(&self) -> bool {
        self.use_optimized_events || self.use_optimized_cache || self.use_optimized_domains
    }
    
    /// 获取优化状态描述
    pub fn get_status_description(&self) -> String {
        let mut enabled = Vec::new();
        
        if self.use_optimized_events {
            enabled.push("事件系统");
        }
        if self.use_optimized_cache {
            enabled.push("缓存系统");
        }
        if self.use_optimized_domains {
            enabled.push("域名处理");
        }
        if self.enable_monitoring {
            enabled.push("性能监控");
        }
        
        if enabled.is_empty() {
            "使用原版实现".to_string()
        } else {
            format!("已启用优化: {}", enabled.join(", "))
        }
    }
}

/// 全局优化配置实例
use std::sync::OnceLock;

static GLOBAL_CONFIG: OnceLock<OptimizationConfig> = OnceLock::new();

/// 获取全局优化配置
pub fn get_optimization_config() -> &'static OptimizationConfig {
    GLOBAL_CONFIG.get_or_init(|| OptimizationConfig::from_env())
}

/// 初始化优化配置
pub fn init_optimization_config(config: OptimizationConfig) -> Result<(), OptimizationConfig> {
    GLOBAL_CONFIG.set(config)
}

/// 检查特定功能是否启用优化
pub fn is_events_optimized() -> bool {
    get_optimization_config().use_optimized_events
}

pub fn is_cache_optimized() -> bool {
    get_optimization_config().use_optimized_cache
}

pub fn is_domains_optimized() -> bool {
    get_optimization_config().use_optimized_domains
}

pub fn is_monitoring_enabled() -> bool {
    get_optimization_config().enable_monitoring
}

/// 优化状态报告
#[derive(Debug, Clone)]
pub struct OptimizationStatus {
    pub config: OptimizationConfig,
    pub description: String,
    pub safety_level: SafetyLevel,
}

#[derive(Debug, Clone, PartialEq)]
pub enum SafetyLevel {
    /// 完全安全 - 只有监控，不影响现有功能
    Safe,
    /// 低风险 - 单个组件优化
    LowRisk,
    /// 中等风险 - 多个组件优化
    MediumRisk,
    /// 高风险 - 全面优化
    HighRisk,
}

impl OptimizationStatus {
    pub fn current() -> Self {
        let config = get_optimization_config().clone();
        let description = config.get_status_description();
        
        let safety_level = match (
            config.use_optimized_events,
            config.use_optimized_cache,
            config.use_optimized_domains,
        ) {
            (false, false, false) => SafetyLevel::Safe,
            (true, false, false) | (false, true, false) | (false, false, true) => SafetyLevel::LowRisk,
            (true, true, false) | (true, false, true) | (false, true, true) => SafetyLevel::MediumRisk,
            (true, true, true) => SafetyLevel::HighRisk,
        };
        
        Self {
            config,
            description,
            safety_level,
        }
    }
    
    pub fn is_safe(&self) -> bool {
        matches!(self.safety_level, SafetyLevel::Safe | SafetyLevel::LowRisk)
    }
}

/// 优化模块初始化
pub fn initialize() -> Result<(), Box<dyn std::error::Error>> {
    let config = OptimizationConfig::from_env();
    
    // 记录优化状态
    tracing::info!("🚀 优化模块初始化");
    tracing::info!("📊 {}", config.get_status_description());
    
    // 如果启用了监控，初始化监控系统
    if config.enable_monitoring {
        tracing::info!("📈 性能监控已启用");
        // 这里可以添加监控系统的初始化代码
    }
    
    // 如果启用了任何优化，显示警告
    if config.has_any_optimization() {
        tracing::warn!("⚠️  已启用性能优化功能，请确保在测试环境中验证");
        tracing::warn!("🔄 如需回退到原版实现，请设置环境变量为false");
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_default_config() {
        let config = OptimizationConfig::default();
        assert!(!config.use_optimized_events);
        assert!(!config.use_optimized_cache);
        assert!(!config.use_optimized_domains);
        assert!(config.enable_monitoring);
        assert!(!config.has_any_optimization());
    }
    
    #[test]
    fn test_progressive_configs() {
        let config = OptimizationConfig::progressive_events();
        assert!(config.use_optimized_events);
        assert!(!config.use_optimized_cache);
        assert!(!config.use_optimized_domains);
        
        let config = OptimizationConfig::progressive_cache();
        assert!(config.use_optimized_events);
        assert!(config.use_optimized_cache);
        assert!(!config.use_optimized_domains);
        
        let config = OptimizationConfig::full_optimization();
        assert!(config.use_optimized_events);
        assert!(config.use_optimized_cache);
        assert!(config.use_optimized_domains);
    }
    
    #[test]
    fn test_safety_levels() {
        let status = OptimizationStatus {
            config: OptimizationConfig::default(),
            description: "test".to_string(),
            safety_level: SafetyLevel::Safe,
        };
        assert!(status.is_safe());
        
        let status = OptimizationStatus {
            config: OptimizationConfig::full_optimization(),
            description: "test".to_string(),
            safety_level: SafetyLevel::HighRisk,
        };
        assert!(!status.is_safe());
    }
}
