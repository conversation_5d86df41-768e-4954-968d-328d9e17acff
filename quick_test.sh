#!/bin/bash

# SM智能代理系统 - 快速功能测试
# 快速验证核心功能是否按需求工作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[✅]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[⚠️]${NC} $1"; }
log_error() { echo -e "${RED}[❌]${NC} $1"; }

echo "🚀 SM智能代理系统 - 快速功能测试"
echo "=================================="

# 1. 编译检查
log_info "1. 检查代码编译..."
if cargo check --quiet; then
    log_success "代码编译通过"
else
    log_error "代码编译失败"
    exit 1
fi

# 2. 运行单元测试
log_info "2. 运行单元测试..."
if cargo test --lib --quiet; then
    log_success "单元测试通过"
else
    log_warning "单元测试有失败项"
fi

# 3. 检查核心模块
log_info "3. 检查核心模块结构..."

# 检查关键文件是否存在
key_files=(
    "src/auto_proxy/mod.rs"
    "src/domain_mapping/mod.rs"
    "src/ssl_manager/mod.rs"
    "src/integrated_proxy/mod.rs"
    "src/security/mod.rs"
    "src/api/auto_proxy.rs"
    "frontend/index.html"
)

for file in "${key_files[@]}"; do
    if [[ -f "$file" ]]; then
        log_success "核心文件存在: $file"
    else
        log_error "核心文件缺失: $file"
    fi
done

# 4. 检查配置文件
log_info "4. 检查配置文件..."
if [[ -f "config.yaml" ]]; then
    log_success "配置文件存在"
else
    log_warning "配置文件不存在，将使用默认配置"
fi

# 5. 检查前端文件
log_info "5. 检查前端文件..."
frontend_files=(
    "frontend/index.html"
    "frontend/auto-proxy.html"
    "frontend/ssl-monitor.html"
    "frontend/dashboard.html"
    "frontend/js/navigation.js"
)

for file in "${frontend_files[@]}"; do
    if [[ -f "$file" ]]; then
        log_success "前端文件存在: $file"
    else
        log_warning "前端文件缺失: $file"
    fi
done

# 6. 检查依赖项
log_info "6. 检查Rust依赖项..."
if cargo tree --quiet >/dev/null 2>&1; then
    log_success "依赖项检查通过"
else
    log_warning "依赖项可能有问题"
fi

# 7. 安全功能测试
log_info "7. 测试安全功能..."

# 检查SecurityEvent结构是否按要求精简
if grep -q "pub metadata: HashMap" src/security/mod.rs; then
    log_error "SecurityEvent仍包含metadata字段，未按要求精简"
else
    log_success "SecurityEvent已按要求精简"
fi

# 检查文件扩展名是否按要求精简
if grep -A5 "allowed_extensions" src/api/static_router.rs | grep -q "woff"; then
    log_error "文件扩展名未按要求精简，仍包含字体文件"
else
    log_success "文件扩展名已按要求精简"
fi

# 8. 构建测试
log_info "8. 测试构建..."
if cargo build --release --quiet; then
    log_success "Release构建成功"
    
    # 检查二进制文件大小
    if [[ -f "target/release/sm" ]]; then
        size=$(du -h target/release/sm | cut -f1)
        log_success "二进制文件大小: $size"
    fi
else
    log_error "Release构建失败"
fi

# 9. 快速启动测试
log_info "9. 快速启动测试..."
timeout 10s ./target/release/sm --help >/dev/null 2>&1 && {
    log_success "程序可以正常启动"
} || {
    log_warning "程序启动测试超时或失败"
}

# 10. 总结
echo "=================================="
log_info "快速测试完成！"

# 检查是否有错误
if grep -q "❌" /tmp/test_output.log 2>/dev/null; then
    log_error "发现错误，请检查上述输出"
    exit 1
else
    log_success "🎉 快速测试通过！系统基本功能正常"
fi

echo ""
echo "💡 提示："
echo "   - 运行 './test_runner.sh' 进行完整测试"
echo "   - 运行 'cargo test' 进行详细单元测试"
echo "   - 运行 './target/release/sm' 启动服务"
