//! 高性能缓存系统
//!
//! 使用DashMap无锁数据结构替代传统的HashMap+RwLock
//! 大幅减少锁竞争，提升并发性能

#[cfg(feature = "high-performance-cache")]
use dashmap::DashMap;

use serde::{Deserialize, Serialize};
use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time::interval;

/// 缓存条目
#[derive(Debug)]
pub struct CacheEntry<T> {
    /// 缓存的数据
    pub data: T,
    /// 创建时间
    pub created_at: Instant,
    /// 过期时间
    pub expires_at: Instant,
    /// 命中次数
    pub hit_count: AtomicU64,
    /// 数据大小（字节）
    pub size: usize,
    /// 最后访问时间
    pub last_accessed: std::sync::RwLock<Instant>,
}

impl<T> CacheEntry<T> {
    pub fn new(data: T, ttl: Duration) -> Self {
        let now = Instant::now();
        let size = std::mem::size_of_val(&data);

        Self {
            data,
            created_at: now,
            expires_at: now + ttl,
            hit_count: AtomicU64::new(0),
            size,
            last_accessed: std::sync::RwLock::new(now),
        }
    }

    pub fn is_expired(&self) -> bool {
        Instant::now() > self.expires_at
    }

    pub fn record_hit(&self) {
        self.hit_count.fetch_add(1, Ordering::Relaxed);
        if let Ok(mut last_accessed) = self.last_accessed.write() {
            *last_accessed = Instant::now();
        }
    }

    pub fn get_hit_count(&self) -> u64 {
        self.hit_count.load(Ordering::Relaxed)
    }
}

/// 高性能缓存配置
#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// 最大条目数
    pub max_entries: usize,
    /// 最大内存使用（字节）
    pub max_memory_bytes: usize,
    /// 默认TTL
    pub default_ttl: Duration,
    /// 清理间隔
    pub cleanup_interval: Duration,
    /// LRU清理阈值
    pub lru_threshold: f64,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_entries: 10000,
            max_memory_bytes: 100 * 1024 * 1024,       // 100MB
            default_ttl: Duration::from_secs(3600),    // 1小时
            cleanup_interval: Duration::from_secs(60), // 1分钟
            lru_threshold: 0.8,                        // 80%时触发LRU清理
        }
    }
}

/// 高性能缓存系统
pub struct HighPerformanceCache<T> {
    #[cfg(feature = "high-performance-cache")]
    storage: Arc<DashMap<String, CacheEntry<T>>>,
    #[cfg(not(feature = "high-performance-cache"))]
    storage: Arc<tokio::sync::RwLock<std::collections::HashMap<String, CacheEntry<T>>>>,

    // 原子统计信息，避免锁
    total_hits: AtomicU64,
    total_requests: AtomicU64,
    total_entries: AtomicUsize,
    total_memory: AtomicUsize,

    config: CacheConfig,
}

impl<T: Clone + Send + Sync + 'static> HighPerformanceCache<T> {
    pub fn new(config: CacheConfig) -> Self {
        let cache = Self {
            #[cfg(feature = "high-performance-cache")]
            storage: Arc::new(DashMap::new()),
            #[cfg(not(feature = "high-performance-cache"))]
            storage: Arc::new(tokio::sync::RwLock::new(std::collections::HashMap::new())),

            total_hits: AtomicU64::new(0),
            total_requests: AtomicU64::new(0),
            total_entries: AtomicUsize::new(0),
            total_memory: AtomicUsize::new(0),
            config,
        };

        // 启动后台清理任务
        cache.start_cleanup_task();
        cache
    }

    /// 获取缓存项
    pub async fn get(&self, key: &str) -> Option<T> {
        self.total_requests.fetch_add(1, Ordering::Relaxed);

        #[cfg(feature = "high-performance-cache")]
        {
            if let Some(mut entry) = self.storage.get_mut(key) {
                if !entry.is_expired() {
                    entry.record_hit();
                    self.total_hits.fetch_add(1, Ordering::Relaxed);
                    return Some(entry.data.clone());
                } else {
                    // 异步删除过期条目
                    let storage = self.storage.clone();
                    let key = key.to_string();
                    let total_entries = self.total_entries.clone();
                    let total_memory = self.total_memory.clone();
                    tokio::spawn(async move {
                        if let Some((_, entry)) = storage.remove(&key) {
                            total_entries.fetch_sub(1, Ordering::Relaxed);
                            total_memory.fetch_sub(entry.size, Ordering::Relaxed);
                        }
                    });
                }
            }
        }

        #[cfg(not(feature = "high-performance-cache"))]
        {
            let mut storage = self.storage.write().await;
            if let Some(entry) = storage.get_mut(key) {
                if !entry.is_expired() {
                    entry.record_hit();
                    self.total_hits.fetch_add(1, Ordering::Relaxed);
                    return Some(entry.data.clone());
                } else {
                    let removed = storage.remove(key);
                    if let Some(entry) = removed {
                        self.total_entries.fetch_sub(1, Ordering::Relaxed);
                        self.total_memory.fetch_sub(entry.size, Ordering::Relaxed);
                    }
                }
            }
        }

        None
    }

    /// 插入缓存项
    pub async fn insert(&self, key: String, data: T) -> Result<(), CacheError> {
        self.insert_with_ttl(key, data, self.config.default_ttl)
            .await
    }

    /// 插入缓存项（指定TTL）
    pub async fn insert_with_ttl(
        &self,
        key: String,
        data: T,
        ttl: Duration,
    ) -> Result<(), CacheError> {
        let entry = CacheEntry::new(data, ttl);

        // 检查内存限制
        let current_memory = self.total_memory.load(Ordering::Relaxed);
        if current_memory + entry.size > self.config.max_memory_bytes {
            return Err(CacheError::MemoryLimitExceeded);
        }

        // 检查条目数限制
        let current_entries = self.total_entries.load(Ordering::Relaxed);
        if current_entries >= self.config.max_entries {
            return Err(CacheError::EntryLimitExceeded);
        }

        #[cfg(feature = "high-performance-cache")]
        {
            let size = entry.size;
            self.storage.insert(key, entry);
            self.total_entries.fetch_add(1, Ordering::Relaxed);
            self.total_memory.fetch_add(size, Ordering::Relaxed);
        }

        #[cfg(not(feature = "high-performance-cache"))]
        {
            let mut storage = self.storage.write().await;
            let size = entry.size;
            storage.insert(key, entry);
            self.total_entries.fetch_add(1, Ordering::Relaxed);
            self.total_memory.fetch_add(size, Ordering::Relaxed);
        }

        Ok(())
    }

    /// 删除缓存项
    pub async fn remove(&self, key: &str) -> Option<T> {
        #[cfg(feature = "high-performance-cache")]
        {
            if let Some((_, entry)) = self.storage.remove(key) {
                self.total_entries.fetch_sub(1, Ordering::Relaxed);
                self.total_memory.fetch_sub(entry.size, Ordering::Relaxed);
                return Some(entry.data);
            }
        }

        #[cfg(not(feature = "high-performance-cache"))]
        {
            let mut storage = self.storage.write().await;
            if let Some(entry) = storage.remove(key) {
                self.total_entries.fetch_sub(1, Ordering::Relaxed);
                self.total_memory.fetch_sub(entry.size, Ordering::Relaxed);
                return Some(entry.data);
            }
        }

        None
    }

    /// 清空缓存
    pub async fn clear(&self) {
        #[cfg(feature = "high-performance-cache")]
        {
            self.storage.clear();
        }

        #[cfg(not(feature = "high-performance-cache"))]
        {
            let mut storage = self.storage.write().await;
            storage.clear();
        }

        self.total_entries.store(0, Ordering::Relaxed);
        self.total_memory.store(0, Ordering::Relaxed);
    }

    /// 获取缓存统计信息
    pub fn get_stats(&self) -> CacheStats {
        let hits = self.total_hits.load(Ordering::Relaxed);
        let requests = self.total_requests.load(Ordering::Relaxed);

        CacheStats {
            total_hits: hits,
            total_requests: requests,
            hit_rate: if requests > 0 {
                hits as f64 / requests as f64
            } else {
                0.0
            },
            total_entries: self.total_entries.load(Ordering::Relaxed),
            total_memory_bytes: self.total_memory.load(Ordering::Relaxed),
            memory_usage_percent: if self.config.max_memory_bytes > 0 {
                self.total_memory.load(Ordering::Relaxed) as f64
                    / self.config.max_memory_bytes as f64
                    * 100.0
            } else {
                0.0
            },
        }
    }

    /// 启动后台清理任务
    fn start_cleanup_task(&self) {
        let storage = self.storage.clone();
        let total_entries = Arc::new(AtomicUsize::new(0));
        let total_memory = Arc::new(AtomicUsize::new(0));
        let cleanup_interval = self.config.cleanup_interval;

        tokio::spawn(async move {
            let mut interval = interval(cleanup_interval);
            loop {
                interval.tick().await;

                #[cfg(feature = "high-performance-cache")]
                {
                    let mut removed_entries = 0;
                    let mut removed_memory = 0;

                    // 清理过期条目
                    storage.retain(|_key, entry| {
                        if entry.is_expired() {
                            removed_entries += 1;
                            removed_memory += entry.size;
                            false
                        } else {
                            true
                        }
                    });

                    if removed_entries > 0 {
                        total_entries.fetch_sub(removed_entries, Ordering::Relaxed);
                        total_memory.fetch_sub(removed_memory, Ordering::Relaxed);
                    }
                }

                #[cfg(not(feature = "high-performance-cache"))]
                {
                    if let Ok(mut storage) = storage.try_write() {
                        let mut to_remove = Vec::new();
                        let mut removed_memory = 0;

                        for (key, entry) in storage.iter() {
                            if entry.is_expired() {
                                to_remove.push(key.clone());
                                removed_memory += entry.size;
                            }
                        }

                        for key in to_remove {
                            storage.remove(&key);
                        }

                        if removed_memory > 0 {
                            total_entries.fetch_sub(
                                removed_memory / std::mem::size_of::<CacheEntry<T>>(),
                                Ordering::Relaxed,
                            );
                            total_memory.fetch_sub(removed_memory, Ordering::Relaxed);
                        }
                    }
                }
            }
        });
    }
}

/// 缓存错误类型
#[derive(Debug, Clone)]
pub enum CacheError {
    MemoryLimitExceeded,
    EntryLimitExceeded,
    SerializationError,
    DeserializationError,
}

impl std::fmt::Display for CacheError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CacheError::MemoryLimitExceeded => write!(f, "缓存内存限制已达到"),
            CacheError::EntryLimitExceeded => write!(f, "缓存条目数限制已达到"),
            CacheError::SerializationError => write!(f, "序列化错误"),
            CacheError::DeserializationError => write!(f, "反序列化错误"),
        }
    }
}

impl std::error::Error for CacheError {}

/// 缓存统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    pub total_hits: u64,
    pub total_requests: u64,
    pub hit_rate: f64,
    pub total_entries: usize,
    pub total_memory_bytes: usize,
    pub memory_usage_percent: f64,
}

impl CacheStats {
    /// 检查缓存性能是否正常
    pub fn is_healthy(&self) -> bool {
        // 命中率应该大于50%，内存使用应该小于90%
        self.hit_rate > 0.5 && self.memory_usage_percent < 90.0
    }

    /// 获取性能建议
    pub fn get_recommendations(&self) -> Vec<String> {
        let mut recommendations = Vec::new();

        if self.hit_rate < 0.5 {
            recommendations.push("缓存命中率较低，建议检查缓存策略".to_string());
        }

        if self.memory_usage_percent > 80.0 {
            recommendations.push("内存使用率较高，建议增加清理频率".to_string());
        }

        if self.total_requests > 0 && self.total_hits == 0 {
            recommendations.push("缓存未命中任何请求，建议检查缓存配置".to_string());
        }

        recommendations
    }
}

/// 缓存系统适配器 - 提供与原系统兼容的接口
pub struct CacheAdapter<T> {
    optimized: Option<Arc<HighPerformanceCache<T>>>,
    // 这里可以添加对原始缓存系统的引用
}

impl<T: Clone + Send + Sync + 'static> CacheAdapter<T> {
    pub fn new(use_optimized: bool, config: Option<CacheConfig>) -> Self {
        Self {
            optimized: if use_optimized {
                Some(Arc::new(HighPerformanceCache::new(
                    config.unwrap_or_default(),
                )))
            } else {
                None
            },
        }
    }

    /// 获取缓存项 - 自动选择使用优化版本或原版本
    pub async fn get(&self, key: &str) -> Option<T> {
        if let Some(optimized) = &self.optimized {
            optimized.get(key).await
        } else {
            // 这里可以调用原始缓存系统
            // 目前返回None表示未命中
            None
        }
    }

    /// 插入缓存项
    pub async fn insert(&self, key: String, data: T) -> Result<(), CacheError> {
        if let Some(optimized) = &self.optimized {
            optimized.insert(key, data).await
        } else {
            // 这里可以调用原始缓存系统
            // 目前直接返回成功
            Ok(())
        }
    }

    /// 删除缓存项
    pub async fn remove(&self, key: &str) -> Option<T> {
        if let Some(optimized) = &self.optimized {
            optimized.remove(key).await
        } else {
            // 这里可以调用原始缓存系统
            None
        }
    }

    /// 获取缓存统计信息
    pub fn get_stats(&self) -> CacheStats {
        if let Some(optimized) = &self.optimized {
            optimized.get_stats()
        } else {
            // 返回默认统计信息
            CacheStats {
                total_hits: 0,
                total_requests: 0,
                hit_rate: 0.0,
                total_entries: 0,
                total_memory_bytes: 0,
                memory_usage_percent: 0.0,
            }
        }
    }

    /// 检查是否使用优化版本
    pub fn is_optimized(&self) -> bool {
        self.optimized.is_some()
    }

    /// 清空缓存
    pub async fn clear(&self) {
        if let Some(optimized) = &self.optimized {
            optimized.clear().await;
        }
        // 原始缓存系统的清空操作
    }
}
