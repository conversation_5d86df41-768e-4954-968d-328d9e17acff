//! 优化版本的事件系统
//!
//! 使用无锁队列和压缩数据结构来提升性能
//! 与原有事件系统接口兼容，可以无缝替换

#[cfg(feature = "lockfree-events")]
use crossbeam::queue::SegQueue;

use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::sync::atomic::{AtomicU32, AtomicU64, AtomicUsize, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;

use crate::types::{EventRecord, EventSeverity, EventType};

/// 压缩的事件记录 - 大幅减少内存使用
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompactEventRecord {
    /// 事件ID - 使用u64而不是String
    pub id: u64,
    /// 事件类型 - 使用u8而不是enum
    pub event_type: u8,
    /// 用户ID - 使用u32而不是String
    pub user_id: Option<u32>,
    /// 域名ID - 使用字符串池中的ID
    pub domain_id: Option<u32>,
    /// IP地址 - 压缩为u32
    pub ip_address: Option<u32>,
    /// 消息ID - 使用字符串池中的ID
    pub message_id: u32,
    /// 时间戳 - Unix时间戳
    pub timestamp: u64,
    /// 严重程度 - 使用u8而不是enum
    pub severity: u8,
}

impl CompactEventRecord {
    /// 从原始EventRecord转换
    pub fn from_event_record(event: &EventRecord, string_pool: &StringPool) -> Self {
        Self {
            id: event.id.parse().unwrap_or_else(|_| {
                use std::collections::hash_map::DefaultHasher;
                use std::hash::{Hash, Hasher};
                let mut hasher = DefaultHasher::new();
                event.id.hash(&mut hasher);
                hasher.finish()
            }),
            event_type: event.event_type as u8,
            user_id: event.user_id.as_ref().and_then(|s| s.parse().ok()),
            domain_id: event.domain.as_ref().map(|s| string_pool.intern(s)),
            ip_address: event
                .ip_address
                .as_ref()
                .and_then(|s| s.parse::<std::net::Ipv4Addr>().ok().map(|ip| ip.into())),
            message_id: string_pool.intern(&event.message),
            timestamp: event.timestamp.timestamp() as u64,
            severity: event.severity as u8,
        }
    }

    /// 转换回原始EventRecord
    pub fn to_event_record(&self, string_pool: &StringPool) -> EventRecord {
        EventRecord {
            id: self.id.to_string(),
            event_type: match self.event_type {
                0 => EventType::Authentication,
                1 => EventType::ProxyRequest,
                2 => EventType::ConfigChange,
                3 => EventType::SecurityAlert,
                4 => EventType::SystemError,
                5 => EventType::DomainAdded,
                6 => EventType::RecursiveProxy,
                _ => EventType::SystemError,
            },
            user_id: self.user_id.map(|id| id.to_string()),
            domain: self.domain_id.and_then(|id| string_pool.get(id)),
            ip_address: self
                .ip_address
                .map(|ip| std::net::Ipv4Addr::from(ip).to_string()),
            user_agent: None, // 压缩版本不保存user_agent
            message: string_pool.get(self.message_id).unwrap_or_default(),
            details: None, // 压缩版本不保存details
            timestamp: chrono::DateTime::from_timestamp(self.timestamp as i64, 0)
                .unwrap_or_default(),
            severity: match self.severity {
                0 => EventSeverity::Debug,
                1 => EventSeverity::Info,
                2 => EventSeverity::Warn,
                3 => EventSeverity::Error,
                4 => EventSeverity::Critical,
                _ => EventSeverity::Info,
            },
        }
    }
}

/// 字符串池 - 减少字符串重复存储
pub struct StringPool {
    strings: std::sync::RwLock<std::collections::HashMap<String, u32>>,
    reverse: std::sync::RwLock<std::collections::HashMap<u32, String>>,
    next_id: AtomicU32,
}

impl StringPool {
    pub fn new() -> Self {
        Self {
            strings: std::sync::RwLock::new(std::collections::HashMap::new()),
            reverse: std::sync::RwLock::new(std::collections::HashMap::new()),
            next_id: AtomicU32::new(1),
        }
    }

    /// 将字符串加入池中，返回ID
    pub fn intern(&self, s: &str) -> u32 {
        // 先尝试读锁查找
        if let Ok(strings) = self.strings.read() {
            if let Some(&id) = strings.get(s) {
                return id;
            }
        }

        // 需要写入新字符串
        if let Ok(mut strings) = self.strings.write() {
            // 双重检查，防止并发写入
            if let Some(&id) = strings.get(s) {
                return id;
            }

            let id = self.next_id.fetch_add(1, Ordering::Relaxed);
            strings.insert(s.to_string(), id);

            if let Ok(mut reverse) = self.reverse.write() {
                reverse.insert(id, s.to_string());
            }

            id
        } else {
            0 // 错误情况下返回0
        }
    }

    /// 根据ID获取字符串
    pub fn get(&self, id: u32) -> Option<String> {
        self.reverse.read().ok()?.get(&id).cloned()
    }
}

/// 优化版本的事件系统
pub struct OptimizedEventSystem {
    #[cfg(feature = "lockfree-events")]
    write_queue: Arc<SegQueue<CompactEventRecord>>,
    #[cfg(not(feature = "lockfree-events"))]
    write_queue: Arc<tokio::sync::Mutex<VecDeque<CompactEventRecord>>>,

    read_buffer: Arc<RwLock<VecDeque<CompactEventRecord>>>,
    string_pool: Arc<StringPool>,

    // 统计信息
    total_events: AtomicU64,
    queue_length: AtomicUsize,

    // 配置
    max_buffer_size: usize,
    batch_size: usize,
}

impl OptimizedEventSystem {
    pub fn new(max_buffer_size: usize) -> Self {
        let system = Self {
            #[cfg(feature = "lockfree-events")]
            write_queue: Arc::new(SegQueue::new()),
            #[cfg(not(feature = "lockfree-events"))]
            write_queue: Arc::new(tokio::sync::Mutex::new(VecDeque::new())),

            read_buffer: Arc::new(RwLock::new(VecDeque::new())),
            string_pool: Arc::new(StringPool::new()),
            total_events: AtomicU64::new(0),
            queue_length: AtomicUsize::new(0),
            max_buffer_size,
            batch_size: 100,
        };

        // 启动后台处理任务
        system.start_background_processor();
        system
    }

    /// 添加事件 - 高性能无锁操作
    pub fn add_event(&self, event: EventRecord) {
        let compact_event = CompactEventRecord::from_event_record(&event, &self.string_pool);

        #[cfg(feature = "lockfree-events")]
        {
            self.write_queue.push(compact_event);
        }

        #[cfg(not(feature = "lockfree-events"))]
        {
            // 如果没有启用lockfree-events特性，使用异步Mutex
            let write_queue = self.write_queue.clone();
            let compact_event = compact_event;
            tokio::spawn(async move {
                if let Ok(mut queue) = write_queue.try_lock() {
                    queue.push_back(compact_event);
                }
            });
        }

        self.queue_length.fetch_add(1, Ordering::Relaxed);
        self.total_events.fetch_add(1, Ordering::Relaxed);
    }

    /// 获取最近的事件
    pub async fn get_recent_events(&self, limit: usize) -> Vec<EventRecord> {
        let buffer = self.read_buffer.read().await;
        buffer
            .iter()
            .rev()
            .take(limit)
            .map(|e| e.to_event_record(&self.string_pool))
            .collect()
    }

    /// 获取事件统计信息
    pub fn get_stats(&self) -> EventSystemStats {
        EventSystemStats {
            total_events: self.total_events.load(Ordering::Relaxed),
            queue_length: self.queue_length.load(Ordering::Relaxed),
            buffer_size: self.read_buffer.try_read().map(|b| b.len()).unwrap_or(0),
            string_pool_size: self.string_pool.next_id.load(Ordering::Relaxed) as usize,
        }
    }

    /// 启动后台处理任务
    fn start_background_processor(&self) {
        let write_queue = self.write_queue.clone();
        let read_buffer = self.read_buffer.clone();
        let queue_length = Arc::new(AtomicUsize::new(0));
        queue_length.store(self.queue_length.load(Ordering::Relaxed), Ordering::Relaxed);
        let max_buffer_size = self.max_buffer_size;
        let batch_size = self.batch_size;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_millis(50));
            loop {
                interval.tick().await;

                // 批量处理写队列
                let mut batch = Vec::new();
                let mut processed = 0;

                #[cfg(feature = "lockfree-events")]
                {
                    while let Some(event) = write_queue.pop() {
                        batch.push(event);
                        processed += 1;
                        if batch.len() >= batch_size {
                            break;
                        }
                    }
                }

                #[cfg(not(feature = "lockfree-events"))]
                {
                    if let Ok(mut queue) = write_queue.try_lock() {
                        while let Some(event) = queue.pop_front() {
                            batch.push(event);
                            processed += 1;
                            if batch.len() >= batch_size {
                                break;
                            }
                        }
                    }
                }

                if !batch.is_empty() {
                    {
                        let mut buffer = read_buffer.write().await;
                        for event in batch {
                            buffer.push_back(event);
                        }

                        // 保持缓冲区大小
                        while buffer.len() > max_buffer_size {
                            buffer.pop_front();
                        }
                    }

                    queue_length.fetch_sub(processed, Ordering::Relaxed);
                }
            }
        });
    }
}

/// 事件系统统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventSystemStats {
    pub total_events: u64,
    pub queue_length: usize,
    pub buffer_size: usize,
    pub string_pool_size: usize,
}

/// 事件系统适配器 - 提供与原系统兼容的接口
pub struct EventSystemAdapter {
    optimized: Option<Arc<OptimizedEventSystem>>,
    original: Arc<tokio::sync::Mutex<Vec<EventRecord>>>, // 原系统的引用
}

impl EventSystemAdapter {
    pub fn new(use_optimized: bool, original: Arc<tokio::sync::Mutex<Vec<EventRecord>>>) -> Self {
        Self {
            optimized: if use_optimized {
                Some(Arc::new(OptimizedEventSystem::new(1000)))
            } else {
                None
            },
            original,
        }
    }

    /// 添加事件 - 自动选择使用优化版本或原版本
    pub async fn add_event(&self, event: EventRecord) {
        if let Some(optimized) = &self.optimized {
            optimized.add_event(event);
        } else {
            // 使用原系统
            let mut events = self.original.lock().await;
            events.push(event);
            if events.len() > 1000 {
                events.drain(0..100);
            }
        }
    }

    /// 获取最近的事件
    pub async fn get_recent_events(&self, limit: usize) -> Vec<EventRecord> {
        if let Some(optimized) = &self.optimized {
            optimized.get_recent_events(limit).await
        } else {
            // 使用原系统
            let events = self.original.lock().await;
            events.iter().rev().take(limit).cloned().collect()
        }
    }

    /// 检查是否使用优化版本
    pub fn is_optimized(&self) -> bool {
        self.optimized.is_some()
    }
}
