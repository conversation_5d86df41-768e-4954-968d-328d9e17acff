//! 内容过滤器模块

use super::config::FilterConfig;
use anyhow::Result;
use regex::Regex;
use std::collections::HashSet;

/// 内容过滤器
pub struct ContentFilter {
    config: FilterConfig,
    compiled_regexes: Vec<Regex>,
    blacklist_set: HashSet<String>,
}

impl ContentFilter {
    /// 创建新的内容过滤器
    pub fn new(config: FilterConfig) -> Result<Self> {
        let mut compiled_regexes = Vec::new();

        // 编译正则表达式
        for pattern in &config.regex_blacklist {
            match Regex::new(pattern) {
                Ok(regex) => compiled_regexes.push(regex),
                Err(e) => {
                    tracing::warn!("无效的正则表达式 '{}': {}", pattern, e);
                }
            }
        }

        // 创建黑名单集合
        let mut blacklist_set = HashSet::new();
        blacklist_set.extend(config.famous_sites_blacklist.iter().cloned());
        blacklist_set.extend(config.custom_blacklist.iter().cloned());

        Ok(Self {
            config,
            compiled_regexes,
            blacklist_set,
        })
    }

    /// 过滤URL列表
    pub fn filter_urls(&self, urls: Vec<String>) -> Vec<String> {
        urls.into_iter()
            .filter(|url| self.is_url_allowed(url))
            .collect()
    }

    /// 过滤域名列表
    pub fn filter_domains(&self, domains: Vec<String>) -> Vec<String> {
        domains
            .into_iter()
            .filter(|domain| self.is_domain_allowed(domain))
            .collect()
    }

    /// 检查URL是否被允许
    pub fn is_url_allowed(&self, url: &str) -> bool {
        // 检查关键词黑名单
        if self.contains_blacklisted_keywords(url) {
            return false;
        }

        // 检查正则表达式黑名单
        if self.matches_regex_blacklist(url) {
            return false;
        }

        // 提取域名并检查域名黑名单
        if let Ok(domain) = self.extract_domain_from_url(url) {
            return self.is_domain_allowed(&domain);
        }

        true
    }

    /// 检查域名是否被允许
    pub fn is_domain_allowed(&self, domain: &str) -> bool {
        let domain_lower = domain.to_lowercase();

        // 检查知名网站黑名单
        if self.is_famous_site(&domain_lower) {
            return false;
        }

        // 检查国家域名黑名单
        if self.is_country_blocked(&domain_lower) {
            return false;
        }

        // 检查自定义黑名单
        if self.blacklist_set.contains(&domain_lower) {
            return false;
        }

        true
    }

    /// 检查是否包含黑名单关键词
    fn contains_blacklisted_keywords(&self, url: &str) -> bool {
        let url_lower = url.to_lowercase();

        for keyword in &self.config.keyword_blacklist {
            if url_lower.contains(&keyword.to_lowercase()) {
                return true;
            }
        }

        false
    }

    /// 检查是否匹配正则表达式黑名单
    fn matches_regex_blacklist(&self, url: &str) -> bool {
        for regex in &self.compiled_regexes {
            if regex.is_match(url) {
                return true;
            }
        }

        false
    }

    /// 检查是否为知名网站
    fn is_famous_site(&self, domain: &str) -> bool {
        for famous_site in &self.config.famous_sites_blacklist {
            let famous_site_lower = famous_site.to_lowercase();

            // 精确匹配或子域名匹配
            if domain == famous_site_lower || domain.ends_with(&format!(".{}", famous_site_lower)) {
                return true;
            }
        }

        false
    }

    /// 检查是否为被阻止的国家域名
    fn is_country_blocked(&self, domain: &str) -> bool {
        for country_tld in &self.config.country_blacklist {
            if domain.ends_with(country_tld) {
                return true;
            }
        }

        false
    }

    /// 从URL中提取域名
    fn extract_domain_from_url(&self, url: &str) -> Result<String> {
        use url::Url;
        let parsed_url = Url::parse(url)?;
        Ok(parsed_url.host_str().unwrap_or("").to_string())
    }

    /// 添加自定义黑名单域名
    pub fn add_custom_blacklist(&mut self, domain: String) {
        self.blacklist_set.insert(domain.to_lowercase());
    }

    /// 移除自定义黑名单域名
    pub fn remove_custom_blacklist(&mut self, domain: &str) {
        self.blacklist_set.remove(&domain.to_lowercase());
    }

    /// 添加关键词黑名单
    pub fn add_keyword_blacklist(&mut self, keyword: String) {
        self.config.keyword_blacklist.push(keyword);
    }

    /// 添加正则表达式黑名单
    pub fn add_regex_blacklist(&mut self, pattern: String) -> Result<()> {
        let regex = Regex::new(&pattern)?;
        self.compiled_regexes.push(regex);
        self.config.regex_blacklist.push(pattern);
        Ok(())
    }

    /// 获取过滤统计信息
    pub fn get_filter_stats(&self) -> FilterStats {
        FilterStats {
            keyword_blacklist_count: self.config.keyword_blacklist.len(),
            regex_blacklist_count: self.config.regex_blacklist.len(),
            country_blacklist_count: self.config.country_blacklist.len(),
            famous_sites_blacklist_count: self.config.famous_sites_blacklist.len(),
            custom_blacklist_count: self.blacklist_set.len(),
        }
    }
}

/// 过滤统计信息
#[derive(Debug, Clone)]
pub struct FilterStats {
    pub keyword_blacklist_count: usize,
    pub regex_blacklist_count: usize,
    pub country_blacklist_count: usize,
    pub famous_sites_blacklist_count: usize,
    pub custom_blacklist_count: usize,
}
