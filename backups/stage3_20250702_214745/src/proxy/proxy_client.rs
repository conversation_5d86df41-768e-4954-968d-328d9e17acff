//! 代理客户端模块 - 支持第三方代理IP和浏览器伪装

use anyhow::{anyhow, Result};
use rand::seq::IteratorRandom;
use rand::seq::SliceRandom;
use reqwest;
use serde::Serialize;
use std::collections::HashMap;
use std::sync::atomic::Ordering;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;

use super::config::{BrowserConfig, ProxyConfig, ProxyServer, ProxyType};

/// 代理客户端
pub struct ProxyClient {
    /// HTTP客户端
    clients: Vec<reqwest::Client>,
    /// 代理配置
    proxy_config: ProxyConfig,
    /// 浏览器配置
    browser_config: BrowserConfig,
    /// 轮询计数器
    round_robin_counter: Arc<std::sync::atomic::AtomicUsize>,
    /// 连接统计
    connection_stats: Arc<tokio::sync::RwLock<HashMap<String, ConnectionStats>>>,
}

#[derive(Debug, Clone, Serialize)]
/// 连接统计信息
pub struct ConnectionStats {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub average_response_time: u64,
    pub last_used: Option<String>,
}

impl Default for ConnectionStats {
    fn default() -> Self {
        Self {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            average_response_time: 0,
            last_used: Some(chrono::Utc::now().to_rfc3339()),
        }
    }
}

impl ProxyClient {
    /// 创建新的代理客户端
    pub async fn new(proxy_config: ProxyConfig, browser_config: BrowserConfig) -> Result<Self> {
        let mut clients = Vec::new();

        if proxy_config.use_proxy && !proxy_config.proxy_servers.is_empty() {
            // 为每个代理服务器创建客户端
            for proxy_server in &proxy_config.proxy_servers {
                if proxy_server.enabled {
                    if let Ok(client) =
                        Self::create_proxy_client(proxy_server, &browser_config).await
                    {
                        clients.push(client);
                    }
                }
            }
        }

        // 如果没有可用的代理或不使用代理，创建直连客户端
        if clients.is_empty() {
            clients.push(Self::create_direct_client(&browser_config).await?);
        }

        Ok(Self {
            clients,
            proxy_config,
            browser_config,
            round_robin_counter: Arc::new(std::sync::atomic::AtomicUsize::new(0)),
            connection_stats: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
        })
    }

    /// 创建代理客户端
    async fn create_proxy_client(
        proxy_server: &ProxyServer,
        browser_config: &BrowserConfig,
    ) -> Result<reqwest::Client> {
        let proxy_url = match proxy_server.proxy_type {
            ProxyType::Http => format!("http://{}:{}", proxy_server.host, proxy_server.port),
            ProxyType::Https => format!("https://{}:{}", proxy_server.host, proxy_server.port),
            ProxyType::Socks4 => format!("socks4://{}:{}", proxy_server.host, proxy_server.port),
            ProxyType::Socks5 => format!("socks5://{}:{}", proxy_server.host, proxy_server.port),
        };

        let mut proxy = reqwest::Proxy::all(&proxy_url)?;

        // 设置代理认证
        if let (Some(username), Some(password)) = (&proxy_server.username, &proxy_server.password) {
            proxy = proxy.basic_auth(username, password);
        }

        let mut builder = reqwest::ClientBuilder::new()
            .proxy(proxy)
            .timeout(Duration::from_secs(30))
            .redirect(reqwest::redirect::Policy::limited(10));

        // 设置默认请求头
        let mut headers = reqwest::header::HeaderMap::new();
        for (key, value) in &browser_config.default_headers {
            if let (Ok(header_name), Ok(header_value)) = (
                reqwest::header::HeaderName::from_bytes(key.as_bytes()),
                reqwest::header::HeaderValue::from_str(value),
            ) {
                headers.insert(header_name, header_value);
            }
        }
        builder = builder.default_headers(headers);

        Ok(builder.build()?)
    }

    /// 创建直连客户端
    async fn create_direct_client(browser_config: &BrowserConfig) -> Result<reqwest::Client> {
        let mut builder = reqwest::ClientBuilder::new()
            .timeout(Duration::from_secs(30))
            .redirect(reqwest::redirect::Policy::limited(10));

        // 设置默认请求头
        let mut headers = reqwest::header::HeaderMap::new();
        for (key, value) in &browser_config.default_headers {
            if let (Ok(header_name), Ok(header_value)) = (
                reqwest::header::HeaderName::from_bytes(key.as_bytes()),
                reqwest::header::HeaderValue::from_str(value),
            ) {
                headers.insert(header_name, header_value);
            }
        }
        builder = builder.default_headers(headers);

        Ok(builder.build()?)
    }

    /// 发送HTTP请求
    pub async fn request(
        &self,
        method: &str,
        url: &str,
        headers: Option<HashMap<String, String>>,
        body: Option<Vec<u8>>,
    ) -> Result<reqwest::Response> {
        let start_time = std::time::Instant::now();

        // 选择客户端
        let client = self.select_client().await?;

        // 构建请求
        let mut request_builder = match method.to_uppercase().as_str() {
            "GET" => client.get(url),
            "POST" => client.post(url),
            "PUT" => client.put(url),
            "DELETE" => client.delete(url),
            "HEAD" => client.head(url),
            "PATCH" => client.patch(url),
            _ => return Err(anyhow!("不支持的HTTP方法: {}", method)),
        };

        // 设置随机User-Agent
        if self.browser_config.random_user_agent && !self.browser_config.user_agents.is_empty() {
            let mut rng = rand::thread_rng();
            if let Some(user_agent) = self.browser_config.user_agents.choose(&mut rng) {
                request_builder = request_builder.header("User-Agent", user_agent);
            }
        }

        // 设置自定义请求头
        if let Some(custom_headers) = headers {
            for (key, value) in custom_headers {
                request_builder = request_builder.header(&key, &value);
            }
        }

        // 设置请求体
        if let Some(body_data) = body {
            request_builder = request_builder.body(body_data);
        }

        // 模拟浏览器行为 - 添加请求延迟
        if self.browser_config.simulate_browser && self.browser_config.request_delay_ms > 0 {
            sleep(Duration::from_millis(self.browser_config.request_delay_ms)).await;
        }

        // 发送请求
        let response = request_builder.send().await?;

        // 更新统计信息
        let response_time = start_time.elapsed();
        self.update_stats(url, true, response_time).await;

        Ok(response)
    }

    /// 选择客户端
    async fn select_client(&self) -> Result<&reqwest::Client> {
        if self.clients.is_empty() {
            return Err(anyhow!("没有可用的HTTP客户端"));
        }

        // 简化为轮询策略，移除复杂的负载均衡
        let index = {
            let current = self.round_robin_counter.fetch_add(1, Ordering::Relaxed);
            current % self.clients.len()
        };

        Ok(&self.clients[index])
    }

    /// 选择连接数最少的客户端
    async fn select_least_connections_client(&self) -> usize {
        let stats = self.connection_stats.read().await;

        let mut min_connections = u64::MAX;
        let mut selected_index = 0;

        for (i, _) in self.clients.iter().enumerate() {
            let total_requests = stats.values().nth(i).map_or(0, |s| s.total_requests);

            if total_requests < min_connections {
                min_connections = total_requests;
                selected_index = i;
            }
        }

        selected_index
    }

    /// 更新统计信息
    async fn update_stats(&self, url: &str, success: bool, response_time: Duration) {
        let mut stats = self.connection_stats.write().await;
        let key = url.to_string();

        let entry = stats.entry(key).or_insert_with(ConnectionStats::default);
        entry.total_requests += 1;
        entry.last_used = Some(chrono::Utc::now().to_rfc3339());

        if success {
            entry.successful_requests += 1;
        } else {
            entry.failed_requests += 1;
        }

        // 更新平均响应时间
        let current_avg = entry.average_response_time;
        let new_avg = if current_avg == 0 {
            response_time.as_millis() as u64
        } else {
            (current_avg + response_time.as_millis() as u64) / 2
        };
        entry.average_response_time = new_avg;
    }

    /// 获取连接统计信息
    pub async fn get_stats(&self) -> HashMap<String, ConnectionStats> {
        self.connection_stats.read().await.clone()
    }

    /// 测试代理连接
    pub async fn test_proxy_connection(&self, test_url: &str) -> Result<bool> {
        match self.request("GET", test_url, None, None).await {
            Ok(response) => Ok(response.status().is_success()),
            Err(_) => Ok(false),
        }
    }

    /// 获取可用的代理数量
    pub fn get_proxy_count(&self) -> usize {
        self.clients.len()
    }
}
