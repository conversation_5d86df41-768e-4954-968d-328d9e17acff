//! 递归代理上下文管理

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 创建递归步骤的参数结构体
#[derive(Debug, Clone)]
pub struct StepParams {
    pub url: String,
    pub method: String,
    pub status_code: u16,
    pub response_time_ms: u64,
    pub from_cache: bool,
    pub discovered_count: usize,
    pub error: Option<String>,
    pub redirect_url: Option<String>,
}

/// 创建递归响应的参数结构体
#[derive(Debug, Clone)]
pub struct ResponseParams {
    pub status_code: u16,
    pub headers: HashMap<String, String>,
    pub body: Vec<u8>,
    pub content_type: String,
    pub from_cache: bool,
    pub chain: Vec<String>,
    pub domains: Vec<String>,
    pub processing_time_ms: u64,
    pub final_url: String,
}
use std::time::Instant;

/// 递归代理上下文
#[derive(Debug, Clone)]
pub struct RecursiveContext {
    /// 当前递归深度
    pub current_depth: u32,
    /// 原始请求URL
    pub original_url: String,
    /// 上游链路
    pub upstream_chain: Vec<String>,
    /// 开始时间
    pub start_time: Instant,
    /// 客户端IP
    pub client_ip: String,
    /// 发现的域名
    pub domains: Vec<String>,
    /// 域名映射关系
    pub mappings: HashMap<String, String>,
    /// 请求头
    pub headers: HashMap<String, String>,
    /// 会话ID
    pub session_id: String,
}

/// 递归代理响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveResponse {
    /// HTTP状态码
    pub status_code: u16,
    /// 响应头
    pub headers: HashMap<String, String>,
    /// 响应体
    pub body: Vec<u8>,
    /// 内容类型
    pub content_type: String,
    /// 是否来自缓存
    pub from_cache: bool,
    /// 递归链路
    pub chain: Vec<String>,
    /// 发现的新域名
    pub domains: Vec<String>,
    /// 处理时间（毫秒）
    pub processing_time_ms: u64,
    /// 最终URL（跟踪重定向后）
    pub final_url: String,
}

/// 递归步骤信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveStep {
    /// 步骤序号
    pub step_number: u32,
    /// 请求URL
    pub url: String,
    /// HTTP方法
    pub method: String,
    /// 状态码
    pub status_code: u16,
    /// 响应时间（毫秒）
    pub response_time_ms: u64,
    /// 是否来自缓存
    pub from_cache: bool,
    /// 发现的域名数量
    pub discovered_domains_count: usize,
    /// 错误信息（如果有）
    pub error: Option<String>,
    /// 重定向URL（如果有）
    pub redirect_url: Option<String>,
}

/// 递归会话信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveSession {
    /// 会话ID
    pub session_id: String,
    /// 原始URL
    pub original_url: String,
    /// 客户端IP
    pub client_ip: String,
    /// 开始时间
    pub start_time: chrono::DateTime<chrono::Utc>,
    /// 结束时间
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    /// 总处理时间（毫秒）
    pub total_time_ms: u64,
    /// 递归步骤
    pub steps: Vec<RecursiveStep>,
    /// 最终状态
    pub final_status: RecursiveStatus,
    /// 总发现域名数
    pub total_discovered_domains: usize,
    /// 缓存命中率
    pub cache_hit_rate: f64,
}

/// 递归状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecursiveStatus {
    /// 成功完成
    Success,
    /// 达到最大深度
    MaxDepthReached,
    /// 达到最大域名数
    MaxDomainsReached,
    /// 网络错误
    NetworkError(String),
    /// 解析错误
    ParseError(String),
    /// 被过滤器阻止
    FilteredOut(String),
    /// 用户取消
    Cancelled,
}

impl RecursiveContext {
    /// 创建新的递归上下文
    pub fn new(original_url: String, client_ip: String) -> Self {
        Self {
            current_depth: 0,
            original_url,
            upstream_chain: Vec::new(),
            start_time: Instant::now(),
            client_ip,
            domains: Vec::new(),
            mappings: HashMap::new(),
            headers: HashMap::new(),
            session_id: uuid::Uuid::new_v4().to_string(),
        }
    }

    /// 增加递归深度
    pub fn increment_depth(&mut self) {
        self.current_depth += 1;
    }

    /// 添加到上游链路
    pub fn add_to_chain(&mut self, url: String) {
        self.upstream_chain.push(url);
    }

    /// 添加发现的域名
    pub fn add_domain(&mut self, domain: String) {
        if !self.domains.contains(&domain) {
            self.domains.push(domain);
        }
    }

    /// 添加域名映射
    pub fn add_mapping(&mut self, original: String, mapped: String) {
        self.mappings.insert(original, mapped);
    }

    /// 获取经过时间
    pub fn elapsed_time(&self) -> std::time::Duration {
        self.start_time.elapsed()
    }

    /// 检查是否应该继续递归
    pub fn should_continue(&self, max_depth: u32, max_domains: u32) -> bool {
        self.current_depth < max_depth && self.domains.len() < max_domains as usize
    }

    /// 创建递归步骤
    pub fn create_step(&self, params: StepParams) -> RecursiveStep {
        RecursiveStep {
            step_number: self.current_depth,
            url: params.url,
            method: params.method,
            status_code: params.status_code,
            response_time_ms: params.response_time_ms,
            from_cache: params.from_cache,
            discovered_domains_count: params.discovered_count,
            error: params.error,
            redirect_url: params.redirect_url,
        }
    }
}

impl RecursiveResponse {
    /// 创建新的递归响应
    pub fn new(params: ResponseParams) -> Self {
        Self {
            status_code: params.status_code,
            headers: params.headers,
            body: params.body,
            content_type: params.content_type,
            from_cache: params.from_cache,
            chain: params.chain,
            domains: params.domains,
            processing_time_ms: params.processing_time_ms,
            final_url: params.final_url,
        }
    }

    /// 获取响应体为字符串
    pub fn body_as_string(&self) -> Result<String, std::string::FromUtf8Error> {
        String::from_utf8(self.body.clone())
    }

    /// 检查是否为成功响应
    pub fn is_success(&self) -> bool {
        self.status_code >= 200 && self.status_code < 300
    }

    /// 检查是否为重定向响应
    pub fn is_redirect(&self) -> bool {
        matches!(self.status_code, 301 | 302 | 307 | 308)
    }

    /// 获取重定向URL
    pub fn get_redirect_url(&self) -> Option<&String> {
        self.headers
            .get("location")
            .or_else(|| self.headers.get("Location"))
    }
}

impl RecursiveSession {
    /// 创建新的递归会话
    pub fn new(session_id: String, original_url: String, client_ip: String) -> Self {
        Self {
            session_id,
            original_url,
            client_ip,
            start_time: chrono::Utc::now(),
            end_time: None,
            total_time_ms: 0,
            steps: Vec::new(),
            final_status: RecursiveStatus::Success,
            total_discovered_domains: 0,
            cache_hit_rate: 0.0,
        }
    }

    /// 添加递归步骤
    pub fn add_step(&mut self, step: RecursiveStep) {
        self.steps.push(step);
    }

    /// 完成会话
    pub fn complete(&mut self, status: RecursiveStatus, total_discovered: usize) {
        self.end_time = Some(chrono::Utc::now());
        self.total_time_ms = self
            .end_time
            .unwrap()
            .signed_duration_since(self.start_time)
            .num_milliseconds() as u64;
        self.final_status = status;
        self.total_discovered_domains = total_discovered;

        // 计算缓存命中率
        let total_requests = self.steps.len();
        let cache_hits = self.steps.iter().filter(|s| s.from_cache).count();
        self.cache_hit_rate = if total_requests > 0 {
            cache_hits as f64 / total_requests as f64
        } else {
            0.0
        };
    }

    /// 获取会话持续时间
    pub fn duration_ms(&self) -> u64 {
        if let Some(end_time) = self.end_time {
            end_time
                .signed_duration_since(self.start_time)
                .num_milliseconds() as u64
        } else {
            chrono::Utc::now()
                .signed_duration_since(self.start_time)
                .num_milliseconds() as u64
        }
    }
}
