use crate::db::traits::DatabaseRepository;
use crate::error::{ProxyError, ProxyResult};
use crate::services::database::MongoDatabase;
use std::sync::Arc;

/// 数据库类型枚举
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum DatabaseType {
    MongoDB,
}

/// 数据库配置
#[derive(Debu<PERSON>, Clone)]
pub struct DatabaseConfig {
    pub db_type: DatabaseType,
    pub connection_string: String,
    pub max_connections: u32,
    pub connection_timeout: std::time::Duration,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            db_type: DatabaseType::MongoDB,
            connection_string: "mongodb://localhost:27017/sm_proxy".to_string(),
            max_connections: 10,
            connection_timeout: std::time::Duration::from_secs(30),
        }
    }
}

impl DatabaseConfig {
    /// 创建数据库仓库实例
    pub async fn create_repository(&self) -> ProxyResult<Arc<dyn DatabaseRepository>> {
        match self.db_type {
            DatabaseType::MongoDB => {
                // 从连接字符串中提取数据库名称
                let db_name = self.extract_db_name_from_uri(&self.connection_string)?;
                let repo = MongoDatabase::new(&self.connection_string, &db_name).await?;
                Ok(Arc::new(repo))
            }
        }
    }

    /// 从MongoDB URI中提取数据库名称
    fn extract_db_name_from_uri(&self, uri: &str) -> ProxyResult<String> {
        // 解析URI，提取数据库名称
        if let Some(db_start) = uri.rfind('/') {
            let db_part = &uri[db_start + 1..];
            if let Some(query_start) = db_part.find('?') {
                Ok(db_part[..query_start].to_string())
            } else {
                Ok(db_part.to_string())
            }
        } else {
            Err(ProxyError::DatabaseError(
                "无法从连接字符串中提取数据库名称".to_string(),
            ))
        }
    }

    /// 从连接字符串推断数据库类型
    pub fn infer_database_type(connection_string: &str) -> ProxyResult<DatabaseType> {
        if connection_string.starts_with("mongodb://")
            || connection_string.starts_with("mongodb+srv://")
        {
            Ok(DatabaseType::MongoDB)
        } else {
            Err(ProxyError::DatabaseError(
                "不支持的数据库连接字符串格式".to_string(),
            ))
        }
    }
}
