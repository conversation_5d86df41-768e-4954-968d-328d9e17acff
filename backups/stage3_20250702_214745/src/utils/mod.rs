//! 工具函数模块
//!
//! 包含日志、配置、辅助函数等工具功能

pub mod logger;

// 重新导出主要功能
pub use logger::setup_logging;

/// 验证域名格式
pub fn validate_domain(domain: &str) -> bool {
    // 简单的域名验证逻辑
    if domain.is_empty() || domain.len() > 253 {
        return false;
    }

    // 检查是否包含有效字符
    domain
        .chars()
        .all(|c| c.is_ascii_alphanumeric() || c == '.' || c == '-')
        && !domain.starts_with('-')
        && !domain.ends_with('-')
        && !domain.starts_with('.')
        && !domain.ends_with('.')
        && domain.contains('.')
}
