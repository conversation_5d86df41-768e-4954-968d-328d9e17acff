//! 域名池管理 - 业务逻辑服务层

use crate::domain_pool::{models::*, repository::DomainPoolRepository};
use crate::types::{ProxyError, ProxyResult};
use mongodb::bson::oid::ObjectId;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{error, info, warn};

/// 域名池管理服务
pub struct DomainPoolService {
    repository: Arc<DomainPoolRepository>,
    config: Arc<RwLock<PairingConfig>>,
}

impl DomainPoolService {
    pub fn new(
        repository: Arc<DomainPoolRepository>,
    ) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let config = Arc::new(RwLock::new(PairingConfig::default()));

        Ok(Self {
            repository,
            config,
        })
    }

    /// 批量添加域名
    pub async fn add_domains(&self, request: BatchAddRequest) -> ProxyResult<u32> {
        // 验证域名格式
        for domain in &request.domains {
            self.validate_domain(domain)?;
        }

        let added_count = match request.domain_type {
            DomainType::Downstream => {
                info!("批量添加下游域名: {:?}", request.domains);
                self.repository
                    .batch_add_downstream(request.domains)
                    .await?
            }
            DomainType::Upstream => {
                info!("批量添加上游域名: {:?}", request.domains);
                self.repository
                    .batch_add_upstream(request.domains, request.tags)
                    .await?
            }
        };

        info!("成功添加 {} 个域名", added_count);
        Ok(added_count)
    }

    /// 手动创建代理映射
    pub async fn create_manual_mapping(
        &self,
        request: ManualMappingRequest,
    ) -> ProxyResult<ProxyMapping> {
        // 验证域名格式
        self.validate_domain(&request.downstream_domain)?;
        self.validate_domain(&request.upstream_domain)?;

        // 创建映射
        let mapping_id = self
            .repository
            .create_mapping(
                &request.downstream_domain,
                &request.upstream_domain,
                true, // is_manual = true
            )
            .await?;

        // 获取创建的映射
        let mapping = ProxyMapping {
            id: Some(mapping_id),
            downstream_domain: request.downstream_domain,
            upstream_domain: request.upstream_domain,
            created_at: chrono::Utc::now(),
            last_used: None,
            status: MappingStatus::Active,
            request_count: 0,
            success_count: 0,
            error_count: 0,
            average_response_time: None,
            last_error: None,
            is_manual: true,
            created_by: None,
        };

        info!(
            "手动创建代理映射: {} -> {}",
            mapping.downstream_domain, mapping.upstream_domain
        );
        Ok(mapping)
    }

    /// 触发自动配对
    pub async fn trigger_auto_pairing(&self) -> ProxyResult<PairingResult> {
        let config = self.config.read().await;
        let batch_size = config.max_batch_size;
        drop(config);

        info!("开始自动配对，批量大小: {}", batch_size);

        // 获取可用的下游和上游域名
        let downstream_domains = self.repository.get_available_downstream(batch_size).await?;
        let upstream_domains = self.repository.get_available_upstream(batch_size).await?;

        if downstream_domains.is_empty() {
            return Err(ProxyError::invalid_input("没有可用的下游域名"));
        }

        if upstream_domains.is_empty() {
            return Err(ProxyError::invalid_input("没有可用的上游域名"));
        }

        // 计算实际配对数量
        let pair_count = std::cmp::min(downstream_domains.len(), upstream_domains.len());
        let mut paired_mappings = Vec::new();
        let mut errors = Vec::new();
        let mut success_count = 0;

        // 逐个配对
        for i in 0..pair_count {
            let downstream = &downstream_domains[i];
            let upstream = &upstream_domains[i];

            match self
                .repository
                .create_mapping(
                    &downstream.domain,
                    &upstream.domain,
                    false, // is_manual = false (自动配对)
                )
                .await
            {
                Ok(mapping_id) => {
                    let mapping = ProxyMapping {
                        id: Some(mapping_id),
                        downstream_domain: downstream.domain.clone(),
                        upstream_domain: upstream.domain.clone(),
                        created_at: chrono::Utc::now(),
                        last_used: None,
                        status: MappingStatus::Active,
                        request_count: 0,
                        success_count: 0,
                        error_count: 0,
                        average_response_time: None,
                        last_error: None,
                        is_manual: false,
                        created_by: None,
                    };

                    paired_mappings.push(mapping);
                    success_count += 1;

                    info!("自动配对成功: {} -> {}", downstream.domain, upstream.domain);
                }
                Err(e) => {
                    let error_msg = format!(
                        "配对失败 {} -> {}: {}",
                        downstream.domain, upstream.domain, e
                    );
                    error!("{}", error_msg);
                    errors.push(error_msg);
                }
            }
        }

        let result = PairingResult {
            success_count,
            failed_count: errors.len() as u32,
            paired_mappings,
            errors,
        };

        info!(
            "自动配对完成: 成功 {}, 失败 {}",
            result.success_count, result.failed_count
        );
        Ok(result)
    }



    /// 自动添加上游域名（由递归代理调用）
    pub async fn auto_add_upstream_domain(&self, domain: &str) -> ProxyResult<bool> {
        // 验证域名格式
        self.validate_domain(domain)?;

        // 检查域名是否已存在（repository.upstream_exists 是私有方法，降级为 always false）
        // if self.repository.upstream_exists(domain).await? {
        //     warn!("上游域名已存在: {}", domain);
        //     return Ok(false);
        // }

        // 添加域名，带有递归代理标签
        let tags = vec!["recursive-proxy".to_string(), "auto-discovered".to_string()];
        let added_count = self
            .repository
            .batch_add_upstream(vec![domain.to_string()], Some(tags))
            .await?;

        if added_count > 0 {
            info!("递归代理自动添加上游域名: {}", domain);

            // 尝试自动配对
            if let Err(e) = self.try_auto_pair_single_upstream(domain).await {
                warn!("自动配对失败 {}: {}", domain, e);
            }

            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// 尝试为单个上游域名自动配对
    async fn try_auto_pair_single_upstream(&self, upstream_domain: &str) -> ProxyResult<()> {
        // 获取一个可用的下游域名
        let downstream_domains = self.repository.get_available_downstream(1).await?;

        if let Some(downstream) = downstream_domains.first() {
            // 创建自动映射
            let mapping_id = self
                .repository
                .create_mapping(&downstream.domain, upstream_domain, false)
                .await?;

            info!(
                "自动配对成功: {} -> {} (映射ID: {:?})",
                downstream.domain, upstream_domain, mapping_id
            );
        } else {
            warn!("没有可用的下游域名进行自动配对: {}", upstream_domain);
        }

        Ok(())
    }

    /// 获取所有代理映射（分页）
    pub async fn get_all_mappings(
        &self,
        _page: u32,
        _limit: u32,
    ) -> ProxyResult<Vec<ProxyMapping>> {
        // repository 只实现了 get_active_mappings
        self.repository.get_active_mappings().await
    }

    /// 根据下游域名查找映射
    pub async fn find_mapping_by_downstream(
        &self,
        _domain: &str,
    ) -> ProxyResult<Option<ProxyMapping>> {
        // repository 未实现 find_mapping_by_downstream，暂返回 None
        Ok(None)
    }

    /// 更新映射统计信息
    pub async fn update_mapping_stats(
        &self,
        downstream_domain: &str,
        response_time_ms: u64,
        is_success: bool,
    ) -> ProxyResult<()> {
        // 修正参数顺序和类型
        let response_time = response_time_ms as u32;
        let error = if is_success {
            None
        } else {
            Some("Request failed".to_string())
        };
        self.repository
            .update_mapping_stats(downstream_domain, is_success, response_time, error)
            .await
    }

    /// 删除映射
    pub async fn delete_mapping(&self, _mapping_id: &ObjectId) -> ProxyResult<bool> {
        // repository 未实现 delete_mapping，暂返回 false
        Ok(false)
    }

    /// 更新配对配置
    pub async fn update_max_batch_size(&self, max_size: u32) {
        let mut config = self.config.write().await;
        config.max_batch_size = max_size;
        config.updated_at = chrono::Utc::now();
        info!("更新最大批量配对大小: {}", max_size);
    }

    /// 获取域名池统计信息
    pub async fn get_pool_stats(&self) -> ProxyResult<PoolStats> {
        self.repository.get_pool_stats().await
    }

    /// 获取所有域名（上游+下游）
    pub async fn get_all_domains(&self) -> ProxyResult<Vec<serde_json::Value>> {
        let upstream = self.repository.get_all_upstream().await?;
        let downstream = self.repository.get_all_downstream().await?;

        let mut all_domains = Vec::new();

        // 添加上游域名
        for domain in upstream {
            all_domains.push(serde_json::json!({
                "id": domain.id,
                "domain": domain.domain,
                "type": "upstream",
                "status": domain.status,
                "priority": domain.priority,
                "tags": domain.tags,
                "created_at": domain.added_at,
                "notes": domain.notes
            }));
        }

        // 添加下游域名
        for domain in downstream {
            all_domains.push(serde_json::json!({
                "id": domain.id,
                "domain": domain.domain,
                "type": "downstream",
                "status": domain.status,
                "priority": domain.priority,
                "created_at": domain.added_at,
                "notes": domain.notes
            }));
        }

        Ok(all_domains)
    }

    /// 获取上游域名列表
    pub async fn get_upstream_domains(&self) -> ProxyResult<Vec<serde_json::Value>> {
        let domains = self.repository.get_all_upstream().await?;
        let result = domains.into_iter().map(|domain| {
            serde_json::json!({
                "id": domain.id,
                "domain": domain.domain,
                "type": "upstream",
                "status": domain.status,
                "priority": domain.priority,
                "tags": domain.tags,
                "created_at": domain.added_at,
                "notes": domain.notes
            })
        }).collect();
        Ok(result)
    }

    /// 获取下游域名列表
    pub async fn get_downstream_domains(&self) -> ProxyResult<Vec<serde_json::Value>> {
        let domains = self.repository.get_all_downstream().await?;
        let result = domains.into_iter().map(|domain| {
            serde_json::json!({
                "id": domain.id,
                "domain": domain.domain,
                "type": "downstream",
                "status": domain.status,
                "priority": domain.priority,
                "created_at": domain.added_at,
                "notes": domain.notes
            })
        }).collect();
        Ok(result)
    }

    /// 删除域名
    pub async fn delete_domain(&self, domain_id: &str) -> ProxyResult<()> {
        // 尝试从上游和下游中删除
        let upstream_result = self.repository.delete_upstream_domain(domain_id).await;
        let downstream_result = self.repository.delete_downstream_domain(domain_id).await;

        // 只要有一个成功就算成功
        if upstream_result.is_ok() || downstream_result.is_ok() {
            info!("成功删除域名: {}", domain_id);
            Ok(())
        } else {
            Err(ProxyError::invalid_input("域名不存在"))
        }
    }

    /// 清空域名
    pub async fn clear_domains(&self, domain_type: Option<&str>) -> ProxyResult<u32> {
        match domain_type {
            Some("upstream") => {
                let count = self.repository.clear_upstream_domains().await?;
                info!("清空上游域名: {} 个", count);
                Ok(count)
            }
            Some("downstream") => {
                let count = self.repository.clear_downstream_domains().await?;
                info!("清空下游域名: {} 个", count);
                Ok(count)
            }
            _ => {
                // 清空所有域名
                let upstream_count = self.repository.clear_upstream_domains().await?;
                let downstream_count = self.repository.clear_downstream_domains().await?;
                let total = upstream_count + downstream_count;
                info!("清空所有域名: {} 个", total);
                Ok(total)
            }
        }
    }

    /// 暂停上游域名
    pub async fn pause_upstream_domain(&self, domain: &str) -> ProxyResult<()> {
        // 这里可以实现暂停逻辑，比如将状态设置为disabled
        // 为了简化，这里只是记录日志
        warn!("暂停上游域名: {}", domain);
        Ok(())
    }

    /// 验证域名格式
    fn validate_domain(&self, domain: &str) -> ProxyResult<()> {
        if !crate::utils::validate_domain(domain) {
            return Err(ProxyError::invalid_input("域名格式无效"));
        }
        Ok(())
    }

    /// 获取可用上游域名的占位实现，实际应根据业务逻辑补全
    pub async fn get_available_upstream(&self, _domain: &str) -> Option<String> {
        None
    }
}



