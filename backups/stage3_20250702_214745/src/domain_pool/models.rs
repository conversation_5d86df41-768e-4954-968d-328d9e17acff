//! 域名池管理 - MongoDB数据模型

use chrono::{DateTime, Utc};
use mongodb::bson::oid::ObjectId;
use serde::{Deserialize, Serialize};

/// 下游域名池 (MongoDB Collection: downstream_pool)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownstreamPool {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub domain: String,
    pub added_at: DateTime<Utc>,
    pub status: PoolStatus,
    pub priority: i32, // FIFO排序
    pub created_by: Option<String>,
    pub notes: Option<String>,
}

/// 上游域名池 (MongoDB Collection: upstream_pool)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpstreamPool {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub domain: String,
    pub added_at: DateTime<Utc>,
    pub status: PoolStatus,
    pub priority: i32, // FIFO排序
    pub tags: Vec<String>,          // 标签数组
    pub created_by: Option<String>,
    pub notes: Option<String>,
}

/// 代理映射关系 (MongoDB Collection: proxy_mappings)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyMapping {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub downstream_domain: String,
    pub upstream_domain: String,
    pub created_at: DateTime<Utc>,
    pub last_used: Option<DateTime<Utc>>,
    pub status: MappingStatus,
    pub request_count: i64,
    pub success_count: i64,
    pub error_count: i64,
    pub average_response_time: Option<i32>,
    pub last_error: Option<String>,
    pub is_manual: bool, // 区分手动创建和自动配对
    pub created_by: Option<String>,
}

/// 递归链路记录 (MongoDB Collection: recursive_chains)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveChain {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub mapping_id: ObjectId,
    pub original_upstream: String,
    pub extracted_upstream: String,
    pub extraction_rule: String,
    pub extracted_at: DateTime<Utc>,
    pub chain_depth: i32,
    pub is_active: bool,
    pub response_data: Option<String>, // 响应内容摘要
}



/// 配对配置 (MongoDB Collection: pairing_configs)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PairingConfig {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub max_batch_size: u32,
    pub auto_pairing_enabled: bool,
    pub health_check_enabled: bool,
    pub health_check_interval: u32, // 秒
    pub max_retry_attempts: u32,
    pub timeout_seconds: u32,
    pub updated_at: DateTime<Utc>,
    pub updated_by: Option<String>,
}

/// 域名池状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum PoolStatus {
    Available, // 可用
    Used,      // 已使用
    Reserved,  // 预留
    Disabled,  // 禁用
}



/// 映射状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum MappingStatus {
    Active,   // 活跃
    Inactive, // 非活跃
    Failed,   // 失败
    Paused,   // 暂停
}

/// API请求结构体
/// 批量添加请求
#[derive(Debug, Deserialize)]
pub struct BatchAddRequest {
    pub domains: Vec<String>,
    pub domain_type: DomainType,
    pub tags: Option<Vec<String>>, // 仅上游域名使用
}

/// 域名类型
#[derive(Debug, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum DomainType {
    Downstream,
    Upstream,
}

/// 手动创建映射请求
#[derive(Debug, Deserialize)]
pub struct ManualMappingRequest {
    pub downstream_domain: String,
    pub upstream_domain: String,
}

/// 触发配对请求
#[derive(Debug, Deserialize)]
pub struct TriggerPairingRequest {
    pub max_batch_size: Option<u32>,
}

/// 域名池统计信息
#[derive(Debug, Serialize)]
pub struct PoolStats {
    pub downstream_available: i64,
    pub downstream_used: i64,
    pub upstream_available: i64,
    pub upstream_used: i64,
    pub active_mappings: i64,
    pub failed_mappings: i64,
    pub total_requests: i64,
    pub success_rate: f64,
}



/// 配对结果
#[derive(Debug, Serialize)]
pub struct PairingResult {
    pub success_count: u32,
    pub failed_count: u32,
    pub paired_mappings: Vec<ProxyMapping>,
    pub errors: Vec<String>,
}

// 实现方法

impl DownstreamPool {
    pub fn new(domain: String, priority: i32) -> Self {
        Self {
            id: None,
            domain,
            added_at: Utc::now(),
            status: PoolStatus::Available,
            priority,
            created_by: None,
            notes: None,
        }
    }
}

impl UpstreamPool {
    pub fn new(domain: String, priority: i32) -> Self {
        Self {
            id: None,
            domain,
            added_at: Utc::now(),
            status: PoolStatus::Available,
            priority,
            tags: Vec::new(),
            created_by: None,
            notes: None,
        }
    }

    pub fn with_tags(mut self, tags: Vec<String>) -> Self {
        self.tags = tags;
        self
    }
}

impl ProxyMapping {
    pub fn new(downstream: String, upstream: String, is_manual: bool) -> Self {
        Self {
            id: None,
            downstream_domain: downstream,
            upstream_domain: upstream,
            created_at: Utc::now(),
            last_used: None,
            status: MappingStatus::Active,
            request_count: 0,
            success_count: 0,
            error_count: 0,
            average_response_time: None,
            last_error: None,
            is_manual,
            created_by: None,
        }
    }

    pub fn update_stats(&mut self, success: bool, response_time: u32, error: Option<String>) {
        self.request_count += 1;
        self.last_used = Some(Utc::now());

        if success {
            self.success_count += 1;
        } else {
            self.error_count += 1;
            self.last_error = error;
        }

        // 更新平均响应时间
        if let Some(avg) = self.average_response_time {
            self.average_response_time = Some((avg + response_time as i32) / 2);
        } else {
            self.average_response_time = Some(response_time as i32);
        }
    }

    pub fn success_rate(&self) -> f64 {
        if self.request_count == 0 {
            0.0
        } else {
            self.success_count as f64 / self.request_count as f64
        }
    }
}

impl Default for PairingConfig {
    fn default() -> Self {
        Self {
            id: None,
            max_batch_size: 100,
            auto_pairing_enabled: true,
            health_check_enabled: true,
            health_check_interval: 300, // 5分钟
            max_retry_attempts: 3,
            timeout_seconds: 30,
            updated_at: Utc::now(),
            updated_by: None,
        }
    }
}
