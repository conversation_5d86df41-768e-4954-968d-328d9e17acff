//! 域名池管理 - API接口层

use crate::domain_pool::{models::*, service::DomainPoolService};
// use anyhow::Error as ProxyError;
use crate::types::ApiResponse;
use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::Json,
    routing::{delete, get, post, put},
    Router,
};
use serde_json::{json, Value};
use std::sync::Arc;
use tracing::info;

/// 创建域名池API路由（包含镜像功能）
pub fn create_domain_pool_routes() -> Router<Arc<DomainPoolService>> {
    Router::new()
        // 域名池基础功能
        .route("/batch-add", post(add_domains))
        .route("/manual-mapping", post(create_manual_mapping))
        .route("/trigger-pairing", post(trigger_auto_pairing))

        .route("/stats", get(get_pool_stats))
        .route("/mappings", get(get_mappings))
        .route("/config/batch-size", post(update_batch_size))
        .route("/domains", get(get_domains))
        .route("/domains/upstream", get(get_upstream))
        .route("/domains/downstream", get(get_downstream))
        .route("/domains/:id", delete(delete_domain))
        .route("/clear", delete(clear_domains))
        // 镜像系统功能
        .route(
            "/mirror/tasks",
            get(list_mirror_tasks).post(start_mirror_task),
        )
        .route(
            "/mirror/tasks/:id",
            get(get_mirror_task).delete(stop_mirror_task),
        )
        .route(
            "/mirror/blacklist",
            get(get_blacklist).post(add_to_blacklist),
        )
        .route("/mirror/blacklist/:type", delete(remove_from_blacklist))
        .route(
            "/mirror/replacement-rules",
            get(get_replacement_rules).post(add_replacement_rule),
        )
        .route(
            "/mirror/replacement-rules/:id",
            put(update_replacement_rule).delete(delete_replacement_rule),
        )
        .route(
            "/mirror/proxy-config",
            get(get_proxy_config).post(update_proxy_config),
        )
}

/// 批量添加域名
async fn add_domains(
    State(service): State<Arc<DomainPoolService>>,
    Json(request): Json<BatchAddRequest>,
) -> axum::Json<ApiResponse<serde_json::Value>> {
    info!("收到批量添加域名请求: {:?}", request.domain_type);

    match service.add_domains(request).await {
        Ok(count) => axum::Json(ApiResponse::success(serde_json::json!({
            "message": format!("成功添加 {} 个域名", count),
            "added_count": count
        }))),

        Err(e) => axum::Json(ApiResponse::error(e.to_string())),
    }
}

/// 手动创建代理映射
async fn create_manual_mapping(
    State(service): State<Arc<DomainPoolService>>,
    Json(request): Json<ManualMappingRequest>,
) -> axum::Json<ApiResponse<serde_json::Value>> {
    info!(
        "收到手动创建映射请求: {} -> {}",
        request.downstream_domain, request.upstream_domain
    );

    match service.create_manual_mapping(request).await {
        Ok(mapping) => axum::Json(ApiResponse::success(serde_json::json!({
            "message": "手动映射创建成功",
            "mapping": {
                "downstream_domain": mapping.downstream_domain,
                "upstream_domain": mapping.upstream_domain,
                "created_at": mapping.created_at,
                "is_manual": mapping.is_manual
            }
        }))),

        Err(e) => axum::Json(ApiResponse::error(e.to_string())),
    }
}

/// 触发自动配对
async fn trigger_auto_pairing(
    State(service): State<Arc<DomainPoolService>>,
    Json(request): Json<TriggerPairingRequest>,
) -> axum::Json<ApiResponse<serde_json::Value>> {
    info!("收到自动配对请求");

    // 如果请求指定了批量大小，先更新配置
    if let Some(batch_size) = request.max_batch_size {
        service.update_max_batch_size(batch_size).await;
    }

    match service.trigger_auto_pairing().await {
        Ok(result) => axum::Json(ApiResponse::success(serde_json::json!({
            "message": format!("配对完成: 成功 {}, 失败 {}", result.success_count, result.failed_count),
            "result": {
                "success_count": result.success_count,
                "failed_count": result.failed_count,
                "paired_mappings": result.paired_mappings,
                "errors": result.errors
            }
        }))),

        Err(e) => axum::Json(ApiResponse::error(e.to_string())),
    }
}



/// 获取域名池统计信息
async fn get_pool_stats(
    State(service): State<Arc<DomainPoolService>>,
) -> axum::Json<ApiResponse<serde_json::Value>> {
    match service.get_pool_stats().await {
        Ok(stats) => axum::Json(ApiResponse::success(serde_json::json!({
            "stats": {
                "downstream": {
                    "available": stats.downstream_available,
                    "used": stats.downstream_used
                },
                "upstream": {
                    "available": stats.upstream_available,
                    "used": stats.upstream_used
                },
                "mappings": {
                    "active": stats.active_mappings,
                    "failed": stats.failed_mappings
                },
                "performance": {
                    "total_requests": stats.total_requests,
                    "success_rate": stats.success_rate
                }
            }
        }))),

        Err(e) => axum::Json(ApiResponse::error(e.to_string())),
    }
}

/// 获取活跃代理映射
async fn get_mappings(
    State(service): State<Arc<DomainPoolService>>,
) -> axum::Json<ApiResponse<serde_json::Value>> {
    // service 只实现 get_all_mappings(page, limit)
    match service.get_all_mappings(1, 1000).await {
        Ok(mappings) => {
            let mappings_json: Vec<Value> = mappings
                .into_iter()
                .map(|m| {
                    json!({
                        "downstream_domain": m.downstream_domain,
                        "upstream_domain": m.upstream_domain,
                        "created_at": m.created_at,
                        "last_used": m.last_used,
                        "status": m.status,
                        "request_count": m.request_count,
                        "success_count": m.success_count,
                        "error_count": m.error_count,
                        "success_rate": m.success_rate(),
                        "average_response_time": m.average_response_time,
                        "is_manual": m.is_manual,
                        "last_error": m.last_error
                    })
                })
                .collect();
            axum::Json(ApiResponse::success(serde_json::json!({
                "mappings": mappings_json,
                "total_count": mappings_json.len()
            })))
        }
        Err(e) => axum::Json(ApiResponse::error(e.to_string())),
    }
}

/// 更新批量配对大小
async fn update_batch_size(
    State(service): State<Arc<DomainPoolService>>,
    Json(request): Json<Value>,
) -> axum::Json<ApiResponse<serde_json::Value>> {
    let batch_size = if let Some(size) = request["max_batch_size"].as_u64() {
        size as u32
    } else {
        return axum::Json(ApiResponse::error("缺少 max_batch_size 参数".to_string()));
    };
    service.update_max_batch_size(batch_size).await;
    axum::Json(ApiResponse::success(serde_json::json!({
        "message": format!("批量配对大小已更新为: {}", batch_size),
        "max_batch_size": batch_size
    })))
}

/// 获取所有域名（上游+下游）
async fn get_domains(
    State(service): State<Arc<DomainPoolService>>,
) -> axum::Json<ApiResponse<serde_json::Value>> {
    match service.get_all_domains().await {
        Ok(domains) => axum::Json(ApiResponse::success(serde_json::json!({
            "domains": domains,
            "total": domains.len()
        }))),
        Err(e) => axum::Json(ApiResponse::error(e.to_string())),
    }
}

/// 获取上游域名列表
async fn get_upstream(
    State(service): State<Arc<DomainPoolService>>,
) -> axum::Json<ApiResponse<serde_json::Value>> {
    match service.get_upstream_domains().await {
        Ok(domains) => axum::Json(ApiResponse::success(serde_json::json!({
            "domains": domains,
            "total": domains.len()
        }))),
        Err(e) => axum::Json(ApiResponse::error(e.to_string())),
    }
}

/// 获取下游域名列表
async fn get_downstream(
    State(service): State<Arc<DomainPoolService>>,
) -> axum::Json<ApiResponse<serde_json::Value>> {
    match service.get_downstream_domains().await {
        Ok(domains) => axum::Json(ApiResponse::success(serde_json::json!({
            "domains": domains,
            "total": domains.len()
        }))),
        Err(e) => axum::Json(ApiResponse::error(e.to_string())),
    }
}

// ==================== 镜像系统API ====================

/// 获取镜像任务列表
async fn list_mirror_tasks(
    State(_service): State<Arc<DomainPoolService>>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现镜像任务列表获取
    let response = json!({
        "success": true,
        "tasks": [],
        "total": 0
    });
    Ok(Json(response))
}

/// 启动镜像任务
async fn start_mirror_task(
    State(_service): State<Arc<DomainPoolService>>,
    Json(request): Json<Value>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现镜像任务启动
    let response = json!({
        "success": true,
        "message": "镜像任务启动成功",
        "task_id": "task_123"
    });
    Ok(Json(response))
}

/// 获取镜像任务详情
async fn get_mirror_task(
    State(_service): State<Arc<DomainPoolService>>,
    Path(task_id): Path<String>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现镜像任务详情获取
    let response = json!({
        "success": true,
        "task": {
            "id": task_id,
            "status": "running",
            "created_at": "2024-01-01T00:00:00Z"
        }
    });
    Ok(Json(response))
}

/// 停止镜像任务
async fn stop_mirror_task(
    State(_service): State<Arc<DomainPoolService>>,
    Path(task_id): Path<String>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现镜像任务停止
    let response = json!({
        "success": true,
        "message": format!("镜像任务 {} 已停止", task_id)
    });
    Ok(Json(response))
}

/// 获取黑名单
async fn get_blacklist(
    State(_service): State<Arc<DomainPoolService>>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现黑名单获取
    let response = json!({
        "success": true,
        "blacklist": {
            "keywords": [],
            "domains": [],
            "regex_patterns": []
        }
    });
    Ok(Json(response))
}

/// 添加到黑名单
async fn add_to_blacklist(
    State(_service): State<Arc<DomainPoolService>>,
    Json(request): Json<Value>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现黑名单添加
    let response = json!({
        "success": true,
        "message": "已添加到黑名单"
    });
    Ok(Json(response))
}

/// 从黑名单移除
async fn remove_from_blacklist(
    State(_service): State<Arc<DomainPoolService>>,
    Path(blacklist_type): Path<String>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现黑名单移除
    let response = json!({
        "success": true,
        "message": format!("已从{}黑名单移除", blacklist_type)
    });
    Ok(Json(response))
}

/// 获取内容替换规则
async fn get_replacement_rules(
    State(_service): State<Arc<DomainPoolService>>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现替换规则获取
    let response = json!({
        "success": true,
        "rules": []
    });
    Ok(Json(response))
}

/// 添加内容替换规则
async fn add_replacement_rule(
    State(_service): State<Arc<DomainPoolService>>,
    Json(request): Json<Value>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现替换规则添加
    let response = json!({
        "success": true,
        "message": "替换规则添加成功",
        "rule_id": "rule_123"
    });
    Ok(Json(response))
}

/// 更新内容替换规则
async fn update_replacement_rule(
    State(_service): State<Arc<DomainPoolService>>,
    Path(rule_id): Path<String>,
    Json(request): Json<Value>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现替换规则更新
    let response = json!({
        "success": true,
        "message": format!("替换规则 {} 更新成功", rule_id)
    });
    Ok(Json(response))
}

/// 删除内容替换规则
async fn delete_replacement_rule(
    State(_service): State<Arc<DomainPoolService>>,
    Path(rule_id): Path<String>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现替换规则删除
    let response = json!({
        "success": true,
        "message": format!("替换规则 {} 删除成功", rule_id)
    });
    Ok(Json(response))
}

/// 获取代理配置
async fn get_proxy_config(
    State(_service): State<Arc<DomainPoolService>>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现代理配置获取
    let response = json!({
        "success": true,
        "config": {
            "proxy_servers": [],
            "rotation_strategy": "sequential"
        }
    });
    Ok(Json(response))
}

/// 更新代理配置
async fn update_proxy_config(
    State(_service): State<Arc<DomainPoolService>>,
    Json(request): Json<Value>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // TODO: 实现代理配置更新
    let response = json!({
        "success": true,
        "message": "代理配置更新成功"
    });
    Ok(Json(response))
}

/// 删除单个域名
async fn delete_domain(
    State(service): State<Arc<DomainPoolService>>,
    Path(domain_id): Path<String>,
) -> axum::Json<ApiResponse<serde_json::Value>> {
    match service.delete_domain(&domain_id).await {
        Ok(_) => axum::Json(ApiResponse::success(serde_json::json!({
            "message": "域名删除成功"
        }))),
        Err(e) => axum::Json(ApiResponse::error(e.to_string())),
    }
}

/// 清空域名
async fn clear_domains(
    State(service): State<Arc<DomainPoolService>>,
    Json(request): Json<serde_json::Value>,
) -> axum::Json<ApiResponse<serde_json::Value>> {
    let domain_type = request.get("type").and_then(|t| t.as_str());

    match service.clear_domains(domain_type).await {
        Ok(count) => axum::Json(ApiResponse::success(serde_json::json!({
            "message": format!("成功清空 {} 个域名", count),
            "cleared_count": count
        }))),
        Err(e) => axum::Json(ApiResponse::error(e.to_string())),
    }
}
