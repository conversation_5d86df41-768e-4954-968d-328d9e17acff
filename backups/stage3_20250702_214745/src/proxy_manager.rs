//! 增强代理管理器 - 集成Pingora和域名池管理
//!
//! 提供动态配置更新、域名路由、负载均衡等功能
//! 支持可选的SSL管理、内容替换、缓存等高级功能

use crate::domains::DomainPoolService;
use anyhow::{anyhow, Result};
use proxy_config::unified::{ProxyConfig, ServerEntry, UpstreamConfig};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

// 可选功能模块导入
use crate::domains::{ContentReplacer, DomainExtractor};
// 注释掉暂时不可用的模块
// use crate::ssl::{SslManager, SslManagerConfig};
// use crate::domain_mapping::extractor::DomainExtractor;
// use crate::domain_mapping::replacer::ContentReplacer;
// use crate::recursive_proxy::cache::IntelligentCache;
// use crate::recursive_proxy::config::CacheConfig;

/// 增强代理配置（简化版本）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnhancedProxyConfig {
    /// 基础代理配置
    pub proxy_config: ProxyConfig,

    /// 功能开关
    pub enable_ssl: bool,
    pub enable_content_replacement: bool,
    pub enable_caching: bool,
    pub enable_recursive_discovery: bool,

    /// 递归代理配置
    pub recursive_config: RecursiveProxyConfig,
}

/// 递归代理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveProxyConfig {
    /// 是否启用递归代理
    pub enabled: bool,
    /// 递归深度
    pub max_depth: u32,
    /// 检查频率（分钟）
    pub check_interval_minutes: u64,
    /// 并发数量
    pub concurrency: u32,
    /// 超时时间（秒）
    pub timeout_seconds: u64,
    /// 内容大小阈值（KB）
    pub content_threshold_kb: u32,
}

impl Default for EnhancedProxyConfig {
    fn default() -> Self {
        Self {
            proxy_config: ProxyConfig::default(),
            enable_ssl: false,
            enable_content_replacement: false,
            enable_caching: false,
            enable_recursive_discovery: true,
            recursive_config: RecursiveProxyConfig {
                enabled: true,
                max_depth: 3,
                check_interval_minutes: 5,
                concurrency: 10,
                timeout_seconds: 30,
                content_threshold_kb: 300,
            },
        }
    }
}

/// 增强代理管理器 - 连接Pingora和域名池，支持可选高级功能
pub struct ProxyManager {
    // 核心组件
    domain_pool: Arc<DomainPoolService>,
    current_config: Arc<RwLock<ProxyConfig>>,
    domain_mappings: Arc<RwLock<HashMap<String, String>>>, // downstream -> upstream

    // 配置和状态
    enhanced_config: EnhancedProxyConfig,
}

impl ProxyManager {
    /// 创建基础代理管理器（向后兼容）
    pub fn new(domain_pool: Arc<DomainPoolService>) -> Self {
        Self {
            domain_pool,
            current_config: Arc::new(RwLock::new(ProxyConfig::default())),
            domain_mappings: Arc::new(RwLock::new(HashMap::new())),
            enhanced_config: EnhancedProxyConfig::default(),
        }
    }

    /// 创建增强代理管理器（支持所有功能）
    pub async fn new_enhanced(
        domain_pool: Arc<DomainPoolService>,
        config: EnhancedProxyConfig,
    ) -> Result<Self> {
        let manager = Self {
            domain_pool,
            current_config: Arc::new(RwLock::new(config.proxy_config.clone())),
            domain_mappings: Arc::new(RwLock::new(HashMap::new())),
            enhanced_config: config.clone(),
        };

        info!("增强代理管理器初始化完成");
        info!(
            "功能状态: SSL={}, 内容替换={}, 缓存={}, 递归发现={}",
            config.enable_ssl,
            config.enable_content_replacement,
            config.enable_caching,
            config.enable_recursive_discovery
        );

        Ok(manager)
    }

    /// 初始化 - 从数据库加载配置
    pub async fn initialize(&self) -> Result<()> {
        tracing::info!("初始化代理管理器...");

        // 加载域名映射
        self.reload_domain_mappings().await?;

        // 生成Pingora配置
        self.update_pingora_config().await?;

        tracing::info!("代理管理器初始化完成");
        Ok(())
    }

    /// 重新加载域名映射（性能优化版）
    pub async fn reload_domain_mappings(&self) -> Result<()> {
        // 性能优化：使用更大的批次大小，减少数据库查询次数
        let mappings = self
            .domain_pool
            .get_all_mappings(1, 5000) // 增加批次大小
            .await
            .map_err(|e| anyhow::anyhow!("获取域名映射失败: {:?}", e))?;

        // 性能优化：预分配HashMap容量
        let mut new_mappings = HashMap::with_capacity(mappings.len());

        for mapping in mappings {
            new_mappings.insert(mapping.downstream_domain, mapping.upstream_domain);
        }

        // 性能优化：一次性替换整个映射，减少锁持有时间
        {
            let mut domain_mappings = self.domain_mappings.write().await;
            *domain_mappings = new_mappings;
        }

        tracing::info!(
            "已加载 {} 个域名映射",
            self.domain_mappings.read().await.len()
        );
        Ok(())
    }

    /// 更新Pingora配置
    pub async fn update_pingora_config(&self) -> Result<()> {
        let domain_mappings = self.domain_mappings.read().await;

        // 收集所有唯一的上游域名
        let mut upstream_domains: Vec<String> = domain_mappings.values().cloned().collect();
        upstream_domains.sort();
        upstream_domains.dedup();

        // 创建上游配置
        let mut upstreams = Vec::new();
        for (index, upstream_domain) in upstream_domains.iter().enumerate() {
            let upstream_config = UpstreamConfig {
                name: format!("upstream_{}", index),
                servers: vec![ServerEntry {
                    addr: format!("{}:80", upstream_domain), // 默认使用80端口
                                                             // 移除权重字段
                }],
            };
            upstreams.push(upstream_config);
        }

        // 更新配置
        let mut config = self.current_config.write().await;
        config.upstreams = upstreams;

        tracing::info!(
            "已更新Pingora配置，包含 {} 个上游服务器",
            config.upstreams.len()
        );
        Ok(())
    }

    /// 根据下游域名查找上游域名
    pub async fn find_upstream_for_domain(&self, downstream_domain: &str) -> Option<String> {
        let domain_mappings = self.domain_mappings.read().await;
        domain_mappings.get(downstream_domain).cloned()
    }

    /// 添加新的域名映射
    pub async fn add_domain_mapping(&self, downstream: String, upstream: String) -> Result<()> {
        // 添加到域名池
        let request = crate::domains::ManualMappingRequest {
            downstream_domain: downstream.clone(),
            upstream_domain: upstream.clone(),
        };

        self.domain_pool
            .create_manual_mapping(request)
            .await
            .map_err(|e| anyhow::anyhow!("创建域名映射失败: {:?}", e))?;

        // 更新本地缓存
        {
            let mut domain_mappings = self.domain_mappings.write().await;
            domain_mappings.insert(downstream.clone(), upstream.clone());
        }

        // 更新Pingora配置
        self.update_pingora_config().await?;

        tracing::info!("已添加域名映射: {} -> {}", downstream, upstream);
        Ok(())
    }

    /// 删除域名映射
    pub async fn remove_domain_mapping(&self, downstream: &str) -> Result<()> {
        // 从本地缓存移除
        {
            let mut domain_mappings = self.domain_mappings.write().await;
            domain_mappings.remove(downstream);
        }

        // 更新Pingora配置
        self.update_pingora_config().await?;

        tracing::info!("已删除域名映射: {}", downstream);
        Ok(())
    }

    /// 获取当前配置
    pub async fn get_current_config(&self) -> ProxyConfig {
        let config = self.current_config.read().await;
        config.clone()
    }

    /// 获取域名映射统计
    pub async fn get_mapping_stats(&self) -> HashMap<String, MappingStats> {
        let domain_mappings = self.domain_mappings.read().await;
        let mut stats = HashMap::new();

        for (downstream, upstream) in domain_mappings.iter() {
            // 这里可以从数据库获取真实的统计数据
            let mapping_stats = MappingStats {
                downstream_domain: downstream.clone(),
                upstream_domain: upstream.clone(),
                request_count: 0,
                success_count: 0,
                error_count: 0,
                avg_response_time: 0,
                last_used: None,
            };
            stats.insert(downstream.clone(), mapping_stats);
        }

        stats
    }

    /// 触发健康检查（已禁用）
    pub async fn trigger_health_check(&self) -> Result<u32> {
        warn!("健康检查功能已禁用");
        Ok(0)
    }

    /// 自动发现并添加上游域名
    pub async fn auto_discover_upstream(&self, domain: &str) -> Result<bool> {
        self.domain_pool
            .auto_add_upstream_domain(domain)
            .await
            .map_err(|e| anyhow::anyhow!("自动发现上游域名失败: {:?}", e))
    }
}

/// 映射统计信息
#[derive(Debug, Clone)]
pub struct MappingStats {
    pub downstream_domain: String,
    pub upstream_domain: String,
    pub request_count: u64,
    pub success_count: u64,
    pub error_count: u64,
    pub avg_response_time: u64,
    pub last_used: Option<chrono::DateTime<chrono::Utc>>,
}

/// 代理路由器 - 负责请求路由逻辑
pub struct ProxyRouter {
    proxy_manager: Arc<ProxyManager>,
}

impl ProxyRouter {
    pub fn new(proxy_manager: Arc<ProxyManager>) -> Self {
        Self { proxy_manager }
    }

    /// 路由请求到合适的上游服务器
    pub async fn route_request(&self, host: &str, path: &str) -> Option<String> {
        // 首先尝试精确匹配
        if let Some(upstream) = self.proxy_manager.find_upstream_for_domain(host).await {
            return Some(upstream);
        }

        // 尝试通配符匹配
        if let Some(upstream) = self.wildcard_match(host).await {
            return Some(upstream);
        }

        // 如果没有找到映射，尝试自动发现
        if let Ok(discovered) = self.proxy_manager.auto_discover_upstream(host).await {
            if discovered {
                // 重新查找映射
                return self.proxy_manager.find_upstream_for_domain(host).await;
            }
        }

        None
    }

    /// 通配符匹配
    async fn wildcard_match(&self, host: &str) -> Option<String> {
        // 这里可以实现更复杂的通配符匹配逻辑
        // 例如：*.example.com 匹配 sub.example.com

        // 简化实现：尝试匹配父域名
        if let Some(parent_domain) = self.get_parent_domain(host) {
            return self
                .proxy_manager
                .find_upstream_for_domain(&parent_domain)
                .await;
        }

        None
    }

    /// 获取父域名
    fn get_parent_domain(&self, host: &str) -> Option<String> {
        let parts: Vec<&str> = host.split('.').collect();
        if parts.len() > 2 {
            Some(parts[1..].join("."))
        } else {
            None
        }
    }
}

/// 代理中间件 - 处理请求和响应
pub struct ProxyMiddleware {
    router: Arc<ProxyRouter>,
}

impl ProxyMiddleware {
    pub fn new(router: Arc<ProxyRouter>) -> Self {
        Self { router }
    }

    /// 处理入站请求
    pub async fn handle_request(&self, host: &str, path: &str) -> Option<String> {
        let start_time = std::time::Instant::now();

        let upstream = self.router.route_request(host, path).await;

        let elapsed = start_time.elapsed();
        tracing::debug!("路由请求: {} -> {:?} (耗时: {:?})", host, upstream, elapsed);

        upstream
    }

    /// 记录请求统计
    pub async fn log_request_stats(
        &self,
        downstream: &str,
        upstream: &str,
        success: bool,
        response_time: u64,
    ) {
        if let Err(e) = self
            .router
            .proxy_manager
            .domain_pool
            .update_mapping_stats(downstream, response_time, success)
            .await
        {
            tracing::warn!("更新映射统计失败: {:?}", e);
        }
    }
}
