//! 安全配置模块 - 已迁移到统一配置系统
pub use crate::types::SecurityConfiguration;
// pub use crate::config::{
//     DatabaseConfiguration, TlsConfiguration,
//     RateLimitConfiguration, get_security_config
// }; // 暂时注释，这些类型不存在

use crate::types::{ProxyError, ProxyResult as Result};
use serde::{Deserialize, Serialize};
use std::fmt;

// 条件导入zeroize
#[cfg(feature = "security-extra")]
use zeroize::{Zeroize, ZeroizeOnDrop};

/// 获取安全配置的简化实现
pub async fn get_security_config() -> crate::types::SecurityConfiguration {
    crate::types::SecurityConfiguration::default()
}

/// 安全字符串类型，自动清零内存 - 保留原有功能
#[cfg(feature = "security-extra")]
#[derive(Clone, Serialize, Deserialize, Zeroize, ZeroizeOnDrop)]
pub struct SecretString {
    #[zeroize(skip)]
    inner: String,
}

/// 简化版安全字符串类型 - 不使用zeroize
#[cfg(not(feature = "security-extra"))]
#[derive(Clone, Serialize, Deserialize)]
pub struct SecretString {
    inner: String,
}

impl SecretString {
    /// 创建新的安全字符串
    pub fn new(secret: String) -> Self {
        Self { inner: secret }
    }

    /// 安全暴露字符串内容（仅在需要时使用）
    pub fn expose_secret(&self) -> &str {
        &self.inner
    }

    /// 验证密钥强度 - 集成到统一系统
    pub fn validate_strength(&self, min_length: usize) -> Result<()> {
        if self.inner.len() < min_length {
            return Err(ProxyError::invalid_input(&format!(
                "密钥长度不能少于 {} 字符",
                min_length
            )));
        }

        // 检查字符复杂度
        let has_upper = self.inner.chars().any(|c| c.is_uppercase());
        let has_lower = self.inner.chars().any(|c| c.is_lowercase());
        let has_digit = self.inner.chars().any(|c| c.is_numeric());
        let has_special = self.inner.chars().any(|c| !c.is_alphanumeric());

        let complexity_score = [has_upper, has_lower, has_digit, has_special]
            .iter()
            .filter(|&&x| x)
            .count();

        if complexity_score < 3 {
            return Err(ProxyError::invalid_input(
                "密钥必须包含大写字母、小写字母、数字和特殊字符中的至少3种",
            ));
        }

        Ok(())
    }
}

impl fmt::Debug for SecretString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.write_str("[REDACTED]")
    }
}

impl fmt::Display for SecretString {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.write_str("[REDACTED]")
    }
}

/// 密钥轮换管理器 - 保留原有功能但简化
pub struct KeyRotationManager {
    current_key: String,
    previous_key: Option<String>,
    last_rotation: std::time::SystemTime,
}

impl KeyRotationManager {
    pub fn new(initial_key: String) -> Result<Self> {
        if initial_key.len() < 32 {
            return Err(ProxyError::config("初始密钥长度不足"));
        }

        Ok(Self {
            current_key: initial_key,
            previous_key: None,
            last_rotation: std::time::SystemTime::now(),
        })
    }

    /// 轮换密钥
    pub fn rotate_key(&mut self, new_key: String) -> Result<()> {
        if new_key.len() < 32 {
            return Err(ProxyError::config("新密钥长度不足"));
        }

        if new_key == self.current_key {
            return Err(ProxyError::config("新密钥不能与当前密钥相同"));
        }

        self.previous_key = Some(self.current_key.clone());
        self.current_key = new_key;
        self.last_rotation = std::time::SystemTime::now();

        tracing::info!("JWT密钥已轮换");
        Ok(())
    }

    /// 获取当前密钥
    pub fn current_key(&self) -> &str {
        &self.current_key
    }

    /// 获取前一个密钥（用于验证旧token）
    pub fn previous_key(&self) -> Option<&str> {
        self.previous_key.as_deref()
    }

    /// 生成新的随机密钥
    pub fn generate_new_key() -> String {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        let bytes: Vec<u8> = (0..64).map(|_| rng.gen()).collect();
        use base64::{engine::general_purpose, Engine as _};
        general_purpose::STANDARD.encode(&bytes)
    }

    /// 获取所有有效密钥
    pub fn get_valid_keys(&self) -> Vec<&str> {
        let mut keys = vec![self.current_key.as_str()];
        if let Some(prev) = &self.previous_key {
            keys.push(prev.as_str());
        }
        keys
    }
}

/// 安全配置工具函数 - 简化版本
pub mod security_utils {
    use super::*;
    use crate::security::get_security_config;

    /// 获取当前JWT密钥（从统一配置系统）
    pub async fn get_current_jwt_secret() -> Result<String> {
        let config = get_security_config().await;
        if config.jwt_secret.is_empty() {
            return Err(ProxyError::config("JWT密钥未配置"));
        }
        Ok(config.jwt_secret)
    }

    /// 验证数据库连接配置
    pub async fn validate_database_config() -> Result<()> {
        let config = get_security_config().await;

        if config.database.url.is_empty() {
            return Err(ProxyError::config("MongoDB URI未配置"));
        }

        if !config.database.url.starts_with("mongodb://")
            && !config.database.url.starts_with("mongodb+srv://")
        {
            return Err(ProxyError::config("MongoDB URI格式无效"));
        }

        Ok(())
    }

    /// 检查TLS配置
    pub async fn validate_tls_config() -> Result<()> {
        let config = get_security_config().await;

        if config.tls.enabled {
            if !std::path::Path::new(&config.tls.cert_path).exists() {
                return Err(ProxyError::config(&format!(
                    "TLS证书文件不存在: {}",
                    config.tls.cert_path
                )));
            }

            if !std::path::Path::new(&config.tls.key_path).exists() {
                return Err(ProxyError::config(&format!(
                    "TLS私钥文件不存在: {}",
                    config.tls.key_path
                )));
            }
        }

        Ok(())
    }

    /// 生成安全配置模板（集成到统一配置系统）
    pub fn generate_secure_template() -> String {
        r#"# 安全配置模板 - 集成到统一配置系统中
# 请在主配置文件或环境变量中设置以下值

security:
  jwt_secret: "" # 使用: openssl rand -base64 64 生成
  database:
    mongodb_uri: "********************************:port/database?ssl=true"
    redis_uri: "redis://:password@host:port/db"
    connection_timeout: 30
  tls:
    cert_path: "/path/to/certificate.pem"
    key_path: "/path/to/private-key.pem"
    enabled: true
  rate_limiting:
    enabled: true
    requests_per_second: 100
    burst_size: 200

# 环境变量形式（优先级更高）：
# JWT_SECRET=your_jwt_secret_here
# MONGODB_URI=mongodb://...
# REDIS_URI=redis://...
# TLS_CERT_PATH=/path/to/cert.pem
# TLS_KEY_PATH=/path/to/key.pem
"#
        .to_string()
    }
}

/// 向后兼容的类型别名
#[deprecated(note = "请使用 crate::config::SecurityConfiguration")]
pub type SecureConfig = SecurityConfiguration;

#[deprecated(note = "请使用 crate::config::get_security_config()")]
pub async fn load_secure_config() -> Result<SecurityConfiguration> {
    Ok(get_security_config().await)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_secret_string_validation() {
        let weak_secret = SecretString::new("123456".to_string());
        assert!(weak_secret.validate_strength(32).is_err());

        let strong_secret = SecretString::new("MyStr0ng!P@ssw0rd#2024$SecureKey".to_string());
        assert!(strong_secret.validate_strength(32).is_ok());
    }

    #[test]
    fn test_key_rotation() {
        let mut manager =
            KeyRotationManager::new("MyStr0ng!P@ssw0rd#2024$SecureKey".to_string()).unwrap();

        assert!(manager
            .rotate_key("NewStr0ng!P@ssw0rd#2024$SecureKey2".to_string())
            .is_ok());

        assert_eq!(manager.get_valid_keys().len(), 2);
    }

    #[test]
    fn test_secret_string_debug() {
        let secret = SecretString::new("sensitive_data".to_string());
        let debug_output = format!("{:?}", secret);
        assert_eq!(debug_output, "[REDACTED]");
        assert!(!debug_output.contains("sensitive_data"));
    }

    #[tokio::test]
    async fn test_security_utils() {
        // 这些测试依赖于统一配置系统
        // 在实际使用中，配置应该先被初始化

        // 测试JWT密钥获取
        // let jwt_result = security_utils::get_current_jwt_secret().await;
        // 由于配置可能未初始化，这里只测试函数存在性

        // 测试配置验证
        // let db_result = security_utils::validate_database_config().await;
        // let tls_result = security_utils::validate_tls_config().await;
    }
}
