//! Security module - 安全模块
pub mod config;
pub mod header_security;
pub mod input_validation; // 完善的输入验证模块
                          // pub mod input_validator; // 文件不存在，暂时注释
                          // pub mod jwt_manager; // 复杂JWT管理器已删除，使用auth模块中的简单版本
pub mod manager; // 安全管理器

// 重新导出新的输入验证功能，替换原有的简单实现
pub use input_validation::{UnifiedValidator, ValidationConfig};

// 重新导出JWT管理功能

// 重新导出安全管理器

// 添加根级别的函数导出 - 不重复定义SecurityMonitor
pub fn generate_new_jwt_secret() -> String {
    use rand::{thread_rng, Rng};
    const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                             abcdefghijklmnopqrstuvwxyz\
                             0123456789+";
    const SECRET_LEN: usize = 64;

    let mut rng = thread_rng();
    (0..SECRET_LEN)
        .map(|_| {
            let idx = rng.gen_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

pub fn update_jwt_secret_everywhere(new_secret: &str) -> Result<(), String> {
    // 更新环境变量
    std::env::set_var("JWT_SECRET", new_secret);

    // 尝试写入到配置文件（如果存在）
    let config_path = std::path::Path::new("config/security.conf");
    if let Ok(mut file) = std::fs::OpenOptions::new()
        .create(true)
        .write(true)
        .truncate(true)
        .open(config_path)
    {
        use std::io::Write;
        if let Err(e) = writeln!(file, "JWT_SECRET={}", new_secret) {
            tracing::warn!("写入JWT密钥到配置文件失败: {}", e);
        }
    }

    tracing::info!("JWT密钥已更新");
    Ok(())
}

pub async fn get_security_config() -> crate::types::SecurityConfiguration {
    crate::types::SecurityConfiguration::default()
}

// 核心依赖
use once_cell::sync::Lazy;
use parking_lot::RwLock;
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use thiserror::Error;
use tracing::{error, info, warn};
use uuid::Uuid;

use proxy_config::SecurityConfig;

// 预编译的正则表达式 - 避免重复编译
static SENSITIVE_PATTERNS: Lazy<Vec<regex::Regex>> = Lazy::new(|| {
    vec![
        // 密码相关
        regex::Regex::new(r"(?i)(password|pwd|pass|secret|key|token)[:=]\s*[^\s]+").unwrap(),
        // 邮箱地址
        regex::Regex::new(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}").unwrap(),
        // 信用卡号
        regex::Regex::new(r"\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b").unwrap(),
        // 手机号
        regex::Regex::new(r"\b1[3-9]\d{9}\b").unwrap(),
        // IP地址的部分遮蔽
        regex::Regex::new(r"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b").unwrap(),
    ]
});

#[derive(Error, Debug)]
pub enum SecurityError {
    #[error("访问被拒绝: {reason}")]
    AccessDenied { reason: String },

    #[error("IP地址被封禁: {ip}")]
    IpBlocked { ip: IpAddr },

    #[error("请求被恶意检测拦截: {reason}")]
    MaliciousRequest { reason: String },

    #[error("CSRF令牌验证失败")]
    CsrfTokenInvalid,

    #[error("认证失败: {reason}")]
    AuthenticationFailed { reason: String },

    #[error("授权失败: 权限不足")]
    AuthorizationFailed,

    #[error("会话过期或无效")]
    SessionInvalid,

    #[error("速率限制: {message}")]
    RateLimit { message: String },

    #[error("资源限制: {message}")]
    Resource { message: String },
}

/// 安全事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityEventType {
    MaliciousRequest,
    IpBlocked,
    RateLimitExceeded,
    AuthenticationFailed,
    AuthorizationFailed,
    ResourceLimitExceeded,
    CsrfAttack,
    SqlInjectionAttempt,
    XssAttempt,
    DirectoryTraversalAttempt,
    BruteForceAttempt,
    SuspiciousLogin,
    PathTraversal,
    UnauthorizedAccess,
    SensitiveFileAccess,
}

/// 安全事件严重程度
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum SecuritySeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 安全事件
#[derive(Debug, Clone, Serialize)]
pub struct SecurityEvent {
    // 保留核心字段
    pub id: String,
    pub event_type: SecurityEventType,
    pub severity: SecuritySeverity,
    pub source_ip: Option<IpAddr>,
    pub timestamp: SystemTime,
}

impl SecurityEvent {
    /// 创建可疑登录事件
    pub fn suspicious_login(
        _username: String,
        ip: String,
        _user_agent: String,
        _reason: String,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            event_type: SecurityEventType::SuspiciousLogin,
            severity: SecuritySeverity::Medium,
            source_ip: ip.parse().ok(),
            timestamp: SystemTime::now(),
        }
    }

    /// 创建路径遍历事件
    pub fn path_traversal(ip: String, _path: String, _user_agent: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            event_type: SecurityEventType::PathTraversal,
            severity: SecuritySeverity::High,
            source_ip: ip.parse().ok(),
            timestamp: SystemTime::now(),
        }
    }

    /// 提取IP地址
    pub fn get_ip(&self) -> Option<String> {
        self.source_ip.map(|ip| ip.to_string())
    }
}

/// 威胁检测规则
#[derive(Debug, Clone)]
pub struct ThreatRule {
    pub id: String,
    pub name: String,
    pub pattern: regex::Regex,
    pub severity: SecuritySeverity,
    pub action: ThreatAction,
    pub enabled: bool,
}

/// 威胁响应动作
#[derive(Debug, Clone, Copy)]
pub enum ThreatAction {
    Log,
    Block,
    BanIp,
    RateLimit,
}

/// IP封禁信息
#[derive(Debug, Clone)]
pub struct IpBan {
    pub ip: IpAddr,
    pub reason: String,
    pub banned_at: SystemTime,
    pub expires_at: Option<SystemTime>,
    pub ban_count: u32,
}

/// IP信誉信息
#[derive(Debug, Clone)]
pub struct IpReputation {
    pub threat_score: u32,
    pub last_seen: Instant,
    pub violations: Vec<SecurityEvent>,
}

// 复杂的安全监控器已删除 - 使用简化的安全管理

// SecurityMonitor实现已删除

// SecurityMonitor方法已删除

// SecurityMonitor私有方法已删除
// SecurityMonitor剩余代码已删除

/// 安全日志处理器 - 简化版，移除重复功能
pub struct SecureLogger {
    sensitive_patterns: Vec<Regex>,
}

impl SecureLogger {
    pub fn new() -> Self {
        Self {
            sensitive_patterns: SENSITIVE_PATTERNS.clone(),
        }
    }

    /// 清理敏感数据
    pub fn sanitize_log_input(&self, input: &str, max_len: usize) -> String {
        // 使用通用字符串工具进行截断
        let mut sanitized = if input.len() > max_len {
            input[..max_len].to_string()
        } else {
            input.to_string()
        };

        // 清理敏感信息
        for pattern in &self.sensitive_patterns {
            sanitized = pattern.replace_all(&sanitized, "[REDACTED]").to_string();
        }

        // 清理控制字符
        // 简化字符串清理
        sanitized.chars().filter(|c| !c.is_control()).collect()
    }
}

impl Default for SecureLogger {
    fn default() -> Self {
        Self::new()
    }
}

// SecurityMonitor全局函数已删除

// SecurityManager已从manager模块导入，删除重复定义

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sanitize_sensitive_data() {
        let logger = SecureLogger::new();
        let input = "password=mysecret123";
        let result = logger.sanitize_log_input(input, 100);
        assert!(!result.contains("mysecret123"));
    }
}
