// src/api/domain_group.rs

// 域名分组相关功能实现

use axum::{
    extract::{Extension, Json, Path},
    http::StatusCode,
    response::IntoResponse,
};
use chrono::Utc;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

use crate::api::ApiResponse;
use crate::db::models::DomainGroup;
use crate::types::AppState;

// 创建域名分组请求结构
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateGroupRequest {
    pub name: String,
    pub description: Option<String>,
    pub domains: Option<Vec<String>>,
    pub load_balancing_strategy: Option<String>,
    pub health_check_enabled: Option<bool>,
}

// 更新域名分组请求结构
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateGroupRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub is_active: Option<bool>,
    pub load_balancing_strategy: Option<String>,
    pub health_check_enabled: Option<bool>,
}

// 添加域名到分组请求结构
#[derive(Debug, Serialize, Deserialize)]
pub struct AddDomainRequest {
    pub domain: String,
}

// 获取所有域名分组
pub async fn list_domain_groups(Extension(state): Extension<Arc<AppState>>) -> impl IntoResponse {
    // 创建分页参数
    let pagination = crate::types::Pagination {
        page: 1,
        per_page: 100,
        total: 0,
        total_pages: 0,
    };

    match state.database.list_domain_groups(&pagination).await {
        Ok(response) => (
            StatusCode::OK,
            axum::Json(ApiResponse {
                success: true,
                message: Some(format!("获取到 {} 个域名分组", response.items.len())),
                data: Some(response.items),
                error: None,
                error_code: None,
                request_id: None,
            }),
        ),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            axum::Json(ApiResponse::<Vec<DomainGroup>> {
                success: false,
                message: Some("获取域名分组失败".to_string()),
                data: None,
                error: Some(e.to_string()),
                error_code: None,
                request_id: None,
            }),
        ),
    }
}

// 获取指定ID的域名分组
pub async fn get_domain_group(
    Extension(state): Extension<Arc<AppState>>,
    Path(group_id): Path<String>,
) -> impl IntoResponse {
    match state.database.get_domain_group_by_id(&group_id).await {
        Ok(Some(group)) => (
            StatusCode::OK,
            axum::Json(ApiResponse {
                success: true,
                message: Some(format!("成功获取域名分组 '{}'", group.name)),
                data: Some(group),
                error: None,
                error_code: None,
                request_id: None,
            }),
        ),
        Ok(None) => (
            StatusCode::NOT_FOUND,
            axum::Json(ApiResponse::<DomainGroup> {
                success: false,
                message: Some(format!("未找到ID为 {} 的域名分组", group_id)),
                data: None,
                error: Some("GroupNotFound".to_string()),
                error_code: None,
                request_id: None,
            }),
        ),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            axum::Json(ApiResponse::<DomainGroup> {
                success: false,
                message: Some("获取域名分组失败".to_string()),
                data: None,
                error: Some(e.to_string()),
                error_code: None,
                request_id: None,
            }),
        ),
    }
}

// 创建新的域名分组
pub async fn create_domain_group(
    Extension(state): Extension<Arc<AppState>>,
    axum::Json(req): axum::Json<CreateGroupRequest>,
) -> impl IntoResponse {
    let group = DomainGroup {
        id: None, // 数据库会自动分配ID
        name: req.name,
        description: req.description,
        domains: req.domains.unwrap_or_default(),
        load_balancing_strategy: "none".to_string(), // 不再支持负载均衡
        health_check_enabled: false,                 // 不再支持健康检查
        is_active: true,
        created_at: Some(Utc::now()),
        updated_at: Some(Utc::now()),
    };

    match state.database.create_domain_group(&group).await {
        Ok(_) => (
            StatusCode::CREATED,
            axum::Json(ApiResponse {
                success: true,
                message: Some(format!("成功创建域名分组 '{}'", group.name)),
                data: Some(group),
                error: None,
                error_code: None,
                request_id: None,
            }),
        ),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            axum::Json(ApiResponse::<DomainGroup> {
                success: false,
                message: Some("创建域名分组失败".to_string()),
                data: None,
                error: Some(e.to_string()),
                error_code: None,
                request_id: None,
            }),
        ),
    }
}

// 更新指定域名分组
pub async fn update_domain_group(
    Extension(state): Extension<Arc<AppState>>,
    Path(group_id): Path<String>,
    Json(req): Json<UpdateGroupRequest>,
) -> impl IntoResponse {
    match state.database.get_domain_group_by_id(&group_id).await {
        Ok(Some(mut group)) => {
            if let Some(name) = req.name {
                group.name = name;
            }
            if let Some(desc) = req.description {
                group.description = Some(desc);
            }
            if let Some(active) = req.is_active {
                group.is_active = active;
            }
            if let Some(strategy) = req.load_balancing_strategy {
                group.load_balancing_strategy = strategy;
            }
            if let Some(health_check) = req.health_check_enabled {
                group.health_check_enabled = health_check;
            }
            group.updated_at = Some(Utc::now());

            match state.database.update_domain_group(&group).await {
                Ok(_) => (
                    StatusCode::OK,
                    axum::Json(ApiResponse {
                        success: true,
                        message: Some(format!("成功更新域名分组 '{}'", group.name)),
                        data: Some(group),
                        error: None,
                        error_code: None,
                        request_id: None,
                    }),
                ),
                Err(e) => (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    axum::Json(ApiResponse::<DomainGroup> {
                        success: false,
                        message: Some("更新域名分组失败".to_string()),
                        data: None,
                        error: Some(e.to_string()),
                        error_code: None,
                        request_id: None,
                    }),
                ),
            }
        }
        Ok(None) => (
            StatusCode::NOT_FOUND,
            axum::Json(ApiResponse::<DomainGroup> {
                success: false,
                message: Some(format!("未找到ID为 {} 的域名分组", group_id)),
                data: None,
                error: Some("GroupNotFound".to_string()),
                error_code: None,
                request_id: None,
            }),
        ),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            axum::Json(ApiResponse::<DomainGroup> {
                success: false,
                message: Some("获取域名分组失败".to_string()),
                data: None,
                error: Some(e.to_string()),
                error_code: None,
                request_id: None,
            }),
        ),
    }
}

// 删除指定域名分组
pub async fn delete_domain_group(
    Extension(state): Extension<Arc<AppState>>,
    Path(group_id): Path<String>,
) -> impl IntoResponse {
    match state.database.get_domain_group_by_id(&group_id).await {
        Ok(Some(_)) => match state.database.delete_domain_group(&group_id).await {
            Ok(_) => (
                StatusCode::OK,
                axum::Json(ApiResponse::<()> {
                    success: true,
                    message: Some("成功删除域名分组".to_string()),
                    data: None,
                    error: None,
                    error_code: None,
                    request_id: None,
                }),
            ),
            Err(e) => (
                StatusCode::INTERNAL_SERVER_ERROR,
                axum::Json(ApiResponse::<()> {
                    success: false,
                    message: Some("删除域名分组失败".to_string()),
                    data: None,
                    error: Some(e.to_string()),
                    error_code: None,
                    request_id: None,
                }),
            ),
        },
        Ok(None) => (
            StatusCode::NOT_FOUND,
            axum::Json(ApiResponse::<()> {
                success: false,
                message: Some(format!("未找到ID为 {} 的域名分组", group_id)),
                data: None,
                error: Some("GroupNotFound".to_string()),
                error_code: None,
                request_id: None,
            }),
        ),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            axum::Json(ApiResponse::<()> {
                success: false,
                message: Some("获取域名分组失败".to_string()),
                data: None,
                error: Some(e.to_string()),
                error_code: None,
                request_id: None,
            }),
        ),
    }
}

// SPA架构：所有页面都通过index.html + JavaScript渲染
// 不需要单独的HTML页面，前端会通过API获取数据并动态渲染

/// 创建域名分组相关的路由
pub fn routes() -> axum::Router<Arc<AppState>> {
    use axum::routing::get;

    axum::Router::new()
        .route(
            "/domain-groups",
            get(list_domain_groups).post(create_domain_group),
        )
        .route(
            "/domain-groups/:id",
            get(get_domain_group)
                .put(update_domain_group)
                .delete(delete_domain_group),
        )
    // SPA架构：页面渲染由前端JavaScript处理，不需要服务端HTML路由
}
