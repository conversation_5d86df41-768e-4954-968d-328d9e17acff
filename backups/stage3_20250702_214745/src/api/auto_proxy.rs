use axum::{
    extract::{Extension, Path, Query},
    http::StatusCode,
    response::Json,
    routing::{delete, get, post, put},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
/// 自动代理API端点
use std::sync::Arc;

use crate::proxy_manager::{EnhancedProxyConfig, RecursiveProxyConfig};
use crate::ssl::{DnsProviderConfig, SslManagerConfig};
// use crate::domain_mapping::DomainMappingConfig; // 已整合到domain_pool中
use crate::types::{ApiResponse, AppState};

/// 自动代理配置请求
#[derive(Debug, Deserialize)]
pub struct AutoProxyConfigRequest {
    pub enable_auto_ssl: Option<bool>,
    pub enable_content_replacement: Option<bool>,
    pub max_subdomains_per_root: Option<u32>,
    pub max_redirect_hops: Option<u32>,
    pub ssl_cert_dir: Option<String>,
    pub contact_email: Option<String>,
    pub dns_provider: Option<DnsProviderConfigRequest>,
    pub recursive_config: Option<RecursiveProxyConfigRequest>,
}

/// DNS提供商配置请求
#[derive(Debug, Deserialize)]
pub struct DnsProviderConfigRequest {
    pub provider: String,
    pub access_key_id: String,
    pub access_key_secret: String,
    pub region: Option<String>,
}

/// 递归代理配置请求
#[derive(Debug, Deserialize)]
pub struct RecursiveProxyConfigRequest {
    pub enabled: Option<bool>,
    pub max_depth: Option<u32>,
    pub check_interval_minutes: Option<u64>,
    pub concurrency: Option<u32>,
    pub timeout_seconds: Option<u64>,
    pub content_threshold_kb: Option<u32>,
}

/// 域名映射创建请求
#[derive(Debug, Deserialize)]
pub struct CreateMappingRequest {
    pub downstream_domain: String,
    pub upstream_domain: String,
    pub ssl_enabled: Option<bool>,
}

/// 域名映射更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateMappingRequest {
    pub downstream_domain: Option<String>,
    pub upstream_domain: Option<String>,
    pub ssl_enabled: Option<bool>,
}

/// SSL证书申请请求
#[derive(Debug, Deserialize)]
pub struct SslCertificateRequest {
    pub domain: String,
    pub force_renewal: Option<bool>,
}

/// 获取自动代理配置
pub async fn get_config(
    Extension(state): Extension<Arc<AppState>>,
) -> Result<Json<ApiResponse<EnhancedProxyConfig>>, StatusCode> {
    // 从数据库或配置文件加载配置
    let config = EnhancedProxyConfig::default(); // 临时使用默认配置

    Ok(Json(ApiResponse::success(config)))
}

/// 更新自动代理配置
pub async fn update_config(
    Extension(state): Extension<Arc<AppState>>,
    Json(request): Json<AutoProxyConfigRequest>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    // 构建新配置
    let mut config = EnhancedProxyConfig::default();

    if let Some(enable_auto_ssl) = request.enable_auto_ssl {
        config.enable_ssl = enable_auto_ssl;
    }

    if let Some(enable_content_replacement) = request.enable_content_replacement {
        config.enable_content_replacement = enable_content_replacement;
    }

    // TODO: 重构后需要更新配置字段
    // if let Some(max_subdomains) = request.max_subdomains_per_root {
    //     config.domain_mapping.max_subdomains_per_root = max_subdomains;
    // }

    // if let Some(max_redirect_hops) = request.max_redirect_hops {
    //     config.domain_mapping.max_redirect_hops = max_redirect_hops;
    // }

    // TODO: SSL配置需要重构，暂时注释掉
    // if let Some(cert_dir) = request.ssl_cert_dir {
    //     config.ssl.cert_dir = cert_dir;
    // }

    // if let Some(email) = request.contact_email {
    //     config.ssl.contact_email = email;
    // }

    // if let Some(dns_config) = request.dns_provider {
    //     config.ssl.dns_provider = DnsProviderConfig {
    //         provider: dns_config.provider,
    //         access_key_id: dns_config.access_key_id,
    //         access_key_secret: dns_config.access_key_secret,
    //         region: dns_config.region,
    //     };
    // }

    if let Some(recursive_config) = request.recursive_config {
        if let Some(enabled) = recursive_config.enabled {
            config.recursive_config.enabled = enabled;
        }
        if let Some(max_depth) = recursive_config.max_depth {
            config.recursive_config.max_depth = max_depth;
        }
        if let Some(interval) = recursive_config.check_interval_minutes {
            config.recursive_config.check_interval_minutes = interval;
        }
        if let Some(concurrency) = recursive_config.concurrency {
            config.recursive_config.concurrency = concurrency;
        }
        if let Some(timeout) = recursive_config.timeout_seconds {
            config.recursive_config.timeout_seconds = timeout;
        }
        if let Some(threshold) = recursive_config.content_threshold_kb {
            config.recursive_config.content_threshold_kb = threshold;
        }
    }

    // 保存配置到数据库
    // TODO: 实现配置保存逻辑

    Ok(Json(ApiResponse::success(())))
}

/// 创建域名映射
pub async fn create_domain_mapping(
    Extension(state): Extension<Arc<AppState>>,
    Json(request): Json<CreateMappingRequest>,
) -> Result<Json<ApiResponse<String>>, StatusCode> {
    use crate::domains::ManualMappingRequest;

    // 创建映射请求（重复检测已在repository层处理）
    let mapping_request = ManualMappingRequest {
        downstream_domain: request.downstream_domain.clone(),
        upstream_domain: request.upstream_domain.clone(),
    };

    // 添加映射
    match state
        .domain_pool_service
        .create_manual_mapping(mapping_request)
        .await
    {
        Ok(_) => Ok(Json(ApiResponse::success(format!(
            "成功创建映射：{} → {}",
            request.downstream_domain, request.upstream_domain
        )))),
        Err(e) => Ok(Json(ApiResponse::error(e.to_string()))),
    }
}

/// 获取域名映射列表
pub async fn get_domain_mappings(
    Extension(state): Extension<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<serde_json::Value>>>, StatusCode> {
    match state.domain_pool_service.get_all_mappings(1, 1000).await {
        Ok(mappings) => {
            let json_mappings: Vec<serde_json::Value> = mappings
                .into_iter()
                .map(|mapping| {
                    serde_json::json!({
                        "id": mapping.id,
                        "downstream_domain": mapping.downstream_domain,
                        "upstream_domain": mapping.upstream_domain,
                        "ssl_enabled": false, // 默认值，ProxyMapping没有此字段
                        "created_at": mapping.created_at,
                        "status": "active"
                    })
                })
                .collect();

            Ok(Json(ApiResponse::success(json_mappings)))
        }
        Err(e) => {
            tracing::error!("获取映射列表失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 获取单个域名映射
pub async fn get_domain_mapping(
    Extension(state): Extension<Arc<AppState>>,
    Path(mapping_id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    match state
        .domain_pool_service
        .get_mapping_by_id(&mapping_id)
        .await
    {
        Ok(mapping) => {
            let json_mapping = serde_json::json!({
                "id": mapping.id.map(|id| id.to_hex()).unwrap_or_default(),
                "downstream_domain": mapping.downstream_domain,
                "upstream_domain": mapping.upstream_domain,
                "ssl_enabled": mapping.ssl_enabled.unwrap_or(false),
                "is_manual": mapping.is_manual,
                "created_at": mapping.created_at,
                "last_accessed": mapping.last_used,
                "request_count": mapping.request_count,
                "avg_response_time": mapping.average_response_time.unwrap_or(0)
            });
            Ok(Json(ApiResponse::success(json_mapping)))
        }
        Err(e) => Ok(Json(ApiResponse::error(e.to_string()))),
    }
}

/// 更新域名映射
pub async fn update_domain_mapping(
    Extension(state): Extension<Arc<AppState>>,
    Path(mapping_id): Path<String>,
    Json(request): Json<UpdateMappingRequest>,
) -> Result<Json<ApiResponse<String>>, StatusCode> {
    match state
        .domain_pool_service
        .update_mapping(&mapping_id, request)
        .await
    {
        Ok(_) => {
            let message = "映射更新成功".to_string();
            Ok(Json(ApiResponse::success(message)))
        }
        Err(e) => Ok(Json(ApiResponse::error(e.to_string()))),
    }
}

/// 删除域名映射
pub async fn delete_domain_mapping(
    Extension(state): Extension<Arc<AppState>>,
    Path(mapping_id): Path<String>,
) -> Result<Json<ApiResponse<String>>, StatusCode> {
    match state.domain_pool_service.delete_mapping(&mapping_id).await {
        Ok(_) => {
            let message = "映射删除成功".to_string();
            Ok(Json(ApiResponse::success(message)))
        }
        Err(e) => Ok(Json(ApiResponse::error(e.to_string()))),
    }
}

/// 申请SSL证书
pub async fn request_ssl_certificate(
    Extension(state): Extension<Arc<AppState>>,
    Json(request): Json<SslCertificateRequest>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    // TODO: 实现SSL证书申请

    let cert_info = serde_json::json!({
        "domain": request.domain,
        "status": "pending",
        "created_at": chrono::Utc::now(),
        "expires_at": null
    });

    Ok(Json(ApiResponse::success(cert_info)))
}

/// 获取SSL证书列表
pub async fn get_ssl_certificates(
    Extension(state): Extension<Arc<AppState>>,
) -> Result<Json<ApiResponse<Vec<serde_json::Value>>>, StatusCode> {
    // TODO: 实现获取SSL证书列表
    let certificates = vec![];

    Ok(Json(ApiResponse::success(certificates)))
}

/// 获取SSL证书详情
pub async fn get_ssl_certificate(
    Extension(state): Extension<Arc<AppState>>,
    Path(domain): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    // TODO: 实现获取SSL证书详情

    let cert_info = serde_json::json!({
        "domain": domain,
        "status": "valid",
        "created_at": chrono::Utc::now(),
        "expires_at": chrono::Utc::now() + chrono::Duration::days(90)
    });

    Ok(Json(ApiResponse::success(cert_info)))
}

/// 获取自动代理统计信息
pub async fn get_auto_proxy_statistics(
    Extension(state): Extension<Arc<AppState>>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    // TODO: 实现获取统计信息

    let stats = serde_json::json!({
        "total_mappings": 0,
        "active_mappings": 0,
        "total_certificates": 0,
        "valid_certificates": 0,
        "total_requests": 0,
        "recursive_enabled": false
    });

    Ok(Json(ApiResponse::success(stats)))
}

/// 触发递归代理发现
pub async fn trigger_recursive_discovery(
    Extension(state): Extension<Arc<AppState>>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    // TODO: 实现触发递归代理发现

    Ok(Json(ApiResponse::success(())))
}

// ==================== 域名管理功能（从domain.rs迁移） ====================

/// 域名创建请求 - 带输入验证
#[derive(Debug, Deserialize)]
pub struct CreateDomainRequest {
    pub domain: String,
    pub backend_url: String,
    pub ssl_enabled: Option<bool>,
    pub ssl_cert_path: Option<String>,
    pub ssl_key_path: Option<String>,
    pub cache_enabled: Option<bool>,
    pub cache_ttl: Option<i32>,
    pub rate_limit: Option<i32>,
    pub health_check_enabled: Option<bool>,
    pub health_check_path: Option<String>,
    pub health_check_interval: Option<i32>,
    pub compression_enabled: Option<bool>,
    pub websocket_enabled: Option<bool>,
    pub max_request_size: Option<i64>,
    pub timeout_secs: Option<i32>,
    pub retry_attempts: Option<i32>,
    pub cors_enabled: Option<bool>,
    pub cors_origins: Option<Vec<String>>,
}

/// 域名更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateDomainRequest {
    pub backend_url: Option<String>,
    pub ssl_enabled: Option<bool>,
    pub ssl_cert_path: Option<String>,
    pub ssl_key_path: Option<String>,
    pub cache_enabled: Option<bool>,
    pub cache_ttl: Option<i32>,
    pub rate_limit: Option<i32>,
    pub health_check_enabled: Option<bool>,
    pub health_check_path: Option<String>,
    pub health_check_interval: Option<i32>,
    pub compression_enabled: Option<bool>,
    pub websocket_enabled: Option<bool>,
    pub max_request_size: Option<i64>,
    pub timeout_secs: Option<i32>,
    pub retry_attempts: Option<i32>,
    pub cors_enabled: Option<bool>,
    pub cors_origins: Option<Vec<String>>,
}

/// 域名查询参数
#[derive(Debug, Deserialize)]
pub struct ListDomainsQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub search: Option<String>,
    pub ssl_enabled: Option<bool>,
    pub cache_enabled: Option<bool>,
    pub health_check_enabled: Option<bool>,
}

/// 获取域名列表 - 带分页和过滤
pub async fn list_domains(
    Extension(state): Extension<Arc<AppState>>,
    Query(query): Query<ListDomainsQuery>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    // TODO: 实现域名列表获取
    let domains = serde_json::json!({
        "domains": [],
        "total": 0,
        "page": query.page.unwrap_or(1),
        "per_page": query.per_page.unwrap_or(10)
    });

    Ok(Json(ApiResponse::success(domains)))
}

/// 创建域名 - 带全面验证
pub async fn create_domain(
    Extension(state): Extension<Arc<AppState>>,
    Json(req): Json<CreateDomainRequest>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    // TODO: 实现域名创建逻辑
    let domain_info = serde_json::json!({
        "id": "domain_001",
        "domain": req.domain,
        "backend_url": req.backend_url,
        "ssl_enabled": req.ssl_enabled.unwrap_or(false),
        "created_at": chrono::Utc::now()
    });

    Ok(Json(ApiResponse::success(domain_info)))
}

/// 获取单个域名
pub async fn get_domain(
    Extension(state): Extension<Arc<AppState>>,
    Path(domain_id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    // TODO: 实现获取单个域名
    let domain_info = serde_json::json!({
        "id": domain_id,
        "domain": "example.com",
        "backend_url": "http://localhost:3000",
        "ssl_enabled": true,
        "cache_enabled": true
    });

    Ok(Json(ApiResponse::success(domain_info)))
}

/// 更新域名
pub async fn update_domain(
    Extension(state): Extension<Arc<AppState>>,
    Path(domain_id): Path<String>,
    Json(req): Json<UpdateDomainRequest>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    // TODO: 实现域名更新逻辑
    let updated_info = serde_json::json!({
        "id": domain_id,
        "message": "域名更新成功",
        "updated_at": chrono::Utc::now()
    });

    Ok(Json(ApiResponse::success(updated_info)))
}

/// 删除域名
pub async fn delete_domain(
    Extension(state): Extension<Arc<AppState>>,
    Path(domain_id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    // TODO: 实现域名删除逻辑
    let result = serde_json::json!({
        "id": domain_id,
        "message": "域名删除成功",
        "deleted_at": chrono::Utc::now()
    });

    Ok(Json(ApiResponse::success(result)))
}

/// 测试域名连通性
pub async fn test_domain_connectivity(
    Extension(state): Extension<Arc<AppState>>,
    Path(domain): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    // TODO: 实现域名连通性测试
    let test_result = serde_json::json!({
        "domain": domain,
        "connectivity": true,
        "response_time_ms": 120,
        "status_code": 200,
        "tested_at": chrono::Utc::now()
    });

    Ok(Json(ApiResponse::success(test_result)))
}

// ==================== 认证功能（从mod.rs迁移） ====================

/// 用户登录
pub async fn login(
    Extension(state): Extension<Arc<AppState>>,
    Json(payload): Json<crate::auth::LoginRequest>,
) -> Result<Json<ApiResponse<crate::auth::LoginResponse>>, StatusCode> {
    tracing::info!("收到登录请求: username={}", payload.username);

    match state
        .auth_service
        .login(&payload.username, &payload.password)
        .await
    {
        Ok(token) => {
            let response = crate::auth::LoginResponse {
                token,
                username: payload.username.clone(),
                role: "admin".to_string(),
                expires_in: 86400,
            };
            tracing::info!("登录成功: username={}", payload.username);
            Ok(Json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::warn!("登录失败: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 用户登出
pub async fn logout() -> Result<Json<ApiResponse<&'static str>>, StatusCode> {
    Ok(Json(ApiResponse::success("已登出")))
}

/// 修改密码
pub async fn change_password(
    Extension(state): Extension<Arc<AppState>>,
    Json(payload): Json<crate::auth::ChangePasswordRequest>,
) -> Result<Json<ApiResponse<&'static str>>, StatusCode> {
    // TODO: 实现密码修改逻辑
    Ok(Json(ApiResponse::success("密码修改成功")))
}

/// 管理员初始化
pub async fn admin_init(
    Extension(state): Extension<Arc<AppState>>,
) -> Result<Json<ApiResponse<String>>, StatusCode> {
    match state.auth_service.ensure_admin_user().await {
        Ok(_) => {
            let message = "管理员账户初始化成功，请查看日志获取临时密码".to_string();
            Ok(Json(ApiResponse::success(message)))
        }
        Err(e) => {
            tracing::error!("管理员初始化失败: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 检查管理员是否存在
pub async fn check_admin_exists(
    Extension(state): Extension<Arc<AppState>>,
) -> Result<Json<ApiResponse<bool>>, StatusCode> {
    match state.auth_service.check_admin_exists().await {
        Ok(exists) => Ok(Json(ApiResponse::success(exists))),
        Err(e) => {
            tracing::error!("检查管理员存在失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

// ==================== 系统状态功能（从mod.rs迁移） ====================

/// 系统状态
pub async fn system_status() -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    let status = serde_json::json!({
        "status": "running",
        "uptime": "1h 30m",
        "memory_usage": "45%",
        "cpu_usage": "12%",
        "active_connections": 0,
        "total_requests": 0
    });

    Ok(Json(ApiResponse::success(status)))
}

/// 系统统计
pub async fn system_stats() -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    let stats = serde_json::json!({
        "domains": 0,
        "groups": 0,
        "requests_today": 0,
        "cache_hit_rate": "0%",
        "avg_response_time": "0ms"
    });

    Ok(Json(ApiResponse::success(stats)))
}

// ==================== 配置管理功能（从mod.rs迁移） ====================

/// 获取系统配置
pub async fn get_system_config() -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    let config = serde_json::json!({
        "cache_enabled": true,
        "cache_ttl": 3600,
        "rate_limit": 1000,
        "ssl_enabled": false,
        "compression_enabled": true
    });

    Ok(Json(ApiResponse::success(config)))
}

/// 更新系统配置
pub async fn update_system_config(
    Json(payload): Json<serde_json::Value>,
) -> Result<Json<ApiResponse<&'static str>>, StatusCode> {
    // TODO: 实现配置更新逻辑
    Ok(Json(ApiResponse::success("配置更新成功")))
}

// ==================== 域名组管理功能（从mod.rs迁移） ====================

/// 域名组列表
pub async fn list_domain_groups() -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    let groups = serde_json::json!({
        "groups": [],
        "total": 0,
        "page": 1,
        "per_page": 10
    });

    Ok(Json(ApiResponse::success(groups)))
}

/// 创建域名组
pub async fn create_domain_group(
    Json(payload): Json<serde_json::Value>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    let group_info = serde_json::json!({
        "id": "group_001",
        "name": payload.get("name").unwrap_or(&serde_json::Value::String("新域名组".to_string())),
        "created_at": chrono::Utc::now()
    });

    Ok(Json(ApiResponse::success(group_info)))
}

/// 获取域名组
pub async fn get_domain_group(
    Path(group_id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    let group_info = serde_json::json!({
        "id": group_id,
        "name": "示例域名组",
        "domains": [],
        "created_at": chrono::Utc::now()
    });

    Ok(Json(ApiResponse::success(group_info)))
}

/// 更新域名组
pub async fn update_domain_group(
    Path(group_id): Path<String>,
    Json(payload): Json<serde_json::Value>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    let updated_info = serde_json::json!({
        "id": group_id,
        "message": "域名组更新成功",
        "updated_at": chrono::Utc::now()
    });

    Ok(Json(ApiResponse::success(updated_info)))
}

/// 删除域名组
pub async fn delete_domain_group(
    Path(group_id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, StatusCode> {
    let result = serde_json::json!({
        "id": group_id,
        "message": "域名组删除成功",
        "deleted_at": chrono::Utc::now()
    });

    Ok(Json(ApiResponse::success(result)))
}

/// 统一API路由 - 包含所有功能
pub fn routes() -> Router<Arc<AppState>> {
    Router::new()
        // ==================== 认证API ====================
        .route("/api/auth/login", post(login))
        .route("/api/auth/logout", post(logout))
        .route("/api/auth/change-password", post(change_password))
        .route("/api/auth/admin_init", post(admin_init))
        .route("/api/auth/check_admin_exists", get(check_admin_exists))
        // ==================== 自动代理配置管理 ====================
        .route("/api/auto-proxy/config", get(get_config))
        .route("/api/auto-proxy/config", put(update_config))
        // ==================== 域名映射管理 ====================
        .route("/api/auto-proxy/mappings", get(get_domain_mappings))
        .route("/api/auto-proxy/mappings", post(create_domain_mapping))
        .route("/api/auto-proxy/mappings/:id", get(get_domain_mapping))
        .route("/api/auto-proxy/mappings/:id", put(update_domain_mapping))
        .route(
            "/api/auto-proxy/mappings/:id",
            delete(delete_domain_mapping),
        )
        // ==================== SSL证书管理 ====================
        .route("/api/auto-proxy/certificates", get(get_ssl_certificates))
        .route(
            "/api/auto-proxy/certificates",
            post(request_ssl_certificate),
        )
        .route(
            "/api/auto-proxy/certificates/:domain",
            get(get_ssl_certificate),
        )
        // ==================== 统计和控制 ====================
        .route("/api/auto-proxy/statistics", get(get_auto_proxy_statistics))
        .route(
            "/api/auto-proxy/recursive/trigger",
            post(trigger_recursive_discovery),
        )
        // ==================== 域名管理 ====================
        .route("/api/domains", get(list_domains))
        .route("/api/domains", post(create_domain))
        .route("/api/domains/:id", get(get_domain))
        .route("/api/domains/:id", put(update_domain))
        .route("/api/domains/:id", delete(delete_domain))
        .route("/api/domains/:domain/test", get(test_domain_connectivity))
        // ==================== 系统状态 ====================
        .route("/api/status", get(system_status))
        .route("/api/stats", get(system_stats))
        // ==================== 配置管理 ====================
        .route("/api/config", get(get_system_config))
        .route("/api/config", post(update_system_config))
        // ==================== 域名组管理 ====================
        .route("/api/domain-groups", get(list_domain_groups))
        .route("/api/domain-groups", post(create_domain_group))
        .route("/api/domain-groups/:id", get(get_domain_group))
        .route("/api/domain-groups/:id", put(update_domain_group))
        .route("/api/domain-groups/:id", delete(delete_domain_group))
}
