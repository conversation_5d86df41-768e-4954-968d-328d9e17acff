//! 递归代理配置模块

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 递归代理主配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveConfig {
    /// 是否启用递归代理
    pub enabled: bool,
    /// 最大递归深度
    pub max_depth: u32,
    /// 最大发现域名数量
    pub max_domains: u32,
    /// 触发配置
    pub trigger: TriggerConfig,
    /// 过滤配置
    pub filter: FilterConfig,
    /// 缓存配置
    pub cache: CacheConfig,
    /// 代理配置
    pub proxy: ProxyConfig,
    /// 浏览器伪装配置
    pub browser: BrowserConfig,
}

/// 触发条件配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TriggerConfig {
    /// 触发的HTTP状态码
    pub status_codes: Vec<u16>,
    /// 触发的内容类型
    pub content_types: Vec<String>,
    /// 是否跟踪重定向
    pub follow_redirects: bool,
    /// 最大重定向次数
    pub max_redirects: u32,
}

/// 过滤配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FilterConfig {
    /// 关键词黑名单
    pub keyword_blacklist: Vec<String>,
    /// 正则表达式黑名单
    pub regex_blacklist: Vec<String>,
    /// 国家域名黑名单
    pub country_blacklist: Vec<String>,
    /// 知名网站黑名单
    pub famous_sites_blacklist: Vec<String>,
    /// 自定义黑名单
    pub custom_blacklist: Vec<String>,
}

/// 缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    /// 是否启用缓存
    pub enabled: bool,
    /// 最大缓存大小（MB）
    pub max_size_mb: u64,
    /// 缓存TTL（秒）
    pub ttl_seconds: u64,
    /// 命中率阈值（低于此值将被清理）
    pub hit_rate_threshold: f64,
    /// 清理间隔（秒）
    pub cleanup_interval_seconds: u64,
}

/// 代理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyConfig {
    /// 是否使用第三方代理
    pub use_proxy: bool,
    /// 代理服务器列表
    pub proxy_servers: Vec<ProxyServer>,
    /// 代理轮换策略
    pub rotation_strategy: ProxyRotationStrategy,
    /// 请求超时（秒）
    pub timeout_seconds: u64,
    /// 重试次数
    pub retry_count: u32,
}

/// 代理服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyServer {
    /// 代理地址
    pub host: String,
    /// 代理端口
    pub port: u16,
    /// 代理类型
    pub proxy_type: ProxyType,
    /// 用户名（可选）
    pub username: Option<String>,
    /// 密码（可选）
    pub password: Option<String>,
    /// 是否启用
    pub enabled: bool,
    /// 权重（用于负载均衡）
    pub weight: u32,
}

/// 代理类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProxyType {
    Http,
    Https,
    Socks4,
    Socks5,
}

/// 代理轮换策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProxyRotationStrategy {
    /// 随机选择
    Random,
    /// 轮询
    RoundRobin,
    /// 加权轮询
    WeightedRoundRobin,
    /// 最少连接
    LeastConnections,
}

/// 浏览器伪装配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserConfig {
    /// 用户代理列表
    pub user_agents: Vec<String>,
    /// 是否随机选择UA
    pub random_user_agent: bool,
    /// 默认请求头
    pub default_headers: HashMap<String, String>,
    /// 是否模拟浏览器行为
    pub simulate_browser: bool,
    /// 请求间隔（毫秒）
    pub request_delay_ms: u64,
}

impl Default for RecursiveConfig {
    fn default() -> Self {
        RecursiveConfig {
            enabled: true,
            max_depth: 3,
            max_domains: 1000,
            trigger: TriggerConfig::default(),
            filter: FilterConfig::default(),
            cache: CacheConfig::default(),
            proxy: ProxyConfig::default(),
            browser: BrowserConfig::default(),
        }
    }
}

impl Default for TriggerConfig {
    fn default() -> Self {
        Self {
            status_codes: vec![200, 301, 302, 307, 308],
            content_types: vec![
                "text/html".to_string(),
                "application/json".to_string(),
                "application/xml".to_string(),
                "text/xml".to_string(),
            ],
            follow_redirects: true,
            max_redirects: 10,
        }
    }
}

impl Default for FilterConfig {
    fn default() -> Self {
        Self {
            keyword_blacklist: vec![
                "admin".to_string(),
                "login".to_string(),
                "auth".to_string(),
                "private".to_string(),
            ],
            regex_blacklist: vec![
                r".*\.exe$".to_string(),
                r".*\.zip$".to_string(),
                r".*\.pdf$".to_string(),
            ],
            country_blacklist: vec![".cn".to_string(), ".ru".to_string(), ".ir".to_string()],
            famous_sites_blacklist: vec![
                "google.com".to_string(),
                "facebook.com".to_string(),
                "youtube.com".to_string(),
                "amazon.com".to_string(),
                "microsoft.com".to_string(),
                "apple.com".to_string(),
                "twitter.com".to_string(),
                "instagram.com".to_string(),
                "linkedin.com".to_string(),
                "github.com".to_string(),
            ],
            custom_blacklist: vec![],
        }
    }
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_size_mb: 512,
            ttl_seconds: 3600,
            hit_rate_threshold: 0.1,
            cleanup_interval_seconds: 300,
        }
    }
}

impl Default for ProxyConfig {
    fn default() -> Self {
        Self {
            use_proxy: false,
            proxy_servers: vec![],
            rotation_strategy: ProxyRotationStrategy::Random,
            timeout_seconds: 30,
            retry_count: 3,
        }
    }
}

impl Default for BrowserConfig {
    fn default() -> Self {
        let mut default_headers = HashMap::new();
        default_headers.insert(
            "Accept".to_string(),
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
                .to_string(),
        );
        default_headers.insert("Accept-Language".to_string(), "en-US,en;q=0.5".to_string());
        default_headers.insert(
            "Accept-Encoding".to_string(),
            "gzip, deflate, br".to_string(),
        );
        default_headers.insert("DNT".to_string(), "1".to_string());
        default_headers.insert("Connection".to_string(), "keep-alive".to_string());
        default_headers.insert("Upgrade-Insecure-Requests".to_string(), "1".to_string());

        Self {
            user_agents: vec![
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36".to_string(),
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36".to_string(),
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36".to_string(),
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0".to_string(),
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0".to_string(),
            ],
            random_user_agent: true,
            default_headers,
            simulate_browser: true,
            request_delay_ms: 1000,
        }
    }
}
