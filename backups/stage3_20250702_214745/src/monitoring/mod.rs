//! 性能监控模块 - 不影响现有功能，只添加监控能力
//!
//! 这个模块完全独立于现有代码，只提供性能监控功能
//! 可以安全地添加到现有系统中，不会影响任何现有功能

use serde::{Deserialize, Serialize};
use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};

/// 性能监控器 - 与现有代码完全独立
pub struct PerformanceMonitor {
    // 事件系统监控
    event_add_count: AtomicU64,
    event_add_total_time: AtomicU64,
    event_query_count: AtomicU64,
    event_query_total_time: AtomicU64,

    // 缓存系统监控
    cache_hit_count: AtomicU64,
    cache_miss_count: AtomicU64,
    cache_operation_time: AtomicU64,

    // 域名处理监控
    domain_extract_count: AtomicU64,
    domain_extract_total_time: AtomicU64,
    domain_replace_count: AtomicU64,
    domain_replace_total_time: AtomicU64,

    // 内存监控
    memory_usage: AtomicUsize,
    memory_peak: AtomicUsize,

    // 锁竞争监控
    lock_contention_count: AtomicU64,
    lock_wait_total_time: AtomicU64,
}

impl PerformanceMonitor {
    pub fn new() -> Arc<Self> {
        Arc::new(Self {
            event_add_count: AtomicU64::new(0),
            event_add_total_time: AtomicU64::new(0),
            event_query_count: AtomicU64::new(0),
            event_query_total_time: AtomicU64::new(0),
            cache_hit_count: AtomicU64::new(0),
            cache_miss_count: AtomicU64::new(0),
            cache_operation_time: AtomicU64::new(0),
            domain_extract_count: AtomicU64::new(0),
            domain_extract_total_time: AtomicU64::new(0),
            domain_replace_count: AtomicU64::new(0),
            domain_replace_total_time: AtomicU64::new(0),
            memory_usage: AtomicUsize::new(0),
            memory_peak: AtomicUsize::new(0),
            lock_contention_count: AtomicU64::new(0),
            lock_wait_total_time: AtomicU64::new(0),
        })
    }

    // 事件系统监控方法
    pub fn record_event_add(&self, duration: Duration) {
        self.event_add_count.fetch_add(1, Ordering::Relaxed);
        self.event_add_total_time
            .fetch_add(duration.as_micros() as u64, Ordering::Relaxed);
    }

    pub fn record_event_query(&self, duration: Duration) {
        self.event_query_count.fetch_add(1, Ordering::Relaxed);
        self.event_query_total_time
            .fetch_add(duration.as_micros() as u64, Ordering::Relaxed);
    }

    // 缓存系统监控方法
    pub fn record_cache_hit(&self, duration: Duration) {
        self.cache_hit_count.fetch_add(1, Ordering::Relaxed);
        self.cache_operation_time
            .fetch_add(duration.as_micros() as u64, Ordering::Relaxed);
    }

    pub fn record_cache_miss(&self, duration: Duration) {
        self.cache_miss_count.fetch_add(1, Ordering::Relaxed);
        self.cache_operation_time
            .fetch_add(duration.as_micros() as u64, Ordering::Relaxed);
    }

    // 域名处理监控方法
    pub fn record_domain_extract(&self, duration: Duration) {
        self.domain_extract_count.fetch_add(1, Ordering::Relaxed);
        self.domain_extract_total_time
            .fetch_add(duration.as_micros() as u64, Ordering::Relaxed);
    }

    pub fn record_domain_replace(&self, duration: Duration) {
        self.domain_replace_count.fetch_add(1, Ordering::Relaxed);
        self.domain_replace_total_time
            .fetch_add(duration.as_micros() as u64, Ordering::Relaxed);
    }

    // 内存监控方法
    pub fn update_memory_usage(&self, bytes: usize) {
        self.memory_usage.store(bytes, Ordering::Relaxed);

        // 更新峰值
        let current_peak = self.memory_peak.load(Ordering::Relaxed);
        if bytes > current_peak {
            self.memory_peak.store(bytes, Ordering::Relaxed);
        }
    }

    // 锁竞争监控方法
    pub fn record_lock_contention(&self, wait_time: Duration) {
        self.lock_contention_count.fetch_add(1, Ordering::Relaxed);
        self.lock_wait_total_time
            .fetch_add(wait_time.as_micros() as u64, Ordering::Relaxed);
    }

    // 获取性能统计
    pub fn get_stats(&self) -> PerformanceStats {
        let event_add_count = self.event_add_count.load(Ordering::Relaxed);
        let event_add_total_time = self.event_add_total_time.load(Ordering::Relaxed);
        let event_query_count = self.event_query_count.load(Ordering::Relaxed);
        let event_query_total_time = self.event_query_total_time.load(Ordering::Relaxed);

        let cache_hits = self.cache_hit_count.load(Ordering::Relaxed);
        let cache_misses = self.cache_miss_count.load(Ordering::Relaxed);
        let cache_operation_time = self.cache_operation_time.load(Ordering::Relaxed);

        let domain_extract_count = self.domain_extract_count.load(Ordering::Relaxed);
        let domain_extract_total_time = self.domain_extract_total_time.load(Ordering::Relaxed);
        let domain_replace_count = self.domain_replace_count.load(Ordering::Relaxed);
        let domain_replace_total_time = self.domain_replace_total_time.load(Ordering::Relaxed);

        let lock_contention_count = self.lock_contention_count.load(Ordering::Relaxed);
        let lock_wait_total_time = self.lock_wait_total_time.load(Ordering::Relaxed);

        PerformanceStats {
            // 事件系统统计
            event_add_avg_time_us: if event_add_count > 0 {
                event_add_total_time / event_add_count
            } else {
                0
            },
            event_query_avg_time_us: if event_query_count > 0 {
                event_query_total_time / event_query_count
            } else {
                0
            },
            event_total_operations: event_add_count + event_query_count,

            // 缓存系统统计
            cache_hit_rate: if cache_hits + cache_misses > 0 {
                cache_hits as f64 / (cache_hits + cache_misses) as f64
            } else {
                0.0
            },
            cache_avg_operation_time_us: if cache_hits + cache_misses > 0 {
                cache_operation_time / (cache_hits + cache_misses)
            } else {
                0
            },
            cache_total_operations: cache_hits + cache_misses,

            // 域名处理统计
            domain_extract_avg_time_us: if domain_extract_count > 0 {
                domain_extract_total_time / domain_extract_count
            } else {
                0
            },
            domain_replace_avg_time_us: if domain_replace_count > 0 {
                domain_replace_total_time / domain_replace_count
            } else {
                0
            },
            domain_total_operations: domain_extract_count + domain_replace_count,

            // 内存统计
            memory_usage_mb: self.memory_usage.load(Ordering::Relaxed) / 1024 / 1024,
            memory_peak_mb: self.memory_peak.load(Ordering::Relaxed) / 1024 / 1024,

            // 锁竞争统计
            lock_contention_count,
            lock_avg_wait_time_us: if lock_contention_count > 0 {
                lock_wait_total_time / lock_contention_count
            } else {
                0
            },
        }
    }

    // 重置统计信息
    pub fn reset_stats(&self) {
        self.event_add_count.store(0, Ordering::Relaxed);
        self.event_add_total_time.store(0, Ordering::Relaxed);
        self.event_query_count.store(0, Ordering::Relaxed);
        self.event_query_total_time.store(0, Ordering::Relaxed);
        self.cache_hit_count.store(0, Ordering::Relaxed);
        self.cache_miss_count.store(0, Ordering::Relaxed);
        self.cache_operation_time.store(0, Ordering::Relaxed);
        self.domain_extract_count.store(0, Ordering::Relaxed);
        self.domain_extract_total_time.store(0, Ordering::Relaxed);
        self.domain_replace_count.store(0, Ordering::Relaxed);
        self.domain_replace_total_time.store(0, Ordering::Relaxed);
        self.lock_contention_count.store(0, Ordering::Relaxed);
        self.lock_wait_total_time.store(0, Ordering::Relaxed);
        // 注意：不重置内存峰值，因为这是累积统计
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceStats {
    // 事件系统统计
    pub event_add_avg_time_us: u64,
    pub event_query_avg_time_us: u64,
    pub event_total_operations: u64,

    // 缓存系统统计
    pub cache_hit_rate: f64,
    pub cache_avg_operation_time_us: u64,
    pub cache_total_operations: u64,

    // 域名处理统计
    pub domain_extract_avg_time_us: u64,
    pub domain_replace_avg_time_us: u64,
    pub domain_total_operations: u64,

    // 内存统计
    pub memory_usage_mb: usize,
    pub memory_peak_mb: usize,

    // 锁竞争统计
    pub lock_contention_count: u64,
    pub lock_avg_wait_time_us: u64,
}

impl PerformanceStats {
    /// 检查是否有性能问题
    pub fn has_performance_issues(&self) -> Vec<String> {
        let mut issues = Vec::new();

        // 检查事件系统性能
        if self.event_add_avg_time_us > 1000 {
            // 超过1ms
            issues.push(format!(
                "事件添加平均耗时过长: {}μs",
                self.event_add_avg_time_us
            ));
        }

        if self.event_query_avg_time_us > 5000 {
            // 超过5ms
            issues.push(format!(
                "事件查询平均耗时过长: {}μs",
                self.event_query_avg_time_us
            ));
        }

        // 检查缓存性能
        if self.cache_hit_rate < 0.8 && self.cache_total_operations > 100 {
            issues.push(format!(
                "缓存命中率过低: {:.2}%",
                self.cache_hit_rate * 100.0
            ));
        }

        if self.cache_avg_operation_time_us > 100 {
            // 超过100μs
            issues.push(format!(
                "缓存操作平均耗时过长: {}μs",
                self.cache_avg_operation_time_us
            ));
        }

        // 检查域名处理性能
        if self.domain_extract_avg_time_us > 10000 {
            // 超过10ms
            issues.push(format!(
                "域名提取平均耗时过长: {}μs",
                self.domain_extract_avg_time_us
            ));
        }

        if self.domain_replace_avg_time_us > 5000 {
            // 超过5ms
            issues.push(format!(
                "域名替换平均耗时过长: {}μs",
                self.domain_replace_avg_time_us
            ));
        }

        // 检查内存使用
        if self.memory_usage_mb > 1024 {
            // 超过1GB
            issues.push(format!("内存使用过高: {}MB", self.memory_usage_mb));
        }

        // 检查锁竞争
        if self.lock_contention_count > 100 && self.lock_avg_wait_time_us > 1000 {
            issues.push(format!(
                "锁竞争严重: {}次竞争，平均等待{}μs",
                self.lock_contention_count, self.lock_avg_wait_time_us
            ));
        }

        issues
    }
}

/// 性能监控工具函数
pub struct PerformanceTimer {
    start: Instant,
}

impl PerformanceTimer {
    pub fn new() -> Self {
        Self {
            start: Instant::now(),
        }
    }

    pub fn elapsed(&self) -> Duration {
        self.start.elapsed()
    }
}

// 全局监控实例 - 可选使用，不影响现有代码
use std::sync::OnceLock;

static GLOBAL_MONITOR: OnceLock<Arc<PerformanceMonitor>> = OnceLock::new();

pub fn get_global_monitor() -> &'static Arc<PerformanceMonitor> {
    GLOBAL_MONITOR.get_or_init(|| PerformanceMonitor::new())
}

// 便捷的监控宏 - 可选使用
#[macro_export]
macro_rules! monitor_performance {
    ($monitor:expr, $operation:expr, $code:block) => {{
        let timer = $crate::monitoring::PerformanceTimer::new();
        let result = $code;
        $monitor.$operation(timer.elapsed());
        result
    }};
}

// 使用示例（在其他模块中可选使用）:
// use crate::monitoring::{get_global_monitor, monitor_performance};
//
// let result = monitor_performance!(get_global_monitor(), record_event_add, {
//     // 原有的事件添加代码
//     app_state.add_event(event).await
// });
