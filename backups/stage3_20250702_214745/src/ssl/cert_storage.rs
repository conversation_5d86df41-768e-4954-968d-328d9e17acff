use anyhow::{anyhow, Result};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
/// SSL证书文件存储管理
/// 负责证书文件的安全存储、备份和恢复
use std::path::{Path, PathBuf};
use tokio::fs;

/// 证书存储管理器
pub struct CertificateStorage {
    base_dir: PathBuf,
    backup_enabled: bool,
    max_backups: usize,
}

/// 证书文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CertificateFiles {
    pub domain: String,
    pub cert_path: PathBuf,
    pub key_path: PathBuf,
    pub chain_path: Option<PathBuf>,
    pub created_at: DateTime<Utc>,
    pub file_size: u64,
    pub checksum: String,
}

impl CertificateStorage {
    /// 创建新的证书存储管理器
    pub fn new<P: AsRef<Path>>(base_dir: P, backup_enabled: bool, max_backups: usize) -> Self {
        Self {
            base_dir: base_dir.as_ref().to_path_buf(),
            backup_enabled,
            max_backups,
        }
    }

    /// 初始化存储目录
    pub async fn initialize(&self) -> Result<()> {
        // 创建基础目录
        fs::create_dir_all(&self.base_dir).await?;

        // 创建备份目录
        if self.backup_enabled {
            let backup_dir = self.base_dir.join("backups");
            fs::create_dir_all(&backup_dir).await?;
        }

        tracing::info!("证书存储目录初始化完成: {:?}", self.base_dir);
        Ok(())
    }

    /// 保存证书文件
    pub async fn save_certificate(
        &self,
        domain: &str,
        cert_pem: &str,
        key_pem: &str,
        chain_pem: Option<&str>,
    ) -> Result<CertificateFiles> {
        // 创建域名目录
        let domain_dir = self.base_dir.join(domain);
        fs::create_dir_all(&domain_dir).await?;

        // 定义文件路径
        let cert_path = domain_dir.join("cert.pem");
        let key_path = domain_dir.join("key.pem");
        let chain_path = if chain_pem.is_some() {
            Some(domain_dir.join("chain.pem"))
        } else {
            None
        };

        // 备份现有证书（如果存在）
        if self.backup_enabled && cert_path.exists() {
            self.backup_certificate(domain).await?;
        }

        // 写入证书文件
        fs::write(&cert_path, cert_pem).await?;
        fs::write(&key_path, key_pem).await?;

        if let (Some(chain_pem), Some(chain_path)) = (chain_pem, &chain_path) {
            fs::write(chain_path, chain_pem).await?;
        }

        // 设置文件权限（仅所有者可读写）
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = fs::metadata(&key_path).await?.permissions();
            perms.set_mode(0o600); // 仅所有者可读写
            fs::set_permissions(&key_path, perms).await?;
        }

        // 计算文件信息
        let cert_metadata = fs::metadata(&cert_path).await?;
        let checksum = self.calculate_checksum(&cert_path).await?;

        let cert_files = CertificateFiles {
            domain: domain.to_string(),
            cert_path,
            key_path,
            chain_path,
            created_at: Utc::now(),
            file_size: cert_metadata.len(),
            checksum,
        };

        tracing::info!("证书文件保存成功: {}", domain);
        Ok(cert_files)
    }

    /// 加载证书文件
    pub async fn load_certificate(&self, domain: &str) -> Result<(String, String, Option<String>)> {
        let domain_dir = self.base_dir.join(domain);

        if !domain_dir.exists() {
            return Err(anyhow!("证书目录不存在: {}", domain));
        }

        let cert_path = domain_dir.join("cert.pem");
        let key_path = domain_dir.join("key.pem");
        let chain_path = domain_dir.join("chain.pem");

        // 检查必需文件是否存在
        if !cert_path.exists() || !key_path.exists() {
            return Err(anyhow!("证书文件不完整: {}", domain));
        }

        // 读取文件内容
        let cert_pem = fs::read_to_string(&cert_path).await?;
        let key_pem = fs::read_to_string(&key_path).await?;
        let chain_pem = if chain_path.exists() {
            Some(fs::read_to_string(&chain_path).await?)
        } else {
            None
        };

        tracing::debug!("证书文件加载成功: {}", domain);
        Ok((cert_pem, key_pem, chain_pem))
    }

    /// 删除证书文件
    pub async fn delete_certificate(&self, domain: &str) -> Result<()> {
        let domain_dir = self.base_dir.join(domain);

        if domain_dir.exists() {
            // 备份后删除
            if self.backup_enabled {
                self.backup_certificate(domain).await?;
            }

            fs::remove_dir_all(&domain_dir).await?;
            tracing::info!("证书文件删除成功: {}", domain);
        }

        Ok(())
    }

    /// 备份证书
    async fn backup_certificate(&self, domain: &str) -> Result<()> {
        let domain_dir = self.base_dir.join(domain);
        let backup_dir = self.base_dir.join("backups").join(domain);

        if !domain_dir.exists() {
            return Ok(());
        }

        // 创建备份目录
        fs::create_dir_all(&backup_dir).await?;

        // 生成备份文件名（包含时间戳）
        let timestamp = Utc::now().format("%Y%m%d_%H%M%S");
        let backup_subdir = backup_dir.join(format!("backup_{}", timestamp));
        fs::create_dir_all(&backup_subdir).await?;

        // 复制证书文件
        let files = ["cert.pem", "key.pem", "chain.pem"];
        for file in &files {
            let src = domain_dir.join(file);
            let dst = backup_subdir.join(file);

            if src.exists() {
                fs::copy(&src, &dst).await?;
            }
        }

        // 清理旧备份
        self.cleanup_old_backups(domain).await?;

        tracing::info!("证书备份完成: {} -> {:?}", domain, backup_subdir);
        Ok(())
    }

    /// 清理旧备份
    async fn cleanup_old_backups(&self, domain: &str) -> Result<()> {
        let backup_dir = self.base_dir.join("backups").join(domain);

        if !backup_dir.exists() {
            return Ok(());
        }

        // 获取所有备份目录
        let mut entries = fs::read_dir(&backup_dir).await?;
        let mut backup_dirs = Vec::new();

        while let Some(entry) = entries.next_entry().await? {
            if entry.file_type().await?.is_dir() {
                if let Some(name) = entry.file_name().to_str() {
                    if name.starts_with("backup_") {
                        backup_dirs.push((entry.path(), name.to_string()));
                    }
                }
            }
        }

        // 按名称排序（时间戳排序）
        backup_dirs.sort_by(|a, b| b.1.cmp(&a.1));

        // 删除超出限制的备份
        if backup_dirs.len() > self.max_backups {
            for (path, _) in backup_dirs.iter().skip(self.max_backups) {
                fs::remove_dir_all(path).await?;
                tracing::debug!("删除旧备份: {:?}", path);
            }
        }

        Ok(())
    }

    /// 恢复证书备份
    pub async fn restore_certificate(&self, domain: &str, backup_timestamp: &str) -> Result<()> {
        let backup_dir = self
            .base_dir
            .join("backups")
            .join(domain)
            .join(format!("backup_{}", backup_timestamp));

        if !backup_dir.exists() {
            return Err(anyhow!("备份不存在: {}", backup_timestamp));
        }

        let domain_dir = self.base_dir.join(domain);
        fs::create_dir_all(&domain_dir).await?;

        // 恢复证书文件
        let files = ["cert.pem", "key.pem", "chain.pem"];
        for file in &files {
            let src = backup_dir.join(file);
            let dst = domain_dir.join(file);

            if src.exists() {
                fs::copy(&src, &dst).await?;
            }
        }

        tracing::info!("证书恢复完成: {} <- {}", domain, backup_timestamp);
        Ok(())
    }

    /// 列出可用备份
    pub async fn list_backups(&self, domain: &str) -> Result<Vec<String>> {
        let backup_dir = self.base_dir.join("backups").join(domain);

        if !backup_dir.exists() {
            return Ok(Vec::new());
        }

        let mut entries = fs::read_dir(&backup_dir).await?;
        let mut backups = Vec::new();

        while let Some(entry) = entries.next_entry().await? {
            if entry.file_type().await?.is_dir() {
                if let Some(name) = entry.file_name().to_str() {
                    if let Some(timestamp) = name.strip_prefix("backup_") {
                        backups.push(timestamp.to_string());
                    }
                }
            }
        }

        backups.sort();
        backups.reverse(); // 最新的在前

        Ok(backups)
    }

    /// 验证证书文件完整性
    pub async fn verify_certificate(&self, domain: &str) -> Result<bool> {
        let domain_dir = self.base_dir.join(domain);
        let cert_path = domain_dir.join("cert.pem");
        let key_path = domain_dir.join("key.pem");

        // 检查文件是否存在
        if !cert_path.exists() || !key_path.exists() {
            return Ok(false);
        }

        // 尝试读取和解析证书
        let cert_pem = fs::read_to_string(&cert_path).await?;
        let key_pem = fs::read_to_string(&key_path).await?;

        // 基本格式验证
        if !cert_pem.contains("-----BEGIN CERTIFICATE-----")
            || !key_pem.contains("-----BEGIN PRIVATE KEY-----")
        {
            return Ok(false);
        }

        Ok(true)
    }

    /// 计算文件校验和
    async fn calculate_checksum(&self, file_path: &Path) -> Result<String> {
        use sha2::{Digest, Sha256};

        let content = fs::read(file_path).await?;
        let mut hasher = Sha256::new();
        hasher.update(&content);
        let result = hasher.finalize();

        Ok(format!("{:x}", result))
    }

    /// 获取证书文件信息
    pub async fn get_certificate_info(&self, domain: &str) -> Result<Option<CertificateFiles>> {
        let domain_dir = self.base_dir.join(domain);
        let cert_path = domain_dir.join("cert.pem");
        let key_path = domain_dir.join("key.pem");
        let chain_path = domain_dir.join("chain.pem");

        if !cert_path.exists() || !key_path.exists() {
            return Ok(None);
        }

        let cert_metadata = fs::metadata(&cert_path).await?;
        let checksum = self.calculate_checksum(&cert_path).await?;

        let cert_files = CertificateFiles {
            domain: domain.to_string(),
            cert_path,
            key_path,
            chain_path: if chain_path.exists() {
                Some(chain_path)
            } else {
                None
            },
            created_at: DateTime::from_timestamp(
                cert_metadata
                    .modified()?
                    .duration_since(std::time::UNIX_EPOCH)?
                    .as_secs() as i64,
                0,
            )
            .unwrap_or_else(Utc::now),
            file_size: cert_metadata.len(),
            checksum,
        };

        Ok(Some(cert_files))
    }
}
