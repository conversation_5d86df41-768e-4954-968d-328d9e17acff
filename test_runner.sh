#!/bin/bash

# SM智能代理系统 - 全自动测试脚本
# 用于全面测试后端代码是否按需求工作

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查测试依赖..."
    
    # 检查Rust和Cargo
    if ! command -v cargo &> /dev/null; then
        log_error "Cargo未安装，请先安装Rust"
        exit 1
    fi
    
    # 检查MongoDB（如果需要）
    if command -v mongod &> /dev/null; then
        log_success "MongoDB已安装"
    else
        log_warning "MongoDB未安装，数据库测试可能失败"
    fi
    
    log_success "依赖检查完成"
}

# 编译项目
build_project() {
    log_info "编译项目..."
    
    if cargo build --release; then
        log_success "项目编译成功"
    else
        log_error "项目编译失败"
        exit 1
    fi
}

# 启动服务（后台）
start_service() {
    log_info "启动SM智能代理服务..."
    
    # 检查端口是否被占用
    if lsof -Pi :1319 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口1319已被占用，尝试停止现有服务..."
        pkill -f "sm" || true
        sleep 2
    fi
    
    # 启动服务
    RUST_LOG=info ./target/release/sm &
    SERVICE_PID=$!
    
    # 等待服务启动
    log_info "等待服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:1319/api/status >/dev/null 2>&1; then
            log_success "服务启动成功 (PID: $SERVICE_PID)"
            return 0
        fi
        sleep 1
    done
    
    log_error "服务启动失败或超时"
    kill $SERVICE_PID 2>/dev/null || true
    exit 1
}

# 运行测试
run_tests() {
    log_info "运行全自动测试套件..."
    
    # 运行集成测试
    if cargo test --test integration_tests -- --nocapture; then
        log_success "集成测试通过"
    else
        log_error "集成测试失败"
        return 1
    fi
    
    # 运行单元测试
    log_info "运行单元测试..."
    if cargo test --lib -- --nocapture; then
        log_success "单元测试通过"
    else
        log_warning "单元测试有失败项"
    fi
    
    return 0
}

# 功能测试
test_core_features() {
    log_info "测试核心功能..."
    
    # 1. 测试API接口
    log_info "测试API接口..."
    
    # 健康检查
    if curl -s -f http://localhost:1319/api/status >/dev/null; then
        log_success "✅ API健康检查通过"
    else
        log_error "❌ API健康检查失败"
    fi
    
    # 自动代理配置
    if curl -s http://localhost:1319/api/auto-proxy/config >/dev/null; then
        log_success "✅ 自动代理配置接口正常"
    else
        log_warning "⚠️ 自动代理配置接口需要认证"
    fi
    
    # SSL证书接口
    if curl -s http://localhost:1319/api/ssl/certificates >/dev/null; then
        log_success "✅ SSL证书接口正常"
    else
        log_warning "⚠️ SSL证书接口需要认证"
    fi
    
    # 2. 测试静态文件服务
    log_info "测试静态文件服务..."
    
    # 测试允许的文件类型
    allowed_files=("index.html" "style.css" "script.js" "favicon.ico" "image.png" "logo.jpg" "icon.svg")
    for file in "${allowed_files[@]}"; do
        response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:1319/$file)
        if [[ "$response" == "200" || "$response" == "404" ]]; then
            log_success "✅ 允许访问: $file"
        else
            log_warning "⚠️ 文件访问异常: $file (状态码: $response)"
        fi
    done
    
    # 测试禁止的文件类型
    forbidden_files=("setup.sh" "config.yaml" "Cargo.toml" ".env" "src/main.rs")
    for file in "${forbidden_files[@]}"; do
        response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:1319/$file)
        if [[ "$response" == "403" || "$response" == "404" ]]; then
            log_success "✅ 成功阻止: $file"
        else
            log_error "❌ 安全漏洞: $file 可以访问 (状态码: $response)"
        fi
    done
    
    # 3. 测试路径遍历防护
    log_info "测试路径遍历防护..."
    
    dangerous_paths=("/../../../etc/passwd" "/../../config.yaml" "/../setup.sh" "/./src/main.rs")
    for path in "${dangerous_paths[@]}"; do
        response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:1319$path)
        if [[ "$response" == "403" || "$response" == "404" ]]; then
            log_success "✅ 成功阻止路径遍历: $path"
        else
            log_error "❌ 路径遍历漏洞: $path (状态码: $response)"
        fi
    done
}

# 性能测试
test_performance() {
    log_info "运行性能测试..."
    
    # 并发请求测试
    log_info "测试并发处理能力..."
    
    # 使用ab工具进行压力测试（如果可用）
    if command -v ab &> /dev/null; then
        log_info "使用Apache Bench进行压力测试..."
        ab -n 100 -c 10 http://localhost:1319/api/status > /tmp/ab_test.log 2>&1
        
        if grep -q "Failed requests:        0" /tmp/ab_test.log; then
            log_success "✅ 压力测试通过 (100请求, 10并发)"
        else
            log_warning "⚠️ 压力测试有失败请求"
        fi
    else
        log_warning "Apache Bench未安装，跳过压力测试"
    fi
    
    # 简单的并发测试
    log_info "运行简单并发测试..."
    for i in {1..10}; do
        curl -s http://localhost:1319/api/status >/dev/null &
    done
    wait
    log_success "✅ 并发测试完成"
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    
    # 停止服务
    if [[ -n "$SERVICE_PID" ]]; then
        kill $SERVICE_PID 2>/dev/null || true
        log_info "服务已停止"
    fi
    
    # 清理临时文件
    rm -f /tmp/ab_test.log
    
    log_success "清理完成"
}

# 主函数
main() {
    echo "🚀 SM智能代理系统 - 全自动测试开始"
    echo "========================================"
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行测试步骤
    check_dependencies
    build_project
    start_service
    
    # 等待服务完全启动
    sleep 3
    
    # 运行测试
    test_success=true
    
    if ! run_tests; then
        test_success=false
    fi
    
    test_core_features
    test_performance
    
    echo "========================================"
    if $test_success; then
        log_success "🎉 所有测试完成！系统按需求正常工作！"
        exit 0
    else
        log_error "❌ 部分测试失败，请检查系统配置"
        exit 1
    fi
}

# 运行主函数
main "$@"
