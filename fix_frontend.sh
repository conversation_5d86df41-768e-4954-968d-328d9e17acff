#!/bin/bash

# 修复前端页面的认证和导航栏问题

echo "🔧 修复前端页面..."

# 检查哪些HTML页面缺少auth.js
echo "检查缺少认证脚本的页面..."

for file in frontend/*.html; do
    if [ -f "$file" ] && [ "$(basename "$file")" != "index.html" ]; then
        if ! grep -q "js/auth.js" "$file"; then
            echo "❌ $file 缺少认证脚本"
            
            # 在最后一个script标签前添加auth.js
            if grep -q "<script" "$file"; then
                # 找到最后一个script标签的位置，在它前面插入auth.js
                sed -i '/<script.*js\/.*\.js/i\    <script src="js/auth.js"></script>' "$file"
                echo "✅ 已添加认证脚本到 $file"
            fi
        else
            echo "✅ $file 已有认证脚本"
        fi
    fi
done

echo "🎉 前端修复完成！"
