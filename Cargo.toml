[workspace]
members = [
    "crates/proxy-core",
    "crates/proxy-config",
    "crates/proxy-cache",
    "crates/proxy-types"
]

# 工作空间依赖管理 - 统一版本
[workspace.dependencies]
# 核心运行时
tokio = { version = "1.35", features = ["rt-multi-thread", "net", "fs", "time", "macros", "signal"] }
anyhow = "1.0"
thiserror = "1.0"
async-trait = "0.1"

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# Pingora 组件
pingora = { version = "0.5", default-features = false }
pingora-core = { version = "0.5", default-features = false }
pingora-proxy = { version = "0.5", default-features = false }
pingora-http = { version = "0.5", default-features = false }
pingora-load-balancing = { version = "0.5", default-features = false }

# Web 框架
axum = { version = "0.7", default-features = false }
axum-extra = "0.9"
tower = "0.4"
tower-http = { version = "0.5", default-features = false }

# 时间和并发
chrono = { version = "0.4", features = ["serde"], default-features = false }
parking_lot = "0.12"
once_cell = "1.19"

# 工具
regex = "1.10"
uuid = { version = "1.6", features = ["v4"] }
base64 = "0.22"
url = "2.5"
bytes = "1.5"

# SSL/TLS 和 ACME
rustls-acme = { version = "0.10", features = ["axum"] }
rustls-pemfile = "2.0"
publicsuffix = "2.2"

# HTML 解析
html5ever = "0.26"
markup5ever_rcdom = "0.2"

# DNS 解析
trust-dns-resolver = "0.23"

# 加密和签名
hmac = "0.12"
sha1 = "0.10"
sha2 = "0.10"

# URL编码
urlencoding = "2.1"

# X.509证书解析
x509-parser = "0.16"

# ACME相关
rcgen = "0.12"

# 日志
tracing = { version = "0.1", default-features = false, features = ["std", "attributes"] }
tracing-subscriber = { version = "0.3", default-features = false, features = ["env-filter", "fmt"] }

# 其他常用依赖
rand = "0.8"
futures = "0.3"
futures-util = "0.3"
bson = "2.8"
http-body-util = "0.1.3"
bincode = "2.0.1"

[package]
name = "sm-simple"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "sm"
path = "src/main.rs"

[dependencies]
# 使用工作空间统一版本
anyhow = { workspace = true }
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
async-trait = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

# Pingora反向代理核心
pingora = { workspace = true }
pingora-core = { workspace = true }
pingora-proxy = { workspace = true }
# 移除负载均衡依赖

# Web框架 - 管理界面
axum = { workspace = true, features = ["http1", "json", "tokio", "macros", "form", "query", "ws"] }
tower = { workspace = true, features = ["util"] }
tower-http = { workspace = true, features = ["fs", "cors", "trace"] }

# 本地crates
proxy-core = { path = "crates/proxy-core" }
proxy-config = { path = "crates/proxy-config" }
proxy-cache = { path = "crates/proxy-cache" }

# 安全认证
jsonwebtoken = "9.2"
bcrypt = "0.15"

# 数据库 - 可选配置（测试用文件存储，生产用MongoDB）
mongodb = { version = "2.8", default-features = false, features = ["tokio-runtime", "bson-chrono-0_4"], optional = true }

# HTTP客户端 - 递归代理必需 (统一使用reqwest)
reqwest = { version = "0.11", features = ["json", "rustls-tls"], default-features = false }

# SSL证书自动申请
rustls-acme = { workspace = true }
rustls-pemfile = { workspace = true }
publicsuffix = { workspace = true }
rcgen = { workspace = true }
x509-parser = { workspace = true }

# 加密和签名
hmac = { workspace = true }
sha1 = { workspace = true }
sha2 = { workspace = true }

# URL编码
urlencoding = { workspace = true }

# HTML内容解析
html5ever = { workspace = true }
markup5ever_rcdom = { workspace = true }

# DNS解析
trust-dns-resolver = { workspace = true }

# 系统信息 - status API使用 (设为可选)
hostname = { version = "0.3", optional = true }
num_cpus = { version = "1.16", optional = true }
local_ipaddress = { version = "0.1", optional = true }
sys-info = { version = "0.9", optional = true }

# 安全和加密 - security模块使用 (使用工作空间版本)
hex = { version = "0.4", optional = true }
base64 = { workspace = true }

# Security模块依赖 (使用工作空间版本)
uuid = { workspace = true }
parking_lot = { workspace = true }
once_cell = { workspace = true }
regex = { workspace = true }

# 核心依赖 (使用工作空间版本)
rand = { workspace = true }
futures = { workspace = true }
bson = { workspace = true }
serde_yaml = { workspace = true }
bytes = { workspace = true }
url = { workspace = true }
futures-util = { workspace = true }

# TLS相关 - 设为可选 (只在需要客户端证书时使用)
zeroize = { version = "1.7", optional = true }

# 时间处理 - 使用工作空间版本
chrono = { workspace = true }

# 其他必要依赖
http-body-util = { workspace = true }
bincode = { workspace = true }

# 功能特性配置
[features]
default = ["mongodb-storage", "system-info"]  # 默认使用MongoDB + 系统信息
file-storage = []                              # 文件存储模式（轻量级测试）
mongodb-storage = ["dep:mongodb"]              # MongoDB存储模式（生产环境）

# 可选功能模块
system-info = ["hostname", "num_cpus", "local_ipaddress", "sys-info"]  # 系统信息API
security-extra = ["hex", "zeroize"]                                   # 额外安全功能
tls-client = []                                                        # TLS客户端证书支持

# 预设配置
minimal = ["mongodb-storage"]                                           # 最小化配置
production = ["mongodb-storage", "system-info", "security-extra"]      # 生产环境完整功能
full = ["mongodb-storage", "system-info", "security-extra", "tls-client"]  # 所有功能

# 编译优化配置
[profile.dev]
# 减少调试信息以加速编译
debug = 1
# 启用增量编译
incremental = true
# 减少优化级别
opt-level = 0

[profile.dev.package."*"]
# 对依赖包进行基本优化
opt-level = 1
