CMAKE_TOOLCHAIN_FILE_x86_64-unknown-linux-gnu = None
CMAKE_TOOLCHAIN_FILE_x86_64_unknown_linux_gnu = None
HOST_CMAKE_TOOLCHAIN_FILE = None
CMAKE_TOOLCHAIN_FILE = None
CMAKE_GENERATOR_x86_64-unknown-linux-gnu = None
CMAKE_GENERATOR_x86_64_unknown_linux_gnu = None
HOST_CMAKE_GENERATOR = None
CMAKE_GENERATOR = None
CMAKE_PREFIX_PATH_x86_64-unknown-linux-gnu = None
CMAKE_PREFIX_PATH_x86_64_unknown_linux_gnu = None
HOST_CMAKE_PREFIX_PATH = None
CMAKE_PREFIX_PATH = None
CMAKE_x86_64-unknown-linux-gnu = None
CMAKE_x86_64_unknown_linux_gnu = None
HOST_CMAKE = None
CMAKE = None
running: cd "/home/<USER>/\xe6\xa1\x8c\xe9\x9d\xa2/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/build" && CMAKE_PREFIX_PATH="" LC_ALL="C" "cmake" "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/libz-ng-sys-1.1.22/src/zlib-ng" "-DBUILD_SHARED_LIBS=OFF" "-DZLIB_COMPAT=OFF" "-DZLIB_ENABLE_TESTS=OFF" "-DWITH_GZFILEOP=ON" "-DCMAKE_INSTALL_LIBDIR=lib" "-DCMAKE_INSTALL_PREFIX=/home/<USER>/\xe6\xa1\x8c\xe9\x9d\xa2/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out" "-DCMAKE_C_FLAGS= -ffunction-sections -fdata-sections -fPIC -m64" "-DCMAKE_C_COMPILER=/usr/bin/cc" "-DCMAKE_CXX_FLAGS= -ffunction-sections -fdata-sections -fPIC -m64" "-DCMAKE_CXX_COMPILER=/usr/bin/c++" "-DCMAKE_ASM_FLAGS= -ffunction-sections -fdata-sections -fPIC -m64" "-DCMAKE_ASM_COMPILER=/usr/bin/cc" "-DCMAKE_BUILD_TYPE=RelWithDebInfo"
-- Using CMake version 3.28.3
-- ZLIB_HEADER_VERSION: 1.3.1
-- ZLIBNG_HEADER_VERSION: 2.2.4
-- The C compiler identification is GNU 13.3.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Arch detected: 'x86_64'
-- Basearch of 'x86_64' has been detected as: 'x86'
-- Performing Test FNO_LTO_AVAILABLE
-- Performing Test FNO_LTO_AVAILABLE - Success
-- Looking for arm_acle.h
-- Looking for arm_acle.h - not found
-- Looking for sys/auxv.h
-- Looking for sys/auxv.h - found
-- Looking for sys/sdt.h
-- Looking for sys/sdt.h - not found
-- Looking for unistd.h
-- Looking for unistd.h - found
-- Looking for linux/auxvec.h
-- Looking for linux/auxvec.h - found
-- Looking for sys/types.h
-- Looking for sys/types.h - found
-- Looking for stdint.h
-- Looking for stdint.h - found
-- Looking for stddef.h
-- Looking for stddef.h - found
-- Check size of off64_t
-- Check size of off64_t - done
-- Looking for fseeko
-- Looking for fseeko - found
-- Looking for strerror
-- Looking for strerror - found
-- Looking for posix_memalign
-- Looking for posix_memalign - found
-- Looking for aligned_alloc
-- Looking for aligned_alloc - found
-- Performing Test HAVE_NO_INTERPOSITION
-- Performing Test HAVE_NO_INTERPOSITION - Success
-- Performing Test HAVE_ATTRIBUTE_VISIBILITY_HIDDEN
-- Performing Test HAVE_ATTRIBUTE_VISIBILITY_HIDDEN - Success
-- Performing Test HAVE_ATTRIBUTE_VISIBILITY_INTERNAL
-- Performing Test HAVE_ATTRIBUTE_VISIBILITY_INTERNAL - Success
-- Performing Test HAVE_ATTRIBUTE_ALIGNED
-- Performing Test HAVE_ATTRIBUTE_ALIGNED - Success
-- Performing Test HAVE_BUILTIN_ASSUME_ALIGNED
-- Performing Test HAVE_BUILTIN_ASSUME_ALIGNED - Success
-- Performing Test HAVE_BUILTIN_CTZ
-- Performing Test HAVE_BUILTIN_CTZ - Success
-- Performing Test HAVE_BUILTIN_CTZLL
-- Performing Test HAVE_BUILTIN_CTZLL - Success
-- Performing Test HAVE_PTRDIFF_T
-- Performing Test HAVE_PTRDIFF_T - Success
-- Performing Test HAVE_XSAVE_INTRIN
-- Performing Test HAVE_XSAVE_INTRIN - Success
-- Performing Test HAVE_SSE2_INTRIN
-- Performing Test HAVE_SSE2_INTRIN - Success
-- Performing Test HAVE_SSSE3_INTRIN
-- Performing Test HAVE_SSSE3_INTRIN - Success
-- Performing Test HAVE_SSE42_INTRIN
-- Performing Test HAVE_SSE42_INTRIN - Success
-- Performing Test HAVE_PCLMULQDQ_INTRIN
-- Performing Test HAVE_PCLMULQDQ_INTRIN - Success
-- Performing Test HAVE_AVX2_INTRIN
-- Performing Test HAVE_AVX2_INTRIN - Success
-- Performing Test HAVE_CASCADE_LAKE
-- Performing Test HAVE_CASCADE_LAKE - Success
-- Performing Test HAVE_AVX512_INTRIN
-- Performing Test HAVE_AVX512_INTRIN - Success
-- Performing Test HAVE_AVX512VNNI_INTRIN
-- Performing Test HAVE_AVX512VNNI_INTRIN - Success
-- Performing Test HAVE_VPCLMULQDQ_INTRIN
-- Performing Test HAVE_VPCLMULQDQ_INTRIN - Success
-- Architecture-specific source files: arch/x86/x86_features.c;arch/x86/chunkset_sse2.c;arch/x86/compare256_sse2.c;arch/x86/slide_hash_sse2.c;arch/x86/adler32_ssse3.c;arch/x86/chunkset_ssse3.c;arch/x86/adler32_sse42.c;arch/x86/crc32_pclmulqdq.c;arch/x86/slide_hash_avx2.c;arch/x86/chunkset_avx2.c;arch/x86/compare256_avx2.c;arch/x86/adler32_avx2.c;arch/x86/adler32_avx512.c;arch/x86/chunkset_avx512.c;arch/x86/adler32_avx512_vnni.c;arch/x86/crc32_vpclmulqdq.c
-- The following features have been enabled:

 * CMAKE_BUILD_TYPE, Build type: RelWithDebInfo (selected)
 * XSAVE, Support XSAVE intrinsics using "-mxsave"
 * SSSE3_ADLER32, Support SSSE3-accelerated adler32, using "-mssse3"
 * SSE42_CRC, Support SSE4.2 optimized adler32 hash generation, using "-msse4.2"
 * PCLMUL_CRC, Support CRC hash generation using PCLMULQDQ, using "-msse4.2 -mpclmul"
 * AVX2_SLIDEHASH, Support AVX2 optimized slide_hash, using "-mavx2 -mbmi2"
 * AVX2_CHUNKSET, Support AVX2 optimized chunkset, using "-mavx2 -mbmi2"
 * AVX2_COMPARE256, Support AVX2 optimized compare256, using "-mavx2 -mbmi2"
 * AVX2_ADLER32, Support AVX2-accelerated adler32, using "-mavx2 -mbmi2"
 * AVX512_ADLER32, Support AVX512-accelerated adler32, using "-mavx512f -mavx512dq -mavx512bw -mavx512vl -mbmi2 -mtune=cascadelake"
 * AVX512_CHUNKSET, Support AVX512 optimized chunkset, using "-mavx512f -mavx512dq -mavx512bw -mavx512vl -mbmi2 -mtune=cascadelake"
 * AVX512VNNI_ADLER32, Support AVX512VNNI adler32, using "-mavx512f -mavx512dq -mavx512bw -mavx512vl -mavx512vnni -mbmi2 -mtune=cascadelake"
 * VPCLMUL_CRC, Support CRC hash generation using VPCLMULQDQ, using "-mpclmul -mvpclmulqdq -mavx512f -mavx512f -mavx512dq -mavx512bw -mavx512vl -mbmi2 -mtune=cascadelake"
 * WITH_GZFILEOP, Compile with support for gzFile related functions
 * ZLIBNG_ENABLE_TESTS, Test zlib-ng specific API
 * WITH_SANITIZER, Enable sanitizer support
 * WITH_GTEST, Build gtest_zlib
 * WITH_OPTIM, Build with optimisation
 * WITH_NEW_STRATEGIES, Use new strategies
 * WITH_RUNTIME_CPU_DETECTION, Build with runtime CPU detection
 * WITH_AVX2, Build with AVX2
 * WITH_AVX512, Build with AVX512
 * WITH_AVX512VNNI, Build with AVX512 VNNI
 * WITH_SSE2, Build with SSE2
 * WITH_SSSE3, Build with SSSE3
 * WITH_SSE42, Build with SSE42
 * WITH_PCLMULQDQ, Build with PCLMULQDQ
 * WITH_VPCLMULQDQ, Build with VPCLMULQDQ

-- The following features have been disabled:

 * ZLIB_SYMBOL_PREFIX, Publicly exported symbols DO NOT have a custom prefix
 * ZLIB_COMPAT, Compile with zlib compatible API
 * ZLIB_ENABLE_TESTS, Build test binaries
 * WITH_FUZZERS, Build test/fuzz
 * WITH_BENCHMARKS, Build test/benchmarks
 * WITH_BENCHMARK_APPS, Build application benchmarks
 * WITH_NATIVE_INSTRUCTIONS, Instruct the compiler to use the full instruction set on this host (gcc/clang -march=native)
 * WITH_MAINTAINER_WARNINGS, Build with project maintainer warnings
 * WITH_CODE_COVERAGE, Enable code coverage reporting
 * WITH_INFLATE_STRICT, Build with strict inflate distance checking
 * WITH_INFLATE_ALLOW_INVALID_DIST, Build with zero fill for inflate invalid distances
 * INSTALL_UTILS, Copy minigzip and minideflate during install

-- Configuring done (10.7s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/build
running: cd "/home/<USER>/\xe6\xa1\x8c\xe9\x9d\xa2/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/build" && LC_ALL="C" MAKEFLAGS="-j --jobserver-fds=8,9 --jobserver-auth=8,9" "cmake" "--build" "/home/<USER>/\xe6\xa1\x8c\xe9\x9d\xa2/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/build" "--target" "install" "--config" "RelWithDebInfo"
[  2%] Building C object CMakeFiles/zlib.dir/arch/generic/adler32_c.c.o
[  4%] Building C object CMakeFiles/zlib.dir/arch/generic/adler32_fold_c.c.o
[  6%] Building C object CMakeFiles/zlib.dir/arch/generic/chunkset_c.c.o
[  8%] Building C object CMakeFiles/zlib.dir/arch/generic/compare256_c.c.o
[ 10%] Building C object CMakeFiles/zlib.dir/arch/generic/crc32_braid_c.c.o
[ 12%] Building C object CMakeFiles/zlib.dir/arch/generic/crc32_fold_c.c.o
[ 14%] Building C object CMakeFiles/zlib.dir/arch/generic/slide_hash_c.c.o
[ 16%] Building C object CMakeFiles/zlib.dir/adler32.c.o
[ 18%] Building C object CMakeFiles/zlib.dir/compress.c.o
[ 20%] Building C object CMakeFiles/zlib.dir/crc32.c.o
[ 22%] Building C object CMakeFiles/zlib.dir/crc32_braid_comb.c.o
[ 24%] Building C object CMakeFiles/zlib.dir/deflate.c.o
[ 26%] Building C object CMakeFiles/zlib.dir/deflate_fast.c.o
[ 28%] Building C object CMakeFiles/zlib.dir/deflate_huff.c.o
[ 30%] Building C object CMakeFiles/zlib.dir/deflate_medium.c.o
[ 32%] Building C object CMakeFiles/zlib.dir/deflate_quick.c.o
[ 34%] Building C object CMakeFiles/zlib.dir/deflate_rle.c.o
[ 36%] Building C object CMakeFiles/zlib.dir/deflate_slow.c.o
[ 38%] Building C object CMakeFiles/zlib.dir/deflate_stored.c.o
[ 40%] Building C object CMakeFiles/zlib.dir/functable.c.o
[ 42%] Building C object CMakeFiles/zlib.dir/infback.c.o
[ 44%] Building C object CMakeFiles/zlib.dir/inflate.c.o
[ 46%] Building C object CMakeFiles/zlib.dir/inftrees.c.o
[ 48%] Building C object CMakeFiles/zlib.dir/insert_string.c.o
[ 51%] Building C object CMakeFiles/zlib.dir/insert_string_roll.c.o
[ 53%] Building C object CMakeFiles/zlib.dir/trees.c.o
[ 55%] Building C object CMakeFiles/zlib.dir/uncompr.c.o
[ 57%] Building C object CMakeFiles/zlib.dir/zutil.c.o
[ 59%] Building C object CMakeFiles/zlib.dir/cpu_features.c.o
[ 61%] Building C object CMakeFiles/zlib.dir/arch/x86/x86_features.c.o
[ 63%] Building C object CMakeFiles/zlib.dir/arch/x86/chunkset_sse2.c.o
[ 65%] Building C object CMakeFiles/zlib.dir/arch/x86/compare256_sse2.c.o
[ 67%] Building C object CMakeFiles/zlib.dir/arch/x86/slide_hash_sse2.c.o
[ 69%] Building C object CMakeFiles/zlib.dir/arch/x86/adler32_ssse3.c.o
[ 71%] Building C object CMakeFiles/zlib.dir/arch/x86/chunkset_ssse3.c.o
[ 73%] Building C object CMakeFiles/zlib.dir/arch/x86/adler32_sse42.c.o
[ 75%] Building C object CMakeFiles/zlib.dir/arch/x86/crc32_pclmulqdq.c.o
[ 77%] Building C object CMakeFiles/zlib.dir/arch/x86/slide_hash_avx2.c.o
[ 79%] Building C object CMakeFiles/zlib.dir/arch/x86/chunkset_avx2.c.o
[ 81%] Building C object CMakeFiles/zlib.dir/arch/x86/compare256_avx2.c.o
[ 83%] Building C object CMakeFiles/zlib.dir/arch/x86/adler32_avx2.c.o
[ 85%] Building C object CMakeFiles/zlib.dir/arch/x86/adler32_avx512.c.o
[ 87%] Building C object CMakeFiles/zlib.dir/arch/x86/chunkset_avx512.c.o
[ 89%] Building C object CMakeFiles/zlib.dir/arch/x86/adler32_avx512_vnni.c.o
[ 91%] Building C object CMakeFiles/zlib.dir/arch/x86/crc32_vpclmulqdq.c.o
[ 93%] Building C object CMakeFiles/zlib.dir/gzlib.c.o
[ 95%] Building C object CMakeFiles/zlib.dir/gzread.c.o
[ 97%] Building C object CMakeFiles/zlib.dir/gzwrite.c.o
[100%] Linking C static library libz-ng.a
[100%] Built target zlib
Install the project...
-- Install configuration: "RelWithDebInfo"
-- Installing: /home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/lib/libz-ng.a
-- Installing: /home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/include/zlib-ng.h
-- Installing: /home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/include/zlib_name_mangling-ng.h
-- Installing: /home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/include/zconf-ng.h
-- Installing: /home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/lib/pkgconfig/zlib-ng.pc
-- Installing: /home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/lib/cmake/zlib-ng/zlib-ng.cmake
-- Installing: /home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/lib/cmake/zlib-ng/zlib-ng-relwithdebinfo.cmake
-- Installing: /home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/lib/cmake/zlib-ng/zlib-ng-config.cmake
-- Installing: /home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/lib/cmake/zlib-ng/zlib-ng-config-version.cmake
cargo:root=/home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out
cargo:rustc-link-search=native=/home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/lib
cargo:rustc-link-lib=static=z-ng
cargo:root=/home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out
cargo:include=/home/<USER>/桌面/sm/target/debug/build/libz-ng-sys-def10a3e8800f26e/out/include
cargo:rustc-cfg=zng
