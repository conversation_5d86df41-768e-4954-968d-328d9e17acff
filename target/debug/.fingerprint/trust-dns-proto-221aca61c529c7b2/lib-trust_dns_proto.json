{"rustc": 15597765236515928571, "features": "[\"default\", \"tokio\", \"tokio-runtime\"]", "declared_features": "[\"backtrace\", \"bytes\", \"default\", \"dns-over-https\", \"dns-over-https-rustls\", \"dns-over-native-tls\", \"dns-over-openssl\", \"dns-over-rustls\", \"dns-over-tls\", \"dnssec\", \"dnssec-openssl\", \"dnssec-ring\", \"h2\", \"http\", \"js-sys\", \"mdns\", \"native-tls\", \"openssl\", \"ring\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde-config\", \"socket2\", \"testing\", \"tokio\", \"tokio-native-tls\", \"tokio-openssl\", \"tokio-runtime\", \"tokio-rustls\", \"wasm-bindgen\", \"wasm-bindgen-crate\", \"webpki\", \"webpki-roots\"]", "target": 492484570920188181, "profile": 17195064474749569637, "path": 15404447282612872260, "deps": [[5103565458935487, "futures_io", false, 5427145575870608023], [95042085696191081, "ipnet", false, 682869334136017672], [99287295355353247, "data_encoding", false, 5728714801875331624], [1042707345065476716, "tinyvec", false, 15006100643507610207], [1811549171721445101, "futures_channel", false, 1453788504570015587], [2828590642173593838, "cfg_if", false, 1571206671589043568], [3150220818285335163, "url", false, 12333196315487844834], [3666196340704888985, "smallvec", false, 18202614195062349763], [5986029879202738730, "log", false, 17126298727032183130], [6852452874680268633, "idna", false, 1659046578870928450], [8008191657135824715, "thiserror", false, 4406955040057758605], [8472643241108742524, "enum_as_inner", false, 50386258748772077], [9538054652646069845, "tokio", false, 15376650133509949099], [10629569228670356391, "futures_util", false, 15535723867050505389], [11946729385090170470, "async_trait", false, 9787621569983403264], [13208667028893622512, "rand", false, 6881440096561656980], [17917672826516349275, "lazy_static", false, 6451159110089736596]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/trust-dns-proto-221aca61c529c7b2/dep-lib-trust_dns_proto", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}