{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"any_tls\", \"boringssl\", \"default\", \"openssl\", \"openssl_derived\", \"rustls\", \"sentry\"]", "target": 15799030865857770792, "profile": 14912514022076944576, "path": 15390576015806691254, "deps": [[418947936956741439, "h2", false, 8994215266854598604], [2706460456408817945, "futures", false, 6102949652763499012], [3722963349756955755, "once_cell", false, 16973236518097757783], [4592814918383864658, "pingora_error", false, 2327944531493750878], [5986029879202738730, "log", false, 7189003991538155933], [9010263965687315507, "http", false, 14207910206936264307], [9451456094439810778, "regex", false, 5903540210641698642], [9538054652646069845, "tokio", false, 16773429822433647056], [10348969647224947607, "pingora_core", false, 17491952783030139821], [11946729385090170470, "async_trait", false, 9787621569983403264], [15128663763258861025, "pingora_cache", false, 16920013334502735557], [15355436635694932780, "clap", false, 3358599763117997012], [16066129441945555748, "bytes", false, 13768183803970146840], [17164677480132756649, "pingora_http", false, 16150250815711804168]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pingora-proxy-d9529582c374280f/dep-lib-pingora_proxy", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}