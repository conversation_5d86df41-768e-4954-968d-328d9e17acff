{"rustc": 15597765236515928571, "features": "[\"bson-chrono-0_4\", \"serde_bytes\", \"tokio-runtime\"]", "declared_features": "[\"async-executor\", \"async-std\", \"async-std-resolver\", \"async-std-runtime\", \"aws-auth\", \"azure-kms\", \"bson-chrono-0_4\", \"bson-serde_with\", \"bson-uuid-0_8\", \"bson-uuid-1\", \"default\", \"flate2\", \"gcp-kms\", \"in-use-encryption-unstable\", \"log\", \"mongocrypt\", \"num_cpus\", \"openssl\", \"openssl-probe\", \"openssl-tls\", \"rayon\", \"reqwest\", \"serde_bytes\", \"snap\", \"snappy-compression\", \"sync\", \"tokio-openssl\", \"tokio-runtime\", \"tokio-sync\", \"tracing\", \"tracing-unstable\", \"zlib-compression\", \"zstd\", \"zstd-compression\"]", "target": 6467422678098242930, "profile": 12700280560728280727, "path": 15574119188886333225, "deps": [[5103565458935487, "futures_io", false, 907847457702290416], [40386456601120721, "percent_encoding", false, 11354114699177974641], [501918078635137462, "take_mut", false, 9512245532186258087], [530211389790465181, "hex", false, 10571903638377882323], [1211321333142909612, "socket2", false, 4312365498728909259], [1288403060204016458, "tokio_util", false, 1271636106207890183], [1469478705076669426, "trust_dns_proto", false, 4386048462180863296], [1526817731016152233, "stringprep", false, 14145247914632215350], [2283771217451780507, "serde_with", false, 10897217145055811332], [4258399515347749257, "pbkdf2", false, 2586948803855903266], [5841926810058920975, "strsim", false, 4843144691777852047], [7051825882133757896, "md5", false, 422240051983225459], [7620660491849607393, "futures_core", false, 10314689439286654183], [7845877979469732493, "trust_dns_resolver", false, 15434355354666597003], [8008191657135824715, "thiserror", false, 4292982608036677724], [8319709847752024821, "uuid", false, 14014932926455800830], [9209347893430674936, "hmac", false, 14836702553831990162], [9504753771229857410, "derive_more", false, 9742986320163001995], [9538054652646069845, "tokio", false, 16773429822433647056], [9689903380558560274, "serde", false, 3369735911161876335], [9751220522286876350, "rustc_version_runtime", false, 7894306947099886361], [9767387392190177945, "typed_builder", false, 11099853182260515263], [9857275760291862238, "sha2", false, 5211589121410554572], [9897246384292347999, "chrono", false, 14243974097386944381], [10435729446543529114, "bitflags", false, 3890044624233075089], [10629569228670356391, "futures_util", false, 2670255602335494802], [10889494155287625682, "serde_bytes", false, 18188723555173842236], [11295624341523567602, "rustls", false, 18149328415545614082], [11946729385090170470, "async_trait", false, 9787621569983403264], [12779779637805422465, "futures_executor", false, 7615119279332709642], [13208667028893622512, "rand", false, 17661824304044710254], [13859769749131231458, "derivative", false, 17654669988082199463], [13886384384316890372, "bson", false, 10180312306400272821], [16311359161338405624, "rustls_pemfile", false, 6726514412114151738], [16622232390123975175, "tokio_rustls", false, 3902151182156405594], [17282734725213053079, "base64", false, 315940645787677738], [17348166850176847349, "sha1", false, 16088620471721832430], [17652733826348741533, "webpki_roots", false, 5678039675759332926], [17917672826516349275, "lazy_static", false, 10676615698902490326]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mongodb-f07743b0e99abf4c/dep-lib-mongodb", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}