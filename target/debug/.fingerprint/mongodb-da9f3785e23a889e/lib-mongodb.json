{"rustc": 15597765236515928571, "features": "[\"bson-chrono-0_4\", \"serde_bytes\", \"tokio-runtime\"]", "declared_features": "[\"async-executor\", \"async-std\", \"async-std-resolver\", \"async-std-runtime\", \"aws-auth\", \"azure-kms\", \"bson-chrono-0_4\", \"bson-serde_with\", \"bson-uuid-0_8\", \"bson-uuid-1\", \"default\", \"flate2\", \"gcp-kms\", \"in-use-encryption-unstable\", \"log\", \"mongocrypt\", \"num_cpus\", \"openssl\", \"openssl-probe\", \"openssl-tls\", \"rayon\", \"reqwest\", \"serde_bytes\", \"snap\", \"snappy-compression\", \"sync\", \"tokio-openssl\", \"tokio-runtime\", \"tokio-sync\", \"tracing\", \"tracing-unstable\", \"zlib-compression\", \"zstd\", \"zstd-compression\"]", "target": 6467422678098242930, "profile": 17195064474749569637, "path": 15574119188886333225, "deps": [[5103565458935487, "futures_io", false, 5427145575870608023], [40386456601120721, "percent_encoding", false, 9596502044904975492], [501918078635137462, "take_mut", false, 5640128168323291116], [530211389790465181, "hex", false, 5104195205125474451], [1211321333142909612, "socket2", false, 15654781810639875200], [1288403060204016458, "tokio_util", false, 14029281029611050173], [1469478705076669426, "trust_dns_proto", false, 15009081170639410431], [1526817731016152233, "stringprep", false, 14055440395253618543], [2283771217451780507, "serde_with", false, 16347636274543572926], [4258399515347749257, "pbkdf2", false, 9255969876037022920], [5841926810058920975, "strsim", false, 3916952989527538656], [7051825882133757896, "md5", false, 8525336013679831894], [7620660491849607393, "futures_core", false, 4854277245167358921], [7845877979469732493, "trust_dns_resolver", false, 15033782053070018274], [8008191657135824715, "thiserror", false, 4406955040057758605], [8319709847752024821, "uuid", false, 6646619580678369137], [9209347893430674936, "hmac", false, 14419584025350283161], [9504753771229857410, "derive_more", false, 9742986320163001995], [9538054652646069845, "tokio", false, 15376650133509949099], [9689903380558560274, "serde", false, 3047106530587541600], [9751220522286876350, "rustc_version_runtime", false, 4569315173843453138], [9767387392190177945, "typed_builder", false, 11099853182260515263], [9857275760291862238, "sha2", false, 12132541297643862737], [9897246384292347999, "chrono", false, 14732775733349892726], [10435729446543529114, "bitflags", false, 1892154077323459123], [10629569228670356391, "futures_util", false, 15535723867050505389], [10889494155287625682, "serde_bytes", false, 2738509535368197053], [11295624341523567602, "rustls", false, 5457096175164732441], [11946729385090170470, "async_trait", false, 9787621569983403264], [12779779637805422465, "futures_executor", false, 12024639161782071717], [13208667028893622512, "rand", false, 6881440096561656980], [13859769749131231458, "derivative", false, 17654669988082199463], [13886384384316890372, "bson", false, 7890426477127291392], [16311359161338405624, "rustls_pemfile", false, 638526290889350645], [16622232390123975175, "tokio_rustls", false, 5541138288150451233], [17282734725213053079, "base64", false, 11374293214287144409], [17348166850176847349, "sha1", false, 10659813385200997685], [17652733826348741533, "webpki_roots", false, 4260419089064320315], [17917672826516349275, "lazy_static", false, 6451159110089736596]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mongodb-da9f3785e23a889e/dep-lib-mongodb", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}