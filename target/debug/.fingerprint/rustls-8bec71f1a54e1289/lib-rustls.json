{"rustc": 15597765236515928571, "features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 5702556193629026150, "path": 7136202088520571105, "deps": [[2883436298747778685, "pki_types", false, 11524916890323979092], [3722963349756955755, "once_cell", false, 18338104825145226077], [6528079939221783635, "zeroize", false, 4575304661798929150], [16400140949089969347, "build_script_build", false, 120235200304523691], [16944451698427853066, "aws_lc_rs", false, 12797580343665350148], [17003143334332120809, "subtle", false, 2956634959620458100], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 13301221520248740546]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-8bec71f1a54e1289/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}