{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 12700280560728280727, "path": 11408239056712582845, "deps": [[784494742817713399, "tower_service", false, 3755155159171530752], [1906322745568073236, "pin_project_lite", false, 11469206941708597148], [2517136641825875337, "sync_wrapper", false, 7756148990326351973], [7712452662827335977, "tower_layer", false, 15204665854513399784], [7858942147296547339, "rustversion", false, 1144016292959928317], [9010263965687315507, "http", false, 14207910206936264307], [10229185211513642314, "mime", false, 97263358332168719], [10629569228670356391, "futures_util", false, 2670255602335494802], [11946729385090170470, "async_trait", false, 9787621569983403264], [14084095096285906100, "http_body", false, 14517852941467237079], [16066129441945555748, "bytes", false, 13768183803970146840], [16900715236047033623, "http_body_util", false, 15698402315623042034]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-bf3152fa8ae2eccc/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}