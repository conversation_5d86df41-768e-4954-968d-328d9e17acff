{"rustc": 15597765236515928571, "features": "[\"any_impl\", \"any_zlib\", \"libz-ng-sys\", \"zlib-ng\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 17195064474749569637, "path": 11498961359426472371, "deps": [[5466618496199522463, "crc32fast", false, 12168922943821551527], [8252095289329321161, "libz_ng_sys", false, 12306166983204302287]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-a81d54b8f904c850/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}