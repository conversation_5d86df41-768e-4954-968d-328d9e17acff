{"rustc": 15597765236515928571, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 15858894019885871395, "path": 15013637280944869658, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-caba2782ce0456d1/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}