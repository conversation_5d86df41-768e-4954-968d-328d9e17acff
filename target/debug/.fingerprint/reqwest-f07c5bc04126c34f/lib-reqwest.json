{"rustc": 15597765236515928571, "features": "[\"__rustls\", \"__tls\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 12700280560728280727, "path": 54076421974538766, "deps": [[40386456601120721, "percent_encoding", false, 11354114699177974641], [95042085696191081, "ipnet", false, 7835021918067370848], [264090853244900308, "sync_wrapper", false, 16544075592476493609], [784494742817713399, "tower_service", false, 3755155159171530752], [1044435446100926395, "hyper_rustls", false, 15147818222459863956], [1906322745568073236, "pin_project_lite", false, 11469206941708597148], [3150220818285335163, "url", false, 2491340890083918030], [3722963349756955755, "once_cell", false, 16973236518097757783], [4405182208873388884, "http", false, 9742194815907298504], [5986029879202738730, "log", false, 7189003991538155933], [7414427314941361239, "hyper", false, 5259258884913083328], [7620660491849607393, "futures_core", false, 10314689439286654183], [8915503303801890683, "http_body", false, 350729921754883948], [9538054652646069845, "tokio", false, 16773429822433647056], [9689903380558560274, "serde", false, 3369735911161876335], [10229185211513642314, "mime", false, 97263358332168719], [10629569228670356391, "futures_util", false, 2670255602335494802], [11295624341523567602, "rustls", false, 18149328415545614082], [13809605890706463735, "h2", false, 14873046544393775920], [14564311161534545801, "encoding_rs", false, 15348312221865913195], [15367738274754116744, "serde_json", false, 10611765193061620429], [16066129441945555748, "bytes", false, 13768183803970146840], [16311359161338405624, "rustls_pemfile", false, 6726514412114151738], [16542808166767769916, "serde_urlencoded", false, 1645025766899658179], [16622232390123975175, "tokio_rustls", false, 3902151182156405594], [17652733826348741533, "webpki_roots", false, 5678039675759332926], [18066890886671768183, "base64", false, 11734988604463454852]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-f07c5bc04126c34f/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}