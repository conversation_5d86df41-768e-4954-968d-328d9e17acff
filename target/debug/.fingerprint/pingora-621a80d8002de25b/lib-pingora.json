{"rustc": 15597765236515928571, "features": "[\"lb\", \"pingora-load-balancing\", \"pingora-proxy\", \"proxy\"]", "declared_features": "[\"any_tls\", \"boringssl\", \"cache\", \"default\", \"document-features\", \"lb\", \"openssl\", \"openssl_derived\", \"patched_http1\", \"pingora-cache\", \"pingora-load-balancing\", \"pingora-proxy\", \"proxy\", \"rustls\", \"sentry\", \"time\"]", "target": 17002998937691195525, "profile": 12700280560728280727, "path": 4877225549769201078, "deps": [[1891952814428875727, "pingora_timeout", false, 7940644619191342571], [10348969647224947607, "pingora_core", false, 17491952783030139821], [12207781452912642015, "pingora_proxy", false, 9505389204466462794], [15607470770229500775, "pingora_load_balancing", false, 14541439355834775760], [17164677480132756649, "pingora_http", false, 16150250815711804168]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pingora-621a80d8002de25b/dep-lib-pingora", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}