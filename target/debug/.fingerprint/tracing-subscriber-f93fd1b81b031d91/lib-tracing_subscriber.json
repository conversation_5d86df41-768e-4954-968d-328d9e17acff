{"rustc": 15597765236515928571, "features": "[\"alloc\", \"env-filter\", \"fmt\", \"matchers\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"std\", \"thread_local\", \"tracing\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 7941841861029469328, "path": 16872852943132186555, "deps": [[1009387600818341822, "matchers", false, 7579994250070628235], [1017461770342116999, "sharded_slab", false, 17402890456016279316], [1359731229228270592, "thread_local", false, 1358431309530802693], [3424551429995674438, "tracing_core", false, 2402580459262690041], [3722963349756955755, "once_cell", false, 16973236518097757783], [8606274917505247608, "tracing", false, 12801918428814197877], [9451456094439810778, "regex", false, 5903540210641698642]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-f93fd1b81b031d91/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}