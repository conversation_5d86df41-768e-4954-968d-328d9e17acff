{"rustc": 15597765236515928571, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 4726246767843925232, "profile": 12700280560728280727, "path": 7512388501904372666, "deps": [[2779309023524819297, "aho_corasick", false, 7186773563888764049], [3129130049864710036, "memchr", false, 13394611481496526994], [9408802513701742484, "regex_syntax", false, 2870780423836012591]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-automata-bf16266e1b9035b6/dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}