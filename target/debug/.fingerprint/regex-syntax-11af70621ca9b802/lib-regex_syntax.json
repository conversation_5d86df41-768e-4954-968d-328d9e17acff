{"rustc": 15597765236515928571, "features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 7529137146482485884, "profile": 12700280560728280727, "path": 8673580867770111861, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-syntax-11af70621ca9b802/dep-lib-regex_syntax", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}