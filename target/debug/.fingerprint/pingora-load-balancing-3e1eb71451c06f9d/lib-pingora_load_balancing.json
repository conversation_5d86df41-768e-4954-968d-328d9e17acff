{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"any_tls\", \"boringssl\", \"default\", \"openssl\", \"openssl_derived\", \"rustls\"]", "target": 18000968732402131124, "profile": 17195064474749569637, "path": 14757130585352949781, "deps": [[1345404220202658316, "fnv", false, 1934866919886975272], [2706460456408817945, "futures", false, 12886858603184463733], [4592814918383864658, "pingora_error", false, 2983403481278240444], [5986029879202738730, "log", false, 17126298727032183130], [9010263965687315507, "http", false, 8233727746814847562], [9538054652646069845, "tokio", false, 15376650133509949099], [10348969647224947607, "pingora_core", false, 1192636708895601165], [11946729385090170470, "async_trait", false, 9787621569983403264], [12218732970638440320, "pingora_runtime", false, 715396241328771812], [13208667028893622512, "rand", false, 6881440096561656980], [13859769749131231458, "derivative", false, 17654669988082199463], [14306042979243042769, "arc_swap", false, 12027021148131916565], [17164677480132756649, "pingora_http", false, 10164246523638745922], [17577369234185105330, "pingora_ketama", false, 9921016693846543434]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pingora-load-balancing-3e1eb71451c06f9d/dep-lib-pingora_load_balancing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}