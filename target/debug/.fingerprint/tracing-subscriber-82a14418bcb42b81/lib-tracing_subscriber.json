{"rustc": 15597765236515928571, "features": "[\"alloc\", \"env-filter\", \"fmt\", \"matchers\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"std\", \"thread_local\", \"tracing\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 4016744513000226791, "path": 16872852943132186555, "deps": [[1009387600818341822, "matchers", false, 4958346710187642080], [1017461770342116999, "sharded_slab", false, 17671012447348419137], [1359731229228270592, "thread_local", false, 14418525012488231649], [3424551429995674438, "tracing_core", false, 18239167129651336186], [3722963349756955755, "once_cell", false, 18338104825145226077], [8606274917505247608, "tracing", false, 9133454467119891042], [9451456094439810778, "regex", false, 3059615849501410242]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-82a14418bcb42b81/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}