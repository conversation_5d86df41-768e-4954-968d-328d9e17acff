{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"any_tls\", \"boringssl\", \"default\", \"openssl\", \"openssl_derived\", \"rustls\"]", "target": 18000968732402131124, "profile": 12700280560728280727, "path": 14757130585352949781, "deps": [[1345404220202658316, "fnv", false, 13342691088155218628], [2706460456408817945, "futures", false, 6102949652763499012], [4592814918383864658, "pingora_error", false, 2327944531493750878], [5986029879202738730, "log", false, 7189003991538155933], [9010263965687315507, "http", false, 14207910206936264307], [9538054652646069845, "tokio", false, 16773429822433647056], [10348969647224947607, "pingora_core", false, 17491952783030139821], [11946729385090170470, "async_trait", false, 9787621569983403264], [12218732970638440320, "pingora_runtime", false, 9271754262170553849], [13208667028893622512, "rand", false, 17661824304044710254], [13859769749131231458, "derivative", false, 17654669988082199463], [14306042979243042769, "arc_swap", false, 17978078133095456850], [17164677480132756649, "pingora_http", false, 16150250815711804168], [17577369234185105330, "pingora_ketama", false, 1442450788380224522]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pingora-load-balancing-1d8f1c2277282165/dep-lib-pingora_load_balancing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}