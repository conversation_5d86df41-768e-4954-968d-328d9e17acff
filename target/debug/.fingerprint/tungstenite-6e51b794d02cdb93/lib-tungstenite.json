{"rustc": 15597765236515928571, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 3965174974797606104, "profile": 12700280560728280727, "path": 14433796262237551303, "deps": [[99287295355353247, "data_encoding", false, 18260411585934210451], [3712811570531045576, "byteorder", false, 10664208381499300764], [4359956005902820838, "utf8", false, 930416091690874669], [5986029879202738730, "log", false, 7189003991538155933], [6163892036024256188, "httparse", false, 1507858056478651801], [8008191657135824715, "thiserror", false, 4292982608036677724], [9010263965687315507, "http", false, 14207910206936264307], [10724389056617919257, "sha1", false, 3696796306990443752], [13208667028893622512, "rand", false, 17661824304044710254], [16066129441945555748, "bytes", false, 13768183803970146840]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tungstenite-6e51b794d02cdb93/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}