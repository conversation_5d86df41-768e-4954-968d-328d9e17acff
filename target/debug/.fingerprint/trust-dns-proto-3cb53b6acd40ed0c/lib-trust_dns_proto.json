{"rustc": 15597765236515928571, "features": "[\"default\", \"tokio\", \"tokio-runtime\"]", "declared_features": "[\"backtrace\", \"bytes\", \"default\", \"dns-over-https\", \"dns-over-https-rustls\", \"dns-over-native-tls\", \"dns-over-openssl\", \"dns-over-rustls\", \"dns-over-tls\", \"dnssec\", \"dnssec-openssl\", \"dnssec-ring\", \"h2\", \"http\", \"js-sys\", \"mdns\", \"native-tls\", \"openssl\", \"ring\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde-config\", \"socket2\", \"testing\", \"tokio\", \"tokio-native-tls\", \"tokio-openssl\", \"tokio-runtime\", \"tokio-rustls\", \"wasm-bindgen\", \"wasm-bindgen-crate\", \"webpki\", \"webpki-roots\"]", "target": 492484570920188181, "profile": 12700280560728280727, "path": 15404447282612872260, "deps": [[5103565458935487, "futures_io", false, 907847457702290416], [95042085696191081, "ipnet", false, 7835021918067370848], [99287295355353247, "data_encoding", false, 18260411585934210451], [1042707345065476716, "tinyvec", false, 6501826688094678178], [1811549171721445101, "futures_channel", false, 14680215421581872067], [2828590642173593838, "cfg_if", false, 1197930349209021247], [3150220818285335163, "url", false, 2491340890083918030], [3666196340704888985, "smallvec", false, 5604825507546553803], [5986029879202738730, "log", false, 7189003991538155933], [6852452874680268633, "idna", false, 4748055020828606667], [8008191657135824715, "thiserror", false, 4292982608036677724], [8472643241108742524, "enum_as_inner", false, 50386258748772077], [9538054652646069845, "tokio", false, 16773429822433647056], [10629569228670356391, "futures_util", false, 2670255602335494802], [11946729385090170470, "async_trait", false, 9787621569983403264], [13208667028893622512, "rand", false, 17661824304044710254], [17917672826516349275, "lazy_static", false, 10676615698902490326]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/trust-dns-proto-3cb53b6acd40ed0c/dep-lib-trust_dns_proto", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}