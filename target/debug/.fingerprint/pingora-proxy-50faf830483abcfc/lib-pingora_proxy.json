{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"any_tls\", \"boringssl\", \"default\", \"openssl\", \"openssl_derived\", \"rustls\", \"sentry\"]", "target": 15799030865857770792, "profile": 13491299140324981694, "path": 15390576015806691254, "deps": [[418947936956741439, "h2", false, 8668784923992784522], [2706460456408817945, "futures", false, 12886858603184463733], [3722963349756955755, "once_cell", false, 18338104825145226077], [4592814918383864658, "pingora_error", false, 2983403481278240444], [5986029879202738730, "log", false, 17126298727032183130], [9010263965687315507, "http", false, 8233727746814847562], [9451456094439810778, "regex", false, 3059615849501410242], [9538054652646069845, "tokio", false, 15376650133509949099], [10348969647224947607, "pingora_core", false, 1192636708895601165], [11946729385090170470, "async_trait", false, 9787621569983403264], [15128663763258861025, "pingora_cache", false, 7808015652609308507], [15355436635694932780, "clap", false, 11207643123892602657], [16066129441945555748, "bytes", false, 1566315335530503968], [17164677480132756649, "pingora_http", false, 10164246523638745922]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pingora-proxy-50faf830483abcfc/dep-lib-pingora_proxy", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}