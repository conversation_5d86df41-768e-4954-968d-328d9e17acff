{"rustc": 15597765236515928571, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 17195064474749569637, "path": 12178078782363569819, "deps": [[1288403060204016458, "tokio_util", false, 14029281029611050173], [1906322745568073236, "pin_project_lite", false, 7404426017592792462], [3129130049864710036, "memchr", false, 10187327220647170709], [7620660491849607393, "futures_core_03", false, 4854277245167358921], [9538054652646069845, "tokio_dep", false, 15376650133509949099], [16066129441945555748, "bytes", false, 1566315335530503968]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/combine-7cf868fc6feb7878/dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}