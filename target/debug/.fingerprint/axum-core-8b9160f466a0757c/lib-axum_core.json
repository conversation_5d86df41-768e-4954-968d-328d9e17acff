{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 17195064474749569637, "path": 11408239056712582845, "deps": [[784494742817713399, "tower_service", false, 9281592329932465444], [1906322745568073236, "pin_project_lite", false, 7404426017592792462], [2517136641825875337, "sync_wrapper", false, 7984831838346464776], [7712452662827335977, "tower_layer", false, 7677507511678965578], [7858942147296547339, "rustversion", false, 1144016292959928317], [9010263965687315507, "http", false, 8233727746814847562], [10229185211513642314, "mime", false, 3209864126256740605], [10629569228670356391, "futures_util", false, 15535723867050505389], [11946729385090170470, "async_trait", false, 9787621569983403264], [14084095096285906100, "http_body", false, 5483037815035822100], [16066129441945555748, "bytes", false, 1566315335530503968], [16900715236047033623, "http_body_util", false, 7507450945954176315]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-8b9160f466a0757c/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}