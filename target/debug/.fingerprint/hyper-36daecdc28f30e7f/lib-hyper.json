{"rustc": 15597765236515928571, "features": "[\"default\", \"http1\", \"http2\", \"server\"]", "declared_features": "[\"capi\", \"client\", \"default\", \"ffi\", \"full\", \"http1\", \"http2\", \"nightly\", \"server\", \"tracing\"]", "target": 9574292076208557625, "profile": 16641491574111796357, "path": 9481101055777595932, "deps": [[418947936956741439, "h2", false, 8994215266854598604], [1811549171721445101, "futures_channel", false, 14680215421581872067], [1906322745568073236, "pin_project_lite", false, 11469206941708597148], [3666196340704888985, "smallvec", false, 5604825507546553803], [6163892036024256188, "httparse", false, 1507858056478651801], [6304235478050270880, "httpdate", false, 5372337350121369894], [7695812897323945497, "itoa", false, 14507238717513960384], [9010263965687315507, "http", false, 14207910206936264307], [9538054652646069845, "tokio", false, 16773429822433647056], [10629569228670356391, "futures_util", false, 2670255602335494802], [14084095096285906100, "http_body", false, 14517852941467237079], [16066129441945555748, "bytes", false, 13768183803970146840]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hyper-36daecdc28f30e7f/dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}