{"rustc": 15597765236515928571, "features": "[\"elf\", \"errno\", \"general\", \"if_ether\", \"ioctl\", \"net\", \"netlink\", \"no_std\", \"prctl\", \"xdp\"]", "declared_features": "[\"bootparam\", \"btrfs\", \"compiler_builtins\", \"core\", \"default\", \"elf\", \"elf_uapi\", \"errno\", \"general\", \"if_arp\", \"if_ether\", \"if_packet\", \"image\", \"io_uring\", \"ioctl\", \"landlock\", \"loop_device\", \"mempolicy\", \"net\", \"netlink\", \"no_std\", \"prctl\", \"ptrace\", \"rustc-dep-of-std\", \"std\", \"system\", \"xdp\"]", "target": 5772965225213482929, "profile": 18398075661344196884, "path": 4048901222374795397, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/linux-raw-sys-cbf5c96b875ef37b/dep-lib-linux_raw_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}