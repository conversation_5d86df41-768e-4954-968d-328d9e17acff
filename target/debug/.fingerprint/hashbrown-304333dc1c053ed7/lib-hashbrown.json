{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 12700280560728280727, "path": 13282080794851334821, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-304333dc1c053ed7/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}