{"rustc": 15597765236515928571, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 12700280560728280727, "path": 12178078782363569819, "deps": [[1288403060204016458, "tokio_util", false, 1271636106207890183], [1906322745568073236, "pin_project_lite", false, 11469206941708597148], [3129130049864710036, "memchr", false, 13394611481496526994], [7620660491849607393, "futures_core_03", false, 10314689439286654183], [9538054652646069845, "tokio_dep", false, 16773429822433647056], [16066129441945555748, "bytes", false, 13768183803970146840]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/combine-0dfbf085085e0da8/dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}