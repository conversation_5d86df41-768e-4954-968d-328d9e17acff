{"rustc": 15597765236515928571, "features": "[\"atty\", \"clap_derive\", \"color\", \"default\", \"derive\", \"once_cell\", \"std\", \"strsim\", \"suggestions\", \"termcolor\"]", "declared_features": "[\"atty\", \"backtrace\", \"cargo\", \"clap_derive\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"once_cell\", \"regex\", \"std\", \"strsim\", \"suggestions\", \"termcolor\", \"terminal_size\", \"unicase\", \"unicode\", \"unstable-doc\", \"unstable-grouped\", \"unstable-replace\", \"unstable-v4\", \"wrap_help\", \"yaml\", \"yaml-rust\"]", "target": 725892165292113192, "profile": 12700280560728280727, "path": 15469193877634190758, "deps": [[580378868546634928, "textwrap", false, 8410285843557813264], [3722963349756955755, "once_cell", false, 16973236518097757783], [4028661655699515326, "clap_derive", false, 10295685966302958072], [5841926810058920975, "strsim", false, 4843144691777852047], [10058577953979766589, "atty", false, 16091394564140089264], [10435729446543529114, "bitflags", false, 3890044624233075089], [12902659978838094914, "termcolor", false, 13661151419089765054], [14923790796823607459, "indexmap", false, 18338334879138757748], [15944592714770878610, "clap_lex", false, 7540993956491877312]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-248d82205dba40e3/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}