{"rustc": 15597765236515928571, "features": "[\"alloc\", \"event\", \"fs\", \"net\", \"pipe\", \"process\", \"std\", \"time\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 10194946731562020788, "path": 14631796156474888313, "deps": [[7896293946984509699, "bitflags", false, 9426407360707775793], [12053020504183902936, "build_script_build", false, 8945047363774968411], [12846346674781677812, "linux_raw_sys", false, 7690654008308159136]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-2ddeb39eb93158df/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}