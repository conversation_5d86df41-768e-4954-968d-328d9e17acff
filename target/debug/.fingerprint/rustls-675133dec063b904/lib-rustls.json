{"rustc": 15597765236515928571, "features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 4000828793364460390, "path": 7136202088520571105, "deps": [[2883436298747778685, "pki_types", false, 15092563333525526629], [3722963349756955755, "once_cell", false, 16973236518097757783], [6528079939221783635, "zeroize", false, 13230605232553055730], [16400140949089969347, "build_script_build", false, 120235200304523691], [16944451698427853066, "aws_lc_rs", false, 17098339356185202571], [17003143334332120809, "subtle", false, 4723686820745073575], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 1459404480164928112]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-675133dec063b904/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}