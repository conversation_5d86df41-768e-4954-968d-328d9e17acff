{"$message_type":"diagnostic","message":"empty line after doc comment","code":{"code":"clippy::empty_line_after_doc_comments","explanation":null},"level":"error","spans":[{"file_name":"src/ssl/mod.rs","byte_start":187,"byte_end":187,"line_start":9,"line_end":9,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":1}],"label":"the comment documents this `use` import","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":160,"byte_end":186,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 4. 证书文件管理","highlight_start":1,"highlight_end":14},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`-D clippy::empty-line-after-doc-comments` implied by `-D warnings`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"to override `-D warnings` add `#[allow(clippy::empty_line_after_doc_comments)]`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if the empty line is unintentional, remove it","code":null,"level":"help","spans":[{"file_name":"src/ssl/mod.rs","byte_start":185,"byte_end":186,"line_start":7,"line_end":8,"column_start":14,"column_end":1,"is_primary":true,"text":[{"text":"/// 4. 证书文件管理","highlight_start":14,"highlight_end":14},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if the comment should document the parent module use an inner doc comment","code":null,"level":"help","spans":[{"file_name":"src/ssl/mod.rs","byte_start":2,"byte_end":3,"line_start":1,"line_end":1,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// SSL证书自动申请和管理模块","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":43,"byte_end":44,"line_start":2,"line_end":2,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// ","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":48,"byte_end":49,"line_start":3,"line_end":3,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 核心功能：","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":68,"byte_end":69,"line_start":4,"line_end":4,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 1. 通配符SSL证书自动申请","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":106,"byte_end":107,"line_start":5,"line_end":5,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 2. 阿里云DNS API集成","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":136,"byte_end":137,"line_start":6,"line_end":6,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 3. 证书自动续期","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":162,"byte_end":163,"line_start":7,"line_end":7,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 4. 证书文件管理","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: empty line after doc comment\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/mod.rs:7:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m/// 4. 证书文件管理\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthe comment documents this `use` import\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `-D clippy::empty-line-after-doc-comments` implied by `-D warnings`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: to override `-D warnings` add `#[allow(clippy::empty_line_after_doc_comments)]`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: \u001b[0m\u001b[0mif the empty line is unintentional, remove it\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if the comment should document the parent module use an inner doc comment\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m SSL证书自动申请和管理模块\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 核心功能：\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 1. 通配符SSL证书自动申请\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 2. 阿里云DNS API集成\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 3. 证书自动续期\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 4. 证书文件管理\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"empty line after doc comment","code":{"code":"clippy::empty_line_after_doc_comments","explanation":null},"level":"error","spans":[{"file_name":"src/ssl/acme_client.rs","byte_start":81,"byte_end":81,"line_start":4,"line_end":4,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":"the comment documents this `use` import","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ssl/acme_client.rs","byte_start":24,"byte_end":80,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 使用rustls-acme库实现Let's Encrypt证书申请","highlight_start":1,"highlight_end":38},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if the empty line is unintentional, remove it","code":null,"level":"help","spans":[{"file_name":"src/ssl/acme_client.rs","byte_start":79,"byte_end":80,"line_start":2,"line_end":3,"column_start":38,"column_end":1,"is_primary":true,"text":[{"text":"/// 使用rustls-acme库实现Let's Encrypt证书申请","highlight_start":38,"highlight_end":38},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if the comment should document the parent module use an inner doc comment","code":null,"level":"help","spans":[{"file_name":"src/ssl/acme_client.rs","byte_start":2,"byte_end":3,"line_start":1,"line_end":1,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// ACME客户端实现","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/acme_client.rs","byte_start":26,"byte_end":27,"line_start":2,"line_end":2,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 使用rustls-acme库实现Let's Encrypt证书申请","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: empty line after doc comment\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/acme_client.rs:2:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m/// 使用rustls-acme库实现Let's Encrypt证书申请\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthe comment documents this `use` import\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: \u001b[0m\u001b[0mif the empty line is unintentional, remove it\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if the comment should document the parent module use an inner doc comment\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m ACME客户端实现\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 使用rustls-acme库实现Let's Encrypt证书申请\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"empty line after doc comment","code":{"code":"clippy::empty_line_after_doc_comments","explanation":null},"level":"error","spans":[{"file_name":"src/ssl/dns_provider.rs","byte_start":78,"byte_end":78,"line_start":4,"line_end":4,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":"the comment documents this `use` import","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ssl/dns_provider.rs","byte_start":32,"byte_end":77,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 支持阿里云DNS API进行DNS-01挑战","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if the empty line is unintentional, remove it","code":null,"level":"help","spans":[{"file_name":"src/ssl/dns_provider.rs","byte_start":76,"byte_end":77,"line_start":2,"line_end":3,"column_start":27,"column_end":1,"is_primary":true,"text":[{"text":"/// 支持阿里云DNS API进行DNS-01挑战","highlight_start":27,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if the comment should document the parent module use an inner doc comment","code":null,"level":"help","spans":[{"file_name":"src/ssl/dns_provider.rs","byte_start":2,"byte_end":3,"line_start":1,"line_end":1,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// DNS提供商抽象和实现","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/dns_provider.rs","byte_start":34,"byte_end":35,"line_start":2,"line_end":2,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 支持阿里云DNS API进行DNS-01挑战","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: empty line after doc comment\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/dns_provider.rs:2:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m/// 支持阿里云DNS API进行DNS-01挑战\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthe comment documents this `use` import\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: \u001b[0m\u001b[0mif the empty line is unintentional, remove it\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if the comment should document the parent module use an inner doc comment\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m DNS提供商抽象和实现\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 支持阿里云DNS API进行DNS-01挑战\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"empty line after doc comment","code":{"code":"clippy::empty_line_after_doc_comments","explanation":null},"level":"error","spans":[{"file_name":"src/ssl/cert_storage.rs","byte_start":89,"byte_end":89,"line_start":4,"line_end":4,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"use std::path::{Path, PathBuf};","highlight_start":1,"highlight_end":1}],"label":"the comment documents this `use` import","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ssl/cert_storage.rs","byte_start":32,"byte_end":88,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 负责证书文件的安全存储、备份和恢复","highlight_start":1,"highlight_end":22},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if the empty line is unintentional, remove it","code":null,"level":"help","spans":[{"file_name":"src/ssl/cert_storage.rs","byte_start":87,"byte_end":88,"line_start":2,"line_end":3,"column_start":22,"column_end":1,"is_primary":true,"text":[{"text":"/// 负责证书文件的安全存储、备份和恢复","highlight_start":22,"highlight_end":22},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if the comment should document the parent module use an inner doc comment","code":null,"level":"help","spans":[{"file_name":"src/ssl/cert_storage.rs","byte_start":2,"byte_end":3,"line_start":1,"line_end":1,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// SSL证书文件存储管理","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/cert_storage.rs","byte_start":34,"byte_end":35,"line_start":2,"line_end":2,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 负责证书文件的安全存储、备份和恢复","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: empty line after doc comment\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/cert_storage.rs:2:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m/// 负责证书文件的安全存储、备份和恢复\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0muse std::path::{Path, PathBuf};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthe comment documents this `use` import\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: \u001b[0m\u001b[0mif the empty line is unintentional, remove it\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if the comment should document the parent module use an inner doc comment\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m SSL证书文件存储管理\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 负责证书文件的安全存储、备份和恢复\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::decode`: Use Engine::decode","code":{"code":"deprecated","explanation":null},"level":"error","spans":[{"file_name":"src/domains/extractor.rs","byte_start":4641,"byte_end":4647,"line_start":138,"line_end":138,"column_start":46,"column_end":52,"is_primary":true,"text":[{"text":"                if let Ok(decoded) = base64::decode(base64_str.as_str()) {","highlight_start":46,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`-D deprecated` implied by `-D warnings`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"to override `-D warnings` add `#[allow(deprecated)]`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `base64::decode`: Use Engine::decode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/extractor.rs:138:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if let Ok(decoded) = base64::decode(base64_str.as_str()) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `-D deprecated` implied by `-D warnings`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: to override `-D warnings` add `#[allow(deprecated)]`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::decode`: Use Engine::decode","code":{"code":"deprecated","explanation":null},"level":"error","spans":[{"file_name":"src/domains/replacer.rs","byte_start":8583,"byte_end":8589,"line_start":234,"line_end":234,"column_start":42,"column_end":48,"is_primary":true,"text":[{"text":"            if let Ok(decoded) = base64::decode(base64_str) {","highlight_start":42,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `base64::decode`: Use Engine::decode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/replacer.rs:234:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m234\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Ok(decoded) = base64::decode(base64_str) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"error","spans":[{"file_name":"src/domains/replacer.rs","byte_start":8905,"byte_end":8911,"line_start":239,"line_end":239,"column_start":56,"column_end":62,"is_primary":true,"text":[{"text":"                        if let Ok(reencoded) = base64::encode(&replaced).parse::<String>() {","highlight_start":56,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/replacer.rs:239:56\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m239\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        if let Ok(reencoded) = base64::encode(&replaced).parse::<String>() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"error","spans":[{"file_name":"src/ssl/dns_provider.rs","byte_start":2790,"byte_end":2796,"line_start":85,"line_end":85,"column_start":17,"column_end":23,"is_primary":true,"text":[{"text":"        base64::encode(signature)","highlight_start":17,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/dns_provider.rs:85:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        base64::encode(signature)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"irrefutable `if let` pattern","code":{"code":"irrefutable_let_patterns","explanation":null},"level":"error","spans":[{"file_name":"src/domains/replacer.rs","byte_start":8877,"byte_end":8940,"line_start":239,"line_end":239,"column_start":28,"column_end":91,"is_primary":true,"text":[{"text":"                        if let Ok(reencoded) = base64::encode(&replaced).parse::<String>() {","highlight_start":28,"highlight_end":91}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this pattern will always match, so the `if let` is useless","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider replacing the `if let` with a `let`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`-D irrefutable-let-patterns` implied by `-D warnings`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"to override `-D warnings` add `#[allow(irrefutable_let_patterns)]`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: irrefutable `if let` pattern\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/replacer.rs:239:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m239\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        if let Ok(reencoded) = base64::encode(&replaced).parse::<String>() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this pattern will always match, so the `if let` is useless\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider replacing the `if let` with a `let`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `-D irrefutable-let-patterns` implied by `-D warnings`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: to override `-D warnings` add `#[allow(irrefutable_let_patterns)]`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"regex syntax error: backreferences are not supported","code":{"code":"clippy::invalid_regex","explanation":null},"level":"error","spans":[{"file_name":"src/domains/replacer.rs","byte_start":3995,"byte_end":3997,"line_start":112,"line_end":112,"column_start":71,"column_end":73,"is_primary":true,"text":[{"text":"        let css_url_regex = Regex::new(r#\"url\\s*\\(\\s*(['\"\"]?)([^'\")]+)\\1\\s*\\)\"#)?;","highlight_start":71,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#invalid_regex","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[deny(clippy::invalid_regex)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: regex syntax error: backreferences are not supported\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/replacer.rs:112:71\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m112\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let css_url_regex = Regex::new(r#\"url\\s*\\(\\s*(['\"\"]?)([^'\")]+)\\1\\s*\\)\"#)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#invalid_regex\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[deny(clippy::invalid_regex)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"regex syntax error: backreferences are not supported","code":{"code":"clippy::invalid_regex","explanation":null},"level":"error","spans":[{"file_name":"src/domains/replacer.rs","byte_start":4877,"byte_end":4879,"line_start":136,"line_end":136,"column_start":82,"column_end":84,"is_primary":true,"text":[{"text":"        let js_string_regex = Regex::new(r#\"(['\"`])([^'\"`]*(?:https?://[^'\"`]*)?)\\1\"#)?;","highlight_start":82,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#invalid_regex","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: regex syntax error: backreferences are not supported\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/replacer.rs:136:82\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m136\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let js_string_regex = Regex::new(r#\"(['\"`])([^'\"`]*(?:https?://[^'\"`]*)?)\\1\"#)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#invalid_regex\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"redundant closure","code":{"code":"clippy::redundant_closure","explanation":null},"level":"error","spans":[{"file_name":"src/monitoring/mod.rs","byte_start":11057,"byte_end":11085,"line_start":273,"line_end":273,"column_start":32,"column_end":60,"is_primary":true,"text":[{"text":"    GLOBAL_MONITOR.get_or_init(|| PerformanceMonitor::new())","highlight_start":32,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_closure","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`-D clippy::redundant-closure` implied by `-D warnings`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"to override `-D warnings` add `#[allow(clippy::redundant_closure)]`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"replace the closure with the function itself","code":null,"level":"help","spans":[{"file_name":"src/monitoring/mod.rs","byte_start":11057,"byte_end":11085,"line_start":273,"line_end":273,"column_start":32,"column_end":60,"is_primary":true,"text":[{"text":"    GLOBAL_MONITOR.get_or_init(|| PerformanceMonitor::new())","highlight_start":32,"highlight_end":60}],"label":null,"suggested_replacement":"PerformanceMonitor::new","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: redundant closure\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/monitoring/mod.rs:273:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m273\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    GLOBAL_MONITOR.get_or_init(|| PerformanceMonitor::new())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: replace the closure with the function itself: `PerformanceMonitor::new`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_closure\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `-D clippy::redundant-closure` implied by `-D warnings`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: to override `-D warnings` add `#[allow(clippy::redundant_closure)]`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"redundant closure","code":{"code":"clippy::redundant_closure","explanation":null},"level":"error","spans":[{"file_name":"src/optimized/mod.rs","byte_start":3896,"byte_end":3929,"line_start":130,"line_end":130,"column_start":31,"column_end":64,"is_primary":true,"text":[{"text":"    GLOBAL_CONFIG.get_or_init(|| OptimizationConfig::from_env())","highlight_start":31,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_closure","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"replace the closure with the function itself","code":null,"level":"help","spans":[{"file_name":"src/optimized/mod.rs","byte_start":3896,"byte_end":3929,"line_start":130,"line_end":130,"column_start":31,"column_end":64,"is_primary":true,"text":[{"text":"    GLOBAL_CONFIG.get_or_init(|| OptimizationConfig::from_env())","highlight_start":31,"highlight_end":64}],"label":null,"suggested_replacement":"OptimizationConfig::from_env","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: redundant closure\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/optimized/mod.rs:130:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    GLOBAL_CONFIG.get_or_init(|| OptimizationConfig::from_env())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: replace the closure with the function itself: `OptimizationConfig::from_env`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#redundant_closure\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this `MutexGuard` is held across an await point","code":{"code":"clippy::await_holding_lock","explanation":null},"level":"error","spans":[{"file_name":"src/ssl/mod.rs","byte_start":9457,"byte_end":9473,"line_start":283,"line_end":283,"column_start":13,"column_end":29,"is_primary":true,"text":[{"text":"        let mut certificates = self.certificates.write();","highlight_start":13,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using an async-aware `Mutex` type or ensuring the `MutexGuard` is dropped before calling `await`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"these are all the await points this lock is held through","code":null,"level":"note","spans":[{"file_name":"src/ssl/mod.rs","byte_start":9543,"byte_end":9548,"line_start":285,"line_end":285,"column_start":32,"column_end":37,"is_primary":true,"text":[{"text":"        while cursor.advance().await? {","highlight_start":32,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/ssl/mod.rs","byte_start":9543,"byte_end":9548,"line_start":285,"line_end":285,"column_start":32,"column_end":37,"is_primary":false,"text":[{"text":"        while cursor.advance().await? {","highlight_start":32,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `await` expression","def_site_span":{"file_name":"src/main.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":null},{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#await_holding_lock","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`-D clippy::await-holding-lock` implied by `-D warnings`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"to override `-D warnings` add `#[allow(clippy::await_holding_lock)]`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: this `MutexGuard` is held across an await point\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/mod.rs:283:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m283\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut certificates = self.certificates.write();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider using an async-aware `Mutex` type or ensuring the `MutexGuard` is dropped before calling `await`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: these are all the await points this lock is held through\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/mod.rs:285:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m285\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        while cursor.advance().await? {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#await_holding_lock\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `-D clippy::await-holding-lock` implied by `-D warnings`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: to override `-D warnings` add `#[allow(clippy::await_holding_lock)]`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"clamp-like pattern without using clamp function","code":{"code":"clippy::manual_clamp","explanation":null},"level":"error","spans":[{"file_name":"src/types.rs","byte_start":47658,"byte_end":47691,"line_start":1560,"line_end":1560,"column_start":30,"column_end":63,"is_primary":true,"text":[{"text":"        let max_concurrent = (cpu_cores * 20).min(200).max(50); // 每核心20个并发，最少50，最多200","highlight_start":30,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"clamp will panic if max < min","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_clamp","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`-D clippy::manual-clamp` implied by `-D warnings`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"to override `-D warnings` add `#[allow(clippy::manual_clamp)]`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"replace with clamp","code":null,"level":"help","spans":[{"file_name":"src/types.rs","byte_start":47658,"byte_end":47691,"line_start":1560,"line_end":1560,"column_start":30,"column_end":63,"is_primary":true,"text":[{"text":"        let max_concurrent = (cpu_cores * 20).min(200).max(50); // 每核心20个并发，最少50，最多200","highlight_start":30,"highlight_end":63}],"label":null,"suggested_replacement":"(cpu_cores * 20).clamp(50, 200)","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: clamp-like pattern without using clamp function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/types.rs:1560:30\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1560\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let max_concurrent = (cpu_cores * 20).min(200).max(50); // 每核心20个并发，最少50，最多200\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: replace with clamp: `(cpu_cores * 20).clamp(50, 200)`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: clamp will panic if max < min\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_clamp\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `-D clippy::manual-clamp` implied by `-D warnings`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: to override `-D warnings` add `#[allow(clippy::manual_clamp)]`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 15 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 15 previous errors\u001b[0m\n\n"}
