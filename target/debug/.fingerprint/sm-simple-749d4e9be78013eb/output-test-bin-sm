{"$message_type":"diagnostic","message":"empty line after doc comment","code":{"code":"clippy::empty_line_after_doc_comments","explanation":null},"level":"error","spans":[{"file_name":"src/ssl/mod.rs","byte_start":187,"byte_end":187,"line_start":9,"line_end":9,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":1}],"label":"the comment documents this `use` import","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":160,"byte_end":186,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 4. 证书文件管理","highlight_start":1,"highlight_end":14},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`-D clippy::empty-line-after-doc-comments` implied by `-D warnings`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"to override `-D warnings` add `#[allow(clippy::empty_line_after_doc_comments)]`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if the empty line is unintentional, remove it","code":null,"level":"help","spans":[{"file_name":"src/ssl/mod.rs","byte_start":185,"byte_end":186,"line_start":7,"line_end":8,"column_start":14,"column_end":1,"is_primary":true,"text":[{"text":"/// 4. 证书文件管理","highlight_start":14,"highlight_end":14},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if the comment should document the parent module use an inner doc comment","code":null,"level":"help","spans":[{"file_name":"src/ssl/mod.rs","byte_start":2,"byte_end":3,"line_start":1,"line_end":1,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// SSL证书自动申请和管理模块","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":43,"byte_end":44,"line_start":2,"line_end":2,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// ","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":48,"byte_end":49,"line_start":3,"line_end":3,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 核心功能：","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":68,"byte_end":69,"line_start":4,"line_end":4,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 1. 通配符SSL证书自动申请","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":106,"byte_end":107,"line_start":5,"line_end":5,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 2. 阿里云DNS API集成","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":136,"byte_end":137,"line_start":6,"line_end":6,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 3. 证书自动续期","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/mod.rs","byte_start":162,"byte_end":163,"line_start":7,"line_end":7,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 4. 证书文件管理","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: empty line after doc comment\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/mod.rs:7:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m/// 4. 证书文件管理\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthe comment documents this `use` import\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `-D clippy::empty-line-after-doc-comments` implied by `-D warnings`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: to override `-D warnings` add `#[allow(clippy::empty_line_after_doc_comments)]`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: \u001b[0m\u001b[0mif the empty line is unintentional, remove it\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if the comment should document the parent module use an inner doc comment\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m SSL证书自动申请和管理模块\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 核心功能：\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 1. 通配符SSL证书自动申请\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 2. 阿里云DNS API集成\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 3. 证书自动续期\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 4. 证书文件管理\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"empty line after doc comment","code":{"code":"clippy::empty_line_after_doc_comments","explanation":null},"level":"error","spans":[{"file_name":"src/ssl/acme_client.rs","byte_start":81,"byte_end":81,"line_start":4,"line_end":4,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":"the comment documents this `use` import","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ssl/acme_client.rs","byte_start":24,"byte_end":80,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 使用rustls-acme库实现Let's Encrypt证书申请","highlight_start":1,"highlight_end":38},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if the empty line is unintentional, remove it","code":null,"level":"help","spans":[{"file_name":"src/ssl/acme_client.rs","byte_start":79,"byte_end":80,"line_start":2,"line_end":3,"column_start":38,"column_end":1,"is_primary":true,"text":[{"text":"/// 使用rustls-acme库实现Let's Encrypt证书申请","highlight_start":38,"highlight_end":38},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if the comment should document the parent module use an inner doc comment","code":null,"level":"help","spans":[{"file_name":"src/ssl/acme_client.rs","byte_start":2,"byte_end":3,"line_start":1,"line_end":1,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// ACME客户端实现","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/acme_client.rs","byte_start":26,"byte_end":27,"line_start":2,"line_end":2,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 使用rustls-acme库实现Let's Encrypt证书申请","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: empty line after doc comment\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/acme_client.rs:2:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m/// 使用rustls-acme库实现Let's Encrypt证书申请\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthe comment documents this `use` import\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: \u001b[0m\u001b[0mif the empty line is unintentional, remove it\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if the comment should document the parent module use an inner doc comment\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m ACME客户端实现\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 使用rustls-acme库实现Let's Encrypt证书申请\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"empty line after doc comment","code":{"code":"clippy::empty_line_after_doc_comments","explanation":null},"level":"error","spans":[{"file_name":"src/ssl/dns_provider.rs","byte_start":78,"byte_end":78,"line_start":4,"line_end":4,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":"the comment documents this `use` import","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ssl/dns_provider.rs","byte_start":32,"byte_end":77,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 支持阿里云DNS API进行DNS-01挑战","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if the empty line is unintentional, remove it","code":null,"level":"help","spans":[{"file_name":"src/ssl/dns_provider.rs","byte_start":76,"byte_end":77,"line_start":2,"line_end":3,"column_start":27,"column_end":1,"is_primary":true,"text":[{"text":"/// 支持阿里云DNS API进行DNS-01挑战","highlight_start":27,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if the comment should document the parent module use an inner doc comment","code":null,"level":"help","spans":[{"file_name":"src/ssl/dns_provider.rs","byte_start":2,"byte_end":3,"line_start":1,"line_end":1,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// DNS提供商抽象和实现","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/dns_provider.rs","byte_start":34,"byte_end":35,"line_start":2,"line_end":2,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 支持阿里云DNS API进行DNS-01挑战","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: empty line after doc comment\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/dns_provider.rs:2:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m/// 支持阿里云DNS API进行DNS-01挑战\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthe comment documents this `use` import\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: \u001b[0m\u001b[0mif the empty line is unintentional, remove it\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if the comment should document the parent module use an inner doc comment\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m DNS提供商抽象和实现\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 支持阿里云DNS API进行DNS-01挑战\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"empty line after doc comment","code":{"code":"clippy::empty_line_after_doc_comments","explanation":null},"level":"error","spans":[{"file_name":"src/ssl/cert_storage.rs","byte_start":89,"byte_end":89,"line_start":4,"line_end":4,"column_start":1,"column_end":1,"is_primary":false,"text":[{"text":"use std::path::{Path, PathBuf};","highlight_start":1,"highlight_end":1}],"label":"the comment documents this `use` import","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/ssl/cert_storage.rs","byte_start":32,"byte_end":88,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"/// 负责证书文件的安全存储、备份和恢复","highlight_start":1,"highlight_end":22},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if the empty line is unintentional, remove it","code":null,"level":"help","spans":[{"file_name":"src/ssl/cert_storage.rs","byte_start":87,"byte_end":88,"line_start":2,"line_end":3,"column_start":22,"column_end":1,"is_primary":true,"text":[{"text":"/// 负责证书文件的安全存储、备份和恢复","highlight_start":22,"highlight_end":22},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if the comment should document the parent module use an inner doc comment","code":null,"level":"help","spans":[{"file_name":"src/ssl/cert_storage.rs","byte_start":2,"byte_end":3,"line_start":1,"line_end":1,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// SSL证书文件存储管理","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/ssl/cert_storage.rs","byte_start":34,"byte_end":35,"line_start":2,"line_end":2,"column_start":3,"column_end":4,"is_primary":true,"text":[{"text":"/// 负责证书文件的安全存储、备份和恢复","highlight_start":3,"highlight_end":4}],"label":null,"suggested_replacement":"!","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: empty line after doc comment\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/cert_storage.rs:2:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m/// 负责证书文件的安全存储、备份和恢复\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0muse std::path::{Path, PathBuf};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthe comment documents this `use` import\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#empty_line_after_doc_comments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: \u001b[0m\u001b[0mif the empty line is unintentional, remove it\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if the comment should document the parent module use an inner doc comment\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m SSL证书文件存储管理\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m//\u001b[0m\u001b[0m\u001b[38;5;10m!\u001b[0m\u001b[0m 负责证书文件的安全存储、备份和恢复\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::decode`: Use Engine::decode","code":{"code":"deprecated","explanation":null},"level":"error","spans":[{"file_name":"src/domains/extractor.rs","byte_start":4641,"byte_end":4647,"line_start":138,"line_end":138,"column_start":46,"column_end":52,"is_primary":true,"text":[{"text":"                if let Ok(decoded) = base64::decode(base64_str.as_str()) {","highlight_start":46,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`-D deprecated` implied by `-D warnings`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"to override `-D warnings` add `#[allow(deprecated)]`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `base64::decode`: Use Engine::decode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/extractor.rs:138:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                if let Ok(decoded) = base64::decode(base64_str.as_str()) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `-D deprecated` implied by `-D warnings`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: to override `-D warnings` add `#[allow(deprecated)]`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::decode`: Use Engine::decode","code":{"code":"deprecated","explanation":null},"level":"error","spans":[{"file_name":"src/domains/replacer.rs","byte_start":8583,"byte_end":8589,"line_start":234,"line_end":234,"column_start":42,"column_end":48,"is_primary":true,"text":[{"text":"            if let Ok(decoded) = base64::decode(base64_str) {","highlight_start":42,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `base64::decode`: Use Engine::decode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/replacer.rs:234:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m234\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Ok(decoded) = base64::decode(base64_str) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"error","spans":[{"file_name":"src/domains/replacer.rs","byte_start":8905,"byte_end":8911,"line_start":239,"line_end":239,"column_start":56,"column_end":62,"is_primary":true,"text":[{"text":"                        if let Ok(reencoded) = base64::encode(&replaced).parse::<String>() {","highlight_start":56,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/replacer.rs:239:56\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m239\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        if let Ok(reencoded) = base64::encode(&replaced).parse::<String>() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"error","spans":[{"file_name":"src/ssl/dns_provider.rs","byte_start":2790,"byte_end":2796,"line_start":85,"line_end":85,"column_start":17,"column_end":23,"is_primary":true,"text":[{"text":"        base64::encode(signature)","highlight_start":17,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ssl/dns_provider.rs:85:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        base64::encode(signature)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `clone` found for struct `std::sync::atomic::AtomicUsize` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/optimized/cache.rs","byte_start":4242,"byte_end":4247,"line_start":141,"line_end":141,"column_start":60,"column_end":65,"is_primary":true,"text":[{"text":"                    let total_entries = self.total_entries.clone();","highlight_start":60,"highlight_end":65}],"label":"method not found in `AtomicUsize`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `clone` found for struct `std::sync::atomic::AtomicUsize` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/optimized/cache.rs:141:60\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let total_entries = self.total_entries.clone();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `AtomicUsize`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `clone` found for struct `std::sync::atomic::AtomicUsize` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/optimized/cache.rs","byte_start":4308,"byte_end":4313,"line_start":142,"line_end":142,"column_start":58,"column_end":63,"is_primary":true,"text":[{"text":"                    let total_memory = self.total_memory.clone();","highlight_start":58,"highlight_end":63}],"label":"method not found in `AtomicUsize`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `clone` found for struct `std::sync::atomic::AtomicUsize` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/optimized/cache.rs:142:58\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let total_memory = self.total_memory.clone();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `AtomicUsize`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"irrefutable `if let` pattern","code":{"code":"irrefutable_let_patterns","explanation":null},"level":"error","spans":[{"file_name":"src/domains/replacer.rs","byte_start":8877,"byte_end":8940,"line_start":239,"line_end":239,"column_start":28,"column_end":91,"is_primary":true,"text":[{"text":"                        if let Ok(reencoded) = base64::encode(&replaced).parse::<String>() {","highlight_start":28,"highlight_end":91}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this pattern will always match, so the `if let` is useless","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider replacing the `if let` with a `let`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`-D irrefutable-let-patterns` implied by `-D warnings`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"to override `-D warnings` add `#[allow(irrefutable_let_patterns)]`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: irrefutable `if let` pattern\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domains/replacer.rs:239:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m239\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        if let Ok(reencoded) = base64::encode(&replaced).parse::<String>() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this pattern will always match, so the `if let` is useless\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider replacing the `if let` with a `let`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `-D irrefutable-let-patterns` implied by `-D warnings`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: to override `-D warnings` add `#[allow(irrefutable_let_patterns)]`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 11 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 11 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0599`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0599`.\u001b[0m\n"}
