{"rustc": 15597765236515928571, "features": "[\"lb\", \"pingora-load-balancing\", \"pingora-proxy\", \"proxy\"]", "declared_features": "[\"any_tls\", \"boringssl\", \"cache\", \"default\", \"document-features\", \"lb\", \"openssl\", \"openssl_derived\", \"patched_http1\", \"pingora-cache\", \"pingora-load-balancing\", \"pingora-proxy\", \"proxy\", \"rustls\", \"sentry\", \"time\"]", "target": 17002998937691195525, "profile": 17195064474749569637, "path": 4877225549769201078, "deps": [[1891952814428875727, "pingora_timeout", false, 14078080470232039648], [10348969647224947607, "pingora_core", false, 1192636708895601165], [12207781452912642015, "pingora_proxy", false, 5328251897425545289], [15607470770229500775, "pingora_load_balancing", false, 11106554235576831572], [17164677480132756649, "pingora_http", false, 10164246523638745922]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pingora-8331b4db7e4ba079/dep-lib-pingora", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}