{"rustc": 15597765236515928571, "features": "[\"client\", \"h2\", \"http1\", \"http2\", \"runtime\", \"socket2\", \"tcp\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"backports\", \"client\", \"default\", \"deprecated\", \"ffi\", \"full\", \"h2\", \"http1\", \"http2\", \"libc\", \"nightly\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "target": 5299595107718448861, "profile": 17195064474749569637, "path": 8060679526570778406, "deps": [[784494742817713399, "tower_service", false, 9281592329932465444], [1569313478171189446, "want", false, 6051871092152412006], [1811549171721445101, "futures_channel", false, 1453788504570015587], [1906322745568073236, "pin_project_lite", false, 7404426017592792462], [4405182208873388884, "http", false, 16515868446764083777], [6163892036024256188, "httparse", false, 7394247263984186888], [6304235478050270880, "httpdate", false, 1042163322219690487], [7620660491849607393, "futures_core", false, 4854277245167358921], [7695812897323945497, "itoa", false, 2658881421350578785], [8606274917505247608, "tracing", false, 9133454467119891042], [8915503303801890683, "http_body", false, 14276110962147134801], [9538054652646069845, "tokio", false, 15376650133509949099], [10629569228670356391, "futures_util", false, 15535723867050505389], [12614995553916589825, "socket2", false, 471925930377388745], [13809605890706463735, "h2", false, 16552333541984842553], [16066129441945555748, "bytes", false, 1566315335530503968]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hyper-ae18097c65a99b1a/dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}