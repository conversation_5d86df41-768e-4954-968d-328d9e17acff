{"rustc": 15597765236515928571, "features": "[\"__rustls\", \"__tls\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 17195064474749569637, "path": 54076421974538766, "deps": [[40386456601120721, "percent_encoding", false, 9596502044904975492], [95042085696191081, "ipnet", false, 682869334136017672], [264090853244900308, "sync_wrapper", false, 10917876855796023541], [784494742817713399, "tower_service", false, 9281592329932465444], [1044435446100926395, "hyper_rustls", false, 6206673473104710472], [1906322745568073236, "pin_project_lite", false, 7404426017592792462], [3150220818285335163, "url", false, 12333196315487844834], [3722963349756955755, "once_cell", false, 18338104825145226077], [4405182208873388884, "http", false, 16515868446764083777], [5986029879202738730, "log", false, 17126298727032183130], [7414427314941361239, "hyper", false, 17529692279597241675], [7620660491849607393, "futures_core", false, 4854277245167358921], [8915503303801890683, "http_body", false, 14276110962147134801], [9538054652646069845, "tokio", false, 15376650133509949099], [9689903380558560274, "serde", false, 3047106530587541600], [10229185211513642314, "mime", false, 3209864126256740605], [10629569228670356391, "futures_util", false, 15535723867050505389], [11295624341523567602, "rustls", false, 5457096175164732441], [13809605890706463735, "h2", false, 16552333541984842553], [14564311161534545801, "encoding_rs", false, 2043433214637187335], [15367738274754116744, "serde_json", false, 14608636572578793853], [16066129441945555748, "bytes", false, 1566315335530503968], [16311359161338405624, "rustls_pemfile", false, 638526290889350645], [16542808166767769916, "serde_urlencoded", false, 3272194804036618258], [16622232390123975175, "tokio_rustls", false, 5541138288150451233], [17652733826348741533, "webpki_roots", false, 4260419089064320315], [18066890886671768183, "base64", false, 3758379949282346791]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-0030080de9ecfd47/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}