# 代理配置文件 - 开发环境配置
# 自动部署时会设置环境变量，无需手动配置

# 服务器配置
frontend_addr: "0.0.0.0:1319"    # 前端管理界面和API服务地址
backend_addr: "0.0.0.0:1911"     # 后端代理服务地址
workers: 2                       # 工作线程数

# JWT配置 - 从环境变量读取（自动设置）
jwt_secret: "${JWT_SECRET}"

# 数据库配置 - 从环境变量读取（自动设置）
mongodb_uri: "${MONGODB_URI}"
redis_uri: "${REDIS_URI:-redis://localhost:6379/0}"

# 管理员账户 - 从环境变量读取（自动设置）
admin:
  username: "${ADMIN_USERNAME:-admin}"
  password_hash: "${ADMIN_PASSWORD_HASH}"

# 强化的安全配置
security:
  tls_min_version: "1.3"        # 仅支持TLS 1.3
  tls_ciphers:                  # 仅允许强密码套件
    - "TLS_AES_256_GCM_SHA384"
    - "TLS_CHACHA20_POLY1305_SHA256"
  cert_file: "${TLS_CERT_PATH:-}"
  key_file: "${TLS_KEY_PATH:-}"
  
  # 会话安全
  session_timeout: 1800         # 30分钟超时
  session_cookie_secure: true   # 仅HTTPS传输Cookie
  session_cookie_httponly: true # 防止XSS访问Cookie
  session_cookie_samesite: "Strict"
  
  # 认证安全 - 简化配置
  max_login_attempts: 5         # 最多5次登录尝试
  lockout_duration: 300         # 5分钟锁定
  password_min_length: 6        # 最小密码长度
  require_strong_passwords: false
  
  # 请求安全
  csrf_protection: true
  xss_protection: true
  content_type_options: true
  frame_options: "DENY"
  referrer_policy: "strict-origin-when-cross-origin"
  
  # HTTPS强制 - 开发环境关闭
  force_https: false
  hsts_enabled: false
  hsts_max_age: 63072000       # 2年HSTS
  hsts_include_subdomains: false
  hsts_preload: false

# 开发环境速率限制 - 较宽松
rate_limit:
  enabled: true
  global_requests_per_minute: 6000   # 全局限制
  per_ip_requests_per_minute: 300000    # 每IP限制
  burst_size: 50                     # 突发限制
  cleanup_interval: 300
  auto_ban_enabled: false            # 开发环境关闭自动封禁
  ban_threshold: 1000                # 封禁阈值
  ban_duration: 300                  # 5分钟封禁

# 安全日志配置 - 统一保存到项目根目录
logging:
  level: "${LOG_LEVEL:-warn}"        # 生产环境warn级别
  format: "json"                     # 结构化日志
  file: "./logs/app.log"             # 主应用日志
  max_size: "100MB"
  max_backups: 30                    # 保留30个备份
  max_age: 90                        # 保留90天
  compress: true
  
  # 简化的日志文件 - 只保留必要的日志
  access_log: "./logs/access.log"        # 访问日志（独立保留）
  
  # 日志安全选项
  sanitize_logs: true                     # 清理敏感信息
  log_ip_addresses: true
  log_user_agents: true

# 监控和告警 - 简化配置
monitoring:
  enabled: false                     # 关闭复杂监控
  health_check_interval: 60

# 输入验证和过滤 - 简化配置
input_validation:
  max_request_size: 10485760         # 10MB最大请求
  block_sql_injection: true
  block_xss_attempts: true

# DDoS防护 - 简化配置
ddos_protection:
  enabled: false                     # 关闭DDoS防护

# 缓存安全配置
cache:
  max_size: 268435456                # 256MB缓存
  default_ttl: 1800                  # 30分钟TTL
  cleanup_interval: 300
  
  # 缓存安全
  cache_private_data: false          # 不缓存私人数据
  respect_cache_headers: true
  vary_on_headers:                   # 基于头部变化缓存
    - "Authorization"
    - "User-Agent"
    - "Accept-Language"

# 反爬虫配置 - 简化配置
anti_crawler:
  enabled: false                     # 关闭反爬虫功能

# 证书管理
certificates:
  auto_renewal: true                 # 自动续期
  renewal_threshold_days: 30         # 30天前开始续期
  backup_certificates: true
  certificate_transparency: true      # 启用CT日志
  
  # OCSP设置
  ocsp_stapling: true
  ocsp_cache_timeout: 3600

# 备份和恢复 - 简化配置
backup:
  enabled: false                     # 关闭自动备份

# 上游服务器配置（空配置，递归功能会自动添加）
upstream_servers: []

# 递归代理配置
recursive_proxy:
  enabled: true              # 启用递归代理
  auto_discovery: true       # 自动发现上游
  max_depth: 3              # 最大递归深度
  timeout_seconds: 30       # 请求超时

# 环境变量要求提醒
# 生产环境部署前必须设置以下环境变量：
# - JWT_SECRET：强随机密钥 (openssl rand -base64 64)
# - MONGODB_URI：包含认证的MongoDB连接串
# - REDIS_URI：包含认证的Redis连接串
# - ADMIN_PASSWORD_HASH：bcrypt哈希的管理员密码
# - TLS_CERT_PATH：TLS证书文件路径
# - TLS_KEY_PATH：TLS私钥文件路径
# - ENVIRONMENT=production：标识生产环境

