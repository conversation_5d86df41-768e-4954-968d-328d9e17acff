# SM智能代理系统 - 统一配置文件
# 使用 proxy-config 统一配置系统

# 服务器配置
server:
  listen: "0.0.0.0:8080"  # 需要包含端口
  workers: 4
  frontend_addr: "0.0.0.0:1319"  # Web管理界面地址
  backend_addr: "127.0.0.1:1911"  # Pingora代理核心地址

# 上游服务器配置
upstreams: []

# 路由配置
routes: []

# 缓存配置 (可选)
cache:
  enabled: true
  max_size: 104857600  # 100MB in bytes
  ttl: 3600

# 安全配置 (可选)
security:
  jwt_secret: "your-jwt-secret-key-must-be-at-least-32-characters-long"
  rate_limit: 100  # requests per minute
