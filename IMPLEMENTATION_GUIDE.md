# SM智能代理系统后端优化实施指南

## 🚀 快速开始

### 环境准备
```bash
# 1. 备份当前代码
git checkout -b optimization-backup
git add .
git commit -m "备份优化前的代码"

# 2. 创建优化分支
git checkout -b backend-optimization

# 3. 安装必要依赖
cargo add dashmap crossbeam tokio-metrics serde_json uuid chrono
```

### 验证工具安装
```bash
# 安装性能分析工具
cargo install cargo-flamegraph
cargo install cargo-profdata

# 安装代码质量工具
cargo install cargo-audit
cargo install cargo-outdated
```

---

## 📋 阶段1: 立即清理 (第1天)

### 1.1 删除ssl_manager模块

**执行脚本**:
```bash
#!/bin/bash
# cleanup_ssl_manager.sh

echo "🔍 查找ssl_manager引用..."
grep -r "ssl_manager" src/ --include="*.rs" > ssl_manager_refs.txt

echo "📝 引用列表:"
cat ssl_manager_refs.txt

echo "🔄 替换引用..."
# 替换所有ssl_manager为ssl
find src/ -name "*.rs" -exec sed -i 's/use.*ssl_manager/use crate::ssl/g' {} \;
find src/ -name "*.rs" -exec sed -i 's/ssl_manager::/ssl::/g' {} \;

echo "🗑️ 删除ssl_manager目录..."
rm -rf src/ssl_manager/

echo "✅ 验证编译..."
cargo check

if [ $? -eq 0 ]; then
    echo "✅ ssl_manager模块清理完成!"
else
    echo "❌ 编译失败，请检查引用替换"
    exit 1
fi
```

**验证清单**:
- [ ] src/ssl_manager/ 目录已删除
- [ ] 所有ssl_manager引用已更新
- [ ] cargo check 通过
- [ ] SSL功能测试通过

### 1.2 分析域名模块重复

**分析脚本**:
```bash
#!/bin/bash
# analyze_domain_modules.sh

echo "📊 分析域名模块重复度..."

# 比较extractor.rs文件
echo "🔍 比较extractor.rs文件:"
diff -u src/domains/extractor.rs src/domain_pool/extractor.rs > extractor_diff.txt
diff -u src/domains/extractor.rs src/domain_mapping/extractor.rs >> extractor_diff.txt

# 比较replacer.rs文件
echo "🔍 比较replacer.rs文件:"
diff -u src/domains/replacer.rs src/domain_pool/replacer.rs > replacer_diff.txt
diff -u src/domains/replacer.rs src/domain_mapping/replacer.rs >> replacer_diff.txt

# 统计重复行数
echo "📈 重复度统计:"
echo "extractor.rs重复行数: $(wc -l < extractor_diff.txt)"
echo "replacer.rs重复行数: $(wc -l < replacer_diff.txt)"

# 生成合并计划
echo "📋 生成合并计划..."
cat > domain_merge_plan.md << 'EOF'
# 域名模块合并计划

## 目标结构
```
src/domains/
├── mod.rs              # 统一导出
├── models.rs           # 合并所有数据模型
├── service.rs          # 统一业务逻辑
├── repository.rs       # 统一数据访问
├── extractor.rs        # 统一域名提取
├── replacer.rs         # 统一内容替换
├── mapping.rs          # 域名映射功能
└── pool.rs            # 域名池管理功能
```

## 迁移步骤
1. 创建新的统一结构
2. 迁移domain_pool独有功能到pool.rs
3. 迁移domain_mapping独有功能到mapping.rs
4. 更新所有引用
5. 删除重复目录
EOF

echo "✅ 分析完成，查看 domain_merge_plan.md"
```

---

## 📋 阶段2: 域名模块合并 (第2-4天)

### 2.1 创建统一域名服务

**实施代码**:
```rust
// src/domains/unified_service.rs
use std::sync::Arc;
use dashmap::DashMap;
use tokio::sync::RwLock;
use crate::error::Result;

#[derive(Clone)]
pub struct UnifiedDomainService {
    // 核心数据存储
    mappings: Arc<DashMap<String, DomainMapping>>,
    pools: Arc<DashMap<String, DomainPool>>,
    
    // 功能组件
    extractor: Arc<DomainExtractor>,
    replacer: Arc<ContentReplacer>,
    repository: Arc<dyn DomainRepository>,
    
    // 配置
    config: DomainConfig,
}

impl UnifiedDomainService {
    pub fn new(repository: Arc<dyn DomainRepository>, config: DomainConfig) -> Self {
        Self {
            mappings: Arc::new(DashMap::new()),
            pools: Arc::new(DashMap::new()),
            extractor: Arc::new(DomainExtractor::new(config.clone())),
            replacer: Arc::new(ContentReplacer::new(config.clone())),
            repository,
            config,
        }
    }

    // 统一的域名提取接口
    pub async fn extract_domains(&self, content: &str, content_type: &str) -> Result<Vec<String>> {
        self.extractor.extract_by_content_type(content, content_type).await
    }

    // 统一的内容替换接口
    pub async fn replace_content(&self, content: &str, mappings: &[DomainMapping]) -> Result<String> {
        self.replacer.replace_with_mappings(content, mappings).await
    }

    // 域名映射管理
    pub async fn create_mapping(&self, upstream: &str, downstream: &str) -> Result<DomainMapping> {
        let mapping = DomainMapping {
            id: uuid::Uuid::new_v4().to_string(),
            upstream_domain: upstream.to_string(),
            downstream_domain: downstream.to_string(),
            created_at: chrono::Utc::now(),
            is_active: true,
            mapping_type: MappingType::Manual,
        };

        // 保存到数据库
        self.repository.save_mapping(&mapping).await?;
        
        // 更新内存缓存
        self.mappings.insert(upstream.to_string(), mapping.clone());
        
        // 更新内容替换器
        self.replacer.add_mapping(upstream, downstream).await?;
        
        Ok(mapping)
    }

    // 域名池管理
    pub async fn add_to_pool(&self, pool_name: &str, domains: Vec<String>) -> Result<()> {
        let mut pool = self.pools.entry(pool_name.to_string())
            .or_insert_with(|| DomainPool::new(pool_name));
        
        for domain in domains {
            pool.add_domain(domain).await?;
        }
        
        // 持久化到数据库
        self.repository.save_pool(&pool).await?;
        
        Ok(())
    }

    // 批量域名映射
    pub async fn create_batch_mappings(&self, pairs: Vec<(String, String)>) -> Result<Vec<DomainMapping>> {
        let mut mappings = Vec::new();
        
        for (upstream, downstream) in pairs {
            match self.create_mapping(&upstream, &downstream).await {
                Ok(mapping) => mappings.push(mapping),
                Err(e) => {
                    log::warn!("创建映射失败 {} -> {}: {}", upstream, downstream, e);
                }
            }
        }
        
        Ok(mappings)
    }
}
```

### 2.2 迁移脚本

**自动迁移脚本**:
```bash
#!/bin/bash
# migrate_domain_modules.sh

echo "🚀 开始域名模块迁移..."

# 1. 创建新的统一结构
mkdir -p src/domains/temp_migration

# 2. 复制并合并extractor.rs
echo "📝 合并extractor.rs..."
cat > src/domains/temp_migration/extractor.rs << 'EOF'
// 统一的域名提取器
use std::collections::HashSet;
use regex::Regex;
use scraper::{Html, Selector};
use crate::error::Result;

pub struct DomainExtractor {
    html_selector: Selector,
    css_regex: Regex,
    url_regex: Regex,
}

impl DomainExtractor {
    pub fn new(config: DomainConfig) -> Self {
        Self {
            html_selector: Selector::parse("a[href], img[src], link[href], script[src]").unwrap(),
            css_regex: Regex::new(r"url\(['\"]?([^'\"]+)['\"]?\)").unwrap(),
            url_regex: Regex::new(r"https?://([^/\s]+)").unwrap(),
        }
    }

    pub async fn extract_by_content_type(&self, content: &str, content_type: &str) -> Result<Vec<String>> {
        match content_type {
            "text/html" => self.extract_from_html(content).await,
            "text/css" => self.extract_from_css(content).await,
            "application/json" => self.extract_from_json(content).await,
            _ => self.extract_from_text(content).await,
        }
    }

    async fn extract_from_html(&self, html: &str) -> Result<Vec<String>> {
        let document = Html::parse_document(html);
        let mut domains = HashSet::new();

        for element in document.select(&self.html_selector) {
            if let Some(url) = element.value().attr("href").or_else(|| element.value().attr("src")) {
                if let Some(domain) = self.extract_domain_from_url(url) {
                    domains.insert(domain);
                }
            }
        }

        Ok(domains.into_iter().collect())
    }

    async fn extract_from_css(&self, css: &str) -> Result<Vec<String>> {
        let mut domains = HashSet::new();

        for cap in self.css_regex.captures_iter(css) {
            if let Some(url) = cap.get(1) {
                if let Some(domain) = self.extract_domain_from_url(url.as_str()) {
                    domains.insert(domain);
                }
            }
        }

        Ok(domains.into_iter().collect())
    }

    fn extract_domain_from_url(&self, url: &str) -> Option<String> {
        self.url_regex.captures(url)
            .and_then(|cap| cap.get(1))
            .map(|m| m.as_str().to_string())
    }
}
EOF

# 3. 合并replacer.rs
echo "📝 合并replacer.rs..."
# ... (类似的合并逻辑)

# 4. 更新引用
echo "🔄 更新模块引用..."
find src/ -name "*.rs" -exec sed -i 's/use.*domain_pool::/use crate::domains::/g' {} \;
find src/ -name "*.rs" -exec sed -i 's/use.*domain_mapping::/use crate::domains::/g' {} \;

# 5. 验证编译
echo "✅ 验证编译..."
cargo check

if [ $? -eq 0 ]; then
    echo "✅ 迁移成功，删除旧模块..."
    rm -rf src/domain_pool/
    rm -rf src/domain_mapping/
    mv src/domains/temp_migration/* src/domains/
    rmdir src/domains/temp_migration
    echo "🎉 域名模块合并完成!"
else
    echo "❌ 编译失败，回滚更改..."
    git checkout -- src/
    exit 1
fi
```

---

## 📋 阶段3: 事件系统优化 (第5-7天)

### 3.1 实现无锁事件系统

**核心实现**:
```rust
// src/events/lockfree_system.rs
use crossbeam::queue::SegQueue;
use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::VecDeque;
use std::sync::atomic::{AtomicUsize, AtomicU64, Ordering};

pub struct LockFreeEventSystem {
    // 写入队列 - 无锁
    write_queue: Arc<SegQueue<CompactEventRecord>>,
    
    // 读取缓冲区 - 读写锁保护
    read_buffer: Arc<RwLock<VecDeque<CompactEventRecord>>>,
    
    // 字符串池
    string_pool: Arc<StringPool>,
    
    // 统计信息 - 原子操作
    total_events: AtomicU64,
    queue_length: AtomicUsize,
    
    // 配置
    config: EventConfig,
}

impl LockFreeEventSystem {
    pub fn new(config: EventConfig) -> Self {
        let system = Self {
            write_queue: Arc::new(SegQueue::new()),
            read_buffer: Arc::new(RwLock::new(VecDeque::new())),
            string_pool: Arc::new(StringPool::new()),
            total_events: AtomicU64::new(0),
            queue_length: AtomicUsize::new(0),
            config,
        };
        
        // 启动后台处理任务
        system.start_background_processor();
        system
    }

    // 无锁写入 - 高性能
    pub fn add_event(&self, event: EventRecord) {
        let compact_event = self.compress_event(event);
        self.write_queue.push(compact_event);
        self.queue_length.fetch_add(1, Ordering::Relaxed);
        self.total_events.fetch_add(1, Ordering::Relaxed);
    }

    // 高效查询
    pub async fn get_recent_events(&self, limit: usize) -> Vec<EventRecord> {
        let buffer = self.read_buffer.read().await;
        buffer.iter()
            .rev()
            .take(limit)
            .map(|e| self.decompress_event(e))
            .collect()
    }

    // 后台处理任务
    fn start_background_processor(&self) {
        let write_queue = self.write_queue.clone();
        let read_buffer = self.read_buffer.clone();
        let queue_length = self.queue_length.clone();
        let max_buffer_size = self.config.max_buffer_size;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_millis(50));
            loop {
                interval.tick().await;
                
                // 批量处理写队列
                let mut batch = Vec::new();
                let mut processed = 0;
                
                while let Some(event) = write_queue.pop() {
                    batch.push(event);
                    processed += 1;
                    if batch.len() >= 100 { break; } // 批量大小限制
                }

                if !batch.is_empty() {
                    let mut buffer = read_buffer.write().await;
                    for event in batch {
                        buffer.push_back(event);
                    }
                    
                    // 保持缓冲区大小
                    while buffer.len() > max_buffer_size {
                        buffer.pop_front();
                    }
                    
                    queue_length.fetch_sub(processed, Ordering::Relaxed);
                }
            }
        });
    }

    // 事件压缩 - 减少内存使用
    fn compress_event(&self, event: EventRecord) -> CompactEventRecord {
        CompactEventRecord {
            id: event.id.parse().unwrap_or_else(|| {
                use std::collections::hash_map::DefaultHasher;
                use std::hash::{Hash, Hasher};
                let mut hasher = DefaultHasher::new();
                event.id.hash(&mut hasher);
                hasher.finish()
            }),
            event_type: event.event_type as u8,
            user_id: event.user_id.and_then(|s| s.parse().ok()),
            domain_id: event.domain.map(|s| self.string_pool.intern(&s)),
            ip_address: event.ip_address.and_then(|s| s.parse::<std::net::Ipv4Addr>().ok().map(|ip| ip.into())),
            message_id: self.string_pool.intern(&event.message),
            timestamp: event.timestamp.timestamp() as u64,
            severity: event.severity as u8,
        }
    }

    // 事件解压缩
    fn decompress_event(&self, compact: &CompactEventRecord) -> EventRecord {
        EventRecord {
            id: compact.id.to_string(),
            event_type: EventType::from(compact.event_type),
            user_id: compact.user_id.map(|id| id.to_string()),
            domain: compact.domain_id.and_then(|id| self.string_pool.get(id)),
            ip_address: compact.ip_address.map(|ip| std::net::Ipv4Addr::from(ip).to_string()),
            message: self.string_pool.get(compact.message_id).unwrap_or_default(),
            timestamp: chrono::DateTime::from_timestamp(compact.timestamp as i64, 0).unwrap_or_default(),
            severity: EventSeverity::from(compact.severity),
            details: None, // 压缩版本不保存details
        }
    }
}

// 紧凑的事件记录 - 大幅减少内存使用
#[derive(Debug, Clone)]
pub struct CompactEventRecord {
    pub id: u64,                    // 8字节
    pub event_type: u8,             // 1字节
    pub user_id: Option<u32>,       // 4字节
    pub domain_id: Option<u32>,     // 4字节
    pub ip_address: Option<u32>,    // 4字节
    pub message_id: u32,            // 4字节
    pub timestamp: u64,             // 8字节
    pub severity: u8,               // 1字节
    // 总计: 34字节 vs 原来的200-500字节
}
```

### 3.2 性能测试脚本

**基准测试**:
```rust
// tests/performance/event_system_benchmark.rs
use criterion::{black_box, criterion_group, criterion_main, Criterion};
use std::sync::Arc;
use tokio::runtime::Runtime;

fn benchmark_event_systems(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    let old_system = Arc::new(OldEventSystem::new());
    let new_system = Arc::new(LockFreeEventSystem::new(EventConfig::default()));
    
    c.bench_function("old_event_system_write", |b| {
        b.iter(|| {
            rt.block_on(async {
                for i in 0..1000 {
                    let event = create_test_event(i);
                    old_system.add_event(event).await;
                }
            })
        })
    });
    
    c.bench_function("new_event_system_write", |b| {
        b.iter(|| {
            for i in 0..1000 {
                let event = create_test_event(i);
                new_system.add_event(event);
            }
        })
    });
    
    c.bench_function("old_event_system_read", |b| {
        b.iter(|| {
            rt.block_on(async {
                let events = old_system.get_recent_events(100).await;
                black_box(events);
            })
        })
    });
    
    c.bench_function("new_event_system_read", |b| {
        b.iter(|| {
            rt.block_on(async {
                let events = new_system.get_recent_events(100).await;
                black_box(events);
            })
        })
    });
}

fn create_test_event(id: usize) -> EventRecord {
    EventRecord {
        id: id.to_string(),
        event_type: EventType::DomainAccess,
        user_id: Some(format!("user_{}", id % 100)),
        domain: Some(format!("domain_{}.com", id % 50)),
        ip_address: Some(format!("192.168.1.{}", id % 255)),
        message: format!("Test event {}", id),
        timestamp: chrono::Utc::now(),
        severity: EventSeverity::Info,
        details: None,
    }
}

criterion_group!(benches, benchmark_event_systems);
criterion_main!(benches);
```

---

## 📊 验证和监控

### 性能验证脚本
```bash
#!/bin/bash
# performance_validation.sh

echo "🚀 开始性能验证..."

# 1. 编译优化版本
cargo build --release

# 2. 运行基准测试
echo "📊 运行基准测试..."
cargo bench

# 3. 内存使用测试
echo "💾 内存使用测试..."
valgrind --tool=massif --pages-as-heap=yes target/release/sm &
PID=$!
sleep 30
kill $PID

# 4. 并发测试
echo "🔄 并发测试..."
cargo test --release -- --test-threads=1 performance

# 5. 生成报告
echo "📋 生成性能报告..."
cat > performance_report.md << EOF
# 性能验证报告

## 基准测试结果
$(cat target/criterion/*/report/index.html | grep -A 5 "Performance")

## 内存使用
$(ls -la massif.out.*)

## 并发测试
$(cargo test --release -- --nocapture performance 2>&1)

## 结论
- 事件系统性能提升: XX%
- 内存使用减少: XX%
- 并发处理能力提升: XX%
EOF

echo "✅ 性能验证完成，查看 performance_report.md"
```

这个实施指南提供了详细的步骤和脚本，可以帮助开发团队按照计划逐步实施后端优化工作。
