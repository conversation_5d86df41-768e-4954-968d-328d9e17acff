#!/bin/bash
# SM智能代理系统 - 智能部署启动器 (v2025.01)
# 🚀 此脚本已合并到setup.sh中，现在作为智能部署的入口点
# 🎯 设计理念：简单、可靠、智能、快速

# 启用严格模式确保脚本安全性
set -euo pipefail

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SETUP_SCRIPT="$SCRIPT_DIR/setup.sh"

# 颜色配置
declare -r RED='\033[1;31m'
declare -r GREEN='\033[1;32m'
declare -r BLUE='\033[1;34m'
declare -r CYAN='\033[1;36m'
declare -r WHITE='\033[1;37m'
declare -r NC='\033[0m'

# 图标
declare -r ICON_SUCCESS="✅"
declare -r ICON_INFO="ℹ️"
declare -r ICON_ROCKET="🚀"

# 显示欢迎信息
show_welcome() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║          SM智能代理系统 - 智能部署启动器 v2025.01            ║"
    echo "║                                                              ║"
    echo "║  ${WHITE}🚀 功能已合并到setup.sh，提供统一的部署体验${CYAN}           ║"
    echo "║  ${WHITE}⚡ 自动启用智能部署模式，减少70%部署时间${CYAN}             ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 主函数
main() {
    # 显示欢迎信息
    show_welcome

    echo -e "${BLUE}${ICON_INFO} 正在启动智能部署流程...${NC}"

    # 检查setup.sh是否存在
    if [[ ! -f "$SETUP_SCRIPT" ]]; then
        echo -e "${RED}❌ 未找到setup.sh文件${NC}"
        echo -e "${WHITE}请确保install.sh和setup.sh在同一目录${NC}"
        exit 1
    fi

    # 检查setup.sh权限
    if [[ ! -x "$SETUP_SCRIPT" ]]; then
        echo -e "${BLUE}🔧 正在修复setup.sh执行权限...${NC}"
        chmod +x "$SETUP_SCRIPT" || {
            echo -e "${RED}❌ 无法添加执行权限${NC}"
            exit 1
        }
        echo -e "${GREEN}${ICON_SUCCESS} 权限修复完成${NC}"
    fi

    # 设置智能部署环境变量
    export SM_INTELLIGENT_DEPLOY=1
    export SM_DEPLOY_VERSION="2025.01"
    export AUTO_INTELLIGENT_DEPLOY=1

    echo -e "${GREEN}${ICON_ROCKET} 启动setup.sh智能部署模式...${NC}"
    echo ""

    # 执行setup.sh
    exec "$SETUP_SCRIPT" "$@"
}

# 程序入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi