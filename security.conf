﻿# 安全防护配置文件
# JWT 密钥安全性设置
JWT_SECRET_AUTO_GENERATE=true
JWT_SECRET_MIN_LENGTH=64
JWT_SECRET_ROTATION_ON_PASSWORD_CHANGE=true

# 文件系统保护
SECURE_FILE_PERMISSIONS=true
PROTECT_ENV_FILE=true
RESTRICT_STATIC_ACCESS=true

# 速率限制设置
LOGIN_RATE_LIMIT=5
LOGIN_RATE_WINDOW=300
LOGIN_BLOCK_DURATION=900

# 威胁检测
ENABLE_SQL_INJECTION_DETECTION=true
ENABLE_XSS_DETECTION=true
ENABLE_PATH_TRAVERSAL_DETECTION=true
ENABLE_COMMAND_INJECTION_DETECTION=true

# IP 封禁设置
ENABLE_AUTO_BAN=true
THREAT_SCORE_THRESHOLD=50
BAN_DURATION_HOURS=24

# 日志和监控
SECURITY_LOG_LEVEL=warn
ENABLE_SECURITY_MONITORING=true
SECURITY_EVENT_RETENTION_DAYS=7

# HTTP 安全头
ENABLE_SECURITY_HEADERS=true
ENABLE_CSP=true
ENABLE_HSTS=true
FRAME_OPTIONS=DENY

# 静态文件保护
FORBIDDEN_FILE_PATTERNS=.env,.env.*,*.key,*.pem,*.p12,cargo.toml,cargo.lock,*.backup,*.bak,*.tmp,*.log
ALLOWED_FILE_EXTENSIONS=html,css,js,png,jpg,jpeg,gif,svg,ico,woff,woff2,ttf,eot,json,xml,txt

# 目录保护
FORBIDDEN_DIRECTORIES=src/,target/,crates/,config/,docs/,.git/

# 会话安全
SESSION_TIMEOUT_MINUTES=60
SESSION_ROTATE_ON_AUTH=true
SECURE_COOKIES=true
